import { IAlarmsTargetsTypes } from '@/@core/domain/AlarmsTargetsTypes'
import { Response } from '@/@core/infra/api/AlarmsTargetsTypesApiV4/AlarmsTargetsTypesApiV4.types'

/** Response */
export const alarmsTargetsTypesResponseMock1: Response = {
  id: 2,
  dataEntity: {
    id: 1,
    name: 'Equipment'
  },
  name: 'Equipamentos',
  slug: 'equipments'
}
export const alarmsTargetsTypesResponseMock2: Response = {
  id: 3,
  dataEntity: {
    id: 2,
    name: 'Company'
  },
  name: 'Empresas',
  slug: 'companies'
}

/** Response parsed domain  */
export const alarmsTargetsTypesMock1: IAlarmsTargetsTypes = {
  id: 2,
  dataEntity: {
    id: 1,
    name: 'Equipment'
  },
  name: 'Equipamentos',
  slug: 'equipments'
}
export const alarmsTargetsTypesMock2: IAlarmsTargetsTypes = {
  id: 3,
  dataEntity: {
    id: 2,
    name: 'Company'
  },
  name: 'Emp<PERSON><PERSON>',
  slug: 'companies'
}
