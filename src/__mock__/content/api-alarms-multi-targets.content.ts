import { IAlarmsMultiTargets } from '@/@core/domain/AlarmsMultiTargets'
import { Response } from '@/@core/infra/api/AlarmsMultiTargetsApiV4/AlarmsMultiTargetsApiV4.types'

/** Response */
export const alarmsMultiTargetsResponseMock1: Response = {
  alarm_id: 144,
  companies: [],
  equipments: [],
  targets: [
    {
      id: 2,
      name: 'Energia exceto virtual',
      targetType: {
        id: 1,
        name: 'Template',
        slug: 'template',
        dataEntity: {
          id: 1,
          name: 'Equipment'
        }
      }
    }
  ]
}
export const alarmsMultiTargetsResponseMock2: Response = {
  alarm_id: 145,
  companies: [{ id: 1, name: 'companies 1' }],
  equipments: [{ id: 1, name: 'equipments 1' }],
  targets: [
    {
      id: 2,
      name: 'Energia exceto virtual',
      targetType: {
        id: 1,
        name: 'Template',
        slug: 'template',
        dataEntity: {
          id: 1,
          name: 'Equipment'
        }
      }
    }
  ]
}

/** Response parsed domain  */
export const alarmsMultiTargetsMock1: IAlarmsMultiTargets = {
  alarmId: 144,
  companies: [],
  equipments: [],
  targets: [
    {
      id: 2,
      name: 'Energia exceto virtual',
      targetType: {
        id: 1,
        name: 'Template',
        slug: 'template',
        dataEntity: {
          id: 1,
          name: 'Equipment'
        }
      }
    }
  ]
}
export const alarmsMultiTargetsMock2: IAlarmsMultiTargets = {
  alarmId: 145,
  companies: [{ id: 1, name: 'companies 1' }],
  equipments: [{ id: 1, name: 'equipments 1' }],
  targets: [
    {
      id: 2,
      name: 'Energia exceto virtual',
      targetType: {
        id: 1,
        name: 'Template',
        slug: 'template',
        dataEntity: {
          id: 1,
          name: 'Equipment'
        }
      }
    }
  ]
}
