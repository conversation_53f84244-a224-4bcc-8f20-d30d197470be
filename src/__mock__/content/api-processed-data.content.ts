import { IProcessedData } from '@/@core/domain/ProcessedData'
import { Response } from '@/@core/infra/api/ProcessedDataApiV3/ProcessedDataApiV3.types'

/** Response */
export const processedDataResponseMock1: Response = {
  id: 7,
  name: 'Ativação instantânea de dispositivo',
  collection: 'equipamento_atividade'
}
export const processedDataResponseMock2: Response = {
  id: 8,
  name: 'Ativação mensal de dispositivo',
  collection: 'equipamento_atividade_mensal'
}

/** Response parsed domain  */
export const processedDataMock1: IProcessedData = {
  id: 7,
  name: 'Ativação instantânea de dispositivo',
  collection: 'equipamento_atividade'
}
export const processedDataMock2: IProcessedData = {
  id: 8,
  name: 'Ativação mensal de dispositivo',
  collection: 'equipamento_atividade_mensal'
}
