import { IAlarmsOperatorLogic } from '@/@core/domain/AlarmsOperatorLogic'
import { Response } from '@/@core/infra/api/AlarmsOperatorLogicApiV4/AlarmsOperatorLogicApiV4.types'

/** Response */
export const alarmsOperatorLogicResponseMock1: Response = {
  id: 1,
  name: '<PERSON><PERSON>',
  operator: '>'
}
export const alarmsOperatorLogicResponseMock2: Response = {
  id: 2,
  name: '<PERSON><PERSON> ou Igual',
  operator: '>='
}

/** Response parsed domain  */
export const alarmsOperatorLogicMock1: IAlarmsOperatorLogic = {
  id: 1,
  name: '<PERSON><PERSON>',
  operator: '>'
}

export const alarmsOperatorLogicMock2: IAlarmsOperatorLogic = {
  id: 2,
  name: '<PERSON><PERSON> ou Igual',
  operator: '>='
}
