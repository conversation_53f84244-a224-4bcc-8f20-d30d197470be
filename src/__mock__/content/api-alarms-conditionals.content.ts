import { IAlarmsConditionals } from '@/@core/domain/AlarmsConditionals'
import { Response } from '@/@core/infra/api/AlarmsConditionalsApiV4/AlarmsConditionalsApiV4.types'

/** Response */
export const alarmsConditionalsResponseMock1: Response = {
  id: 320,
  alarm_stage_id: 1,
  value: '44',
  alarm: {
    id: 144,
    name: '0144 - Alarm test front'
  },
  rule: {
    id: 7,
    name: 'Medição Instantânea'
  },
  operator: {
    id: 3,
    name: '<PERSON><PERSON>'
  },
  property: {
    id: 11,
    name: 'property 11'
  },
  processed: {
    id: 8,
    name: 'Ativação mensal de dispositivo',
    auto_normalized: false
  }
}
export const alarmsConditionalsResponseMock2: Response = {
  id: 321,
  alarm_stage_id: 2,
  value: '44',
  alarm: {
    id: 144,
    name: '0144 - Alarm test front'
  },
  rule: {
    id: 6,
    name: 'Medi<PERSON> horária'
  },
  operator: {
    id: 4,
    name: '<PERSON><PERSON> ou Igual'
  },
  property: null,
  processed: null
}

/** Response parsed domain  */
export const alarmsConditionalsMock1: IAlarmsConditionals = {
  id: 320,
  alarmStageId: 1,
  value: '44',
  alarm: {
    id: 144,
    name: '0144 - <PERSON>arm test front'
  },
  rule: {
    id: 7,
    name: 'Medição Instantânea'
  },
  operator: {
    id: 3,
    name: 'Menor'
  },
  property: {
    id: 11,
    name: 'property 11'
  },
  processed: {
    id: 8,
    name: 'Ativação mensal de dispositivo',
    autoNormalized: false
  }
}
export const alarmsConditionalsMock2: IAlarmsConditionals = {
  id: 321,
  alarmStageId: 2,
  value: '44',
  alarm: {
    id: 144,
    name: '0144 - Alarm test front'
  },
  rule: {
    id: 6,
    name: 'Medição horária'
  },
  operator: {
    id: 4,
    name: 'Menor ou Igual'
  },
  property: null,
  processed: null
}
