import { IEquipmentVirtual } from '@/@core/domain/EquipmentVirtual'
import { Response } from '@/@core/infra/api/EquipmentVirtualApiV4/EquipmentVirtualApiV4.types'

/** Response */
export const equipmentVirtualResponseMock1: Response = {
  id: 1,
  equipment_virtual: {
    id: 1,
    name: 'equipment_virtual_1'
  },
  equipment_composition: {
    id: 1,
    name: 'equipment_composition_1'
  },
  rule: 'soma'
}

export const equipmentVirtualResponseMock2: Response = {
  id: 2,
  equipment_virtual: {
    id: 2,
    name: 'equipment_virtual_2'
  },
  equipment_composition: {
    id: 2,
    name: 'equipment_composition_2'
  },
  rule: 'soma'
}

export const equipmentVirtualMock1: IEquipmentVirtual = {
  id: 1,
  equipmentVirtual: {
    id: 1,
    name: 'equipment_virtual_1'
  },
  equipmentComposition: {
    id: 1,
    name: 'equipment_composition_1'
  },
  rule: 'soma'
}

export const equipmentVirtualMock2: IEquipmentVirtual = {
  id: 2,
  equipmentVirtual: {
    id: 2,
    name: 'equipment_virtual_2'
  },
  equipmentComposition: {
    id: 2,
    name: 'equipment_composition_2'
  },
  rule: 'soma'
}
