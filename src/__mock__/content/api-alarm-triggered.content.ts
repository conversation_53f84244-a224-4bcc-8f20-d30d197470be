import { IAlarmTriggered } from '@/@core/domain/AlarmTriggered'

export const alarmTriggeredMock1: IAlarmTriggered = {
  id: 1,
  alarmId: 10,
  alarmName: 'Alarme de Teste',
  companyId: 100,
  companyName: 'Empresa Exemplo',
  equipmentId: 200,
  equipmentName: 'Equipamento Exemplo',
  triggeredAt: '2024-06-24T12:00:00Z',
  triggeredValues: 'valor1',
  normalizedAt: '2024-06-24T13:00:00Z',
  normalizedValues: 'valor2',
  status: 'alarm',
  actionSupport: {
    deviceStatusAfterTrigger: 1,
    deviceStatusAfterNormalize: 0
  }
}

export const alarmTriggeredMock2: IAlarmTriggered = {
  id: 44,
  alarmId: 10,
  alarmName: 'Alarme de Teste',
  companyId: 100,
  companyName: 'Empresa Exemplo',
  equipmentId: 200,
  equipmentName: 'Equipamento Exemplo',
  triggeredAt: '2024-06-24T12:00:00Z',
  triggeredValues: 'valor1',
  normalizedAt: '2024-06-24T13:00:00Z',
  normalizedValues: 'valor2',
  status: 'alarm',
  actionSupport: {
    deviceStatusAfterTrigger: 1,
    deviceStatusAfterNormalize: 0
  }
}
