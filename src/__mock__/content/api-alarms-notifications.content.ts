import IAlarmsNotifications from '@/@core/domain/AlarmsNotifications'
import { AlarmsNotificationsResponse } from '@/@core/infra/api/AlarmsNotificationsApiV4/AlarmsNotificationsApiV4.types'

export const alarmsNotificationsResponseMock: AlarmsNotificationsResponse = {
  id: 1,
  alarm: {
    id: 1,
    name: 'Equipamento Desconectado'
  },
  channel: {
    id: 1,
    name: 'E-mail'
  },
  frequency: {
    id: 1,
    name: 'realtime'
  },
  user: {
    id: 1,
    name: 'Name'
  },
  configs: ['<EMAIL>', '<EMAIL>']
}

export const alarmsNotificationsMock1: IAlarmsNotifications = {
  id: 1,
  alarm: {
    id: 1,
    name: 'Equipamento Desconectado'
  },
  alarmId: 1,
  alarmName: 'Equipamento Desconectado',
  channel: {
    id: 1,
    name: 'E-mail'
  },
  channelId: 1,
  channelName: 'E-mail',
  frequency: {
    id: 1,
    name: 'realtime'
  },
  frequencyId: 1,
  frequencyName: 'realtime',
  user: {
    id: 1,
    name: 'Name'
  },
  userId: 1,
  userName: 'Name',
  configs: ['<EMAIL>', '<EMAIL>']
}

export const alarmsNotificationsMock2: IAlarmsNotifications = {
  id: 1,
  alarm: {
    id: 1,
    name: 'Equipamento Desconectado'
  },
  alarmId: 1,
  alarmName: 'Equipamento Desconectado',
  channel: {
    id: 1,
    name: 'E-mail'
  },
  channelId: 1,
  channelName: 'E-mail',
  frequency: {
    id: 1,
    name: 'realtime'
  },
  frequencyId: 1,
  frequencyName: 'realtime',
  user: {
    id: 1,
    name: 'Name'
  },
  userId: 1,
  userName: 'Name',
  configs: ['<EMAIL>', '<EMAIL>']
}

export const alarmsNotificationsMock3: IAlarmsNotifications = {
  alarm: {
    id: 1,
    name: 'Equipamento Desconectado'
  },
  alarmId: 1,
  alarmName: 'Equipamento Desconectado',
  channel: {
    id: 1,
    name: 'E-mail'
  },
  channelId: 1,
  channelName: 'E-mail',
  configs: ['<EMAIL>', '<EMAIL>'],
  frequency: {
    id: 1,
    name: 'realtime'
  },
  frequencyId: 1,
  frequencyName: 'realtime',
  id: 1,
  user: {
    id: 1,
    name: 'Name'
  },
  userId: 1,
  userName: 'Name'
}
