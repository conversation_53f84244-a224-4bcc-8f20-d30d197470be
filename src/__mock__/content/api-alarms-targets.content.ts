import { IAlarmsTargets } from '@/@core/domain/AlarmsTargets'
import { Response } from '@/@core/infra/api/AlarmsTargetsApiV4/AlarmsTargetsApiV4.types'

/** Response */
export const alarmsTargetsResponseMock1: Response = {
  id: 5,
  targetType: { id: 1, name: 'Template' },
  name: 'água exceto virtual',
  description: 'Todos equipamento de água com exceção do subtipo virtual',
  dynamic_entities: true,
  status: true
}
export const alarmsTargetsResponseMock2: Response = {
  id: 2,
  targetType: { id: 1, name: 'Template' },
  name: 'Energia exceto virtual',
  description: 'Todos equipamento de energia com exceção dos subtipos virtual',
  dynamic_entities: true,
  status: true
}

/** Response parsed domain  */
export const alarmsTargetsMock1: IAlarmsTargets = {
  id: 5,
  targetType: { id: 1, name: 'Template' },
  name: 'água exceto virtual',
  description: 'Todos equipamento de água com exceção do subtipo virtual',
  dynamicEntities: true,
  status: true
}
export const alarmsTargetsMock2: IAlarmsTargets = {
  id: 2,
  targetType: { id: 1, name: 'Template' },
  name: 'Energia exceto virtual',
  description: 'Todos equipamento de energia com exceção dos subtipos virtual',
  dynamicEntities: true,
  status: true
}
