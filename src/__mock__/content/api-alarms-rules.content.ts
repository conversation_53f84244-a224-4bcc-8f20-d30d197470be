import { IAlarmsRules } from '@/@core/domain/AlarmsRules'
import { Response } from '@/@core/infra/api/AlarmsRulesApiV4/AlarmsRulesApiV4.types'

/** Response */
export const alarmsRulesResponseMock1: Response = {
  id: 1,
  alarms_rules_type_id: 1,
  rule_type: {
    required_property: false,
    required_processed: false
  },
  name: 'Equipamento desconectado',
  description:
    'Regra que verifica se o equipamento está desconectado. Retorno: boleano.',
  backend_rules: 'required|bool',
  aggregate: 'first',
  status: true
}
export const alarmsRulesResponseMock2: Response = {
  id: 5,
  alarms_rules_type_id: 4,
  rule_type: {
    required_property: false,
    required_processed: true
  },
  name: 'Medição diária',
  description:
    'Esta regra soma os valores enviados e processados no dia atual.Retorno: Inteiro ou float.',
  backend_rules: 'required',
  aggregate: 'sum',
  status: true
}

/** Response parsed domain  */
export const alarmsRulesMock1: IAlarmsRules = {
  id: 1,
  alarmsRulesTypeId: 1,
  ruleType: {
    requiredProperty: false,
    requiredProcessed: false
  },
  name: 'Equipamento desconectado',
  description:
    'Regra que verifica se o equipamento está desconectado. Retorno: boleano.',
  backendRules: 'required|bool',
  aggregate: 'first',
  status: true
}

export const alarmsRulesMock2: IAlarmsRules = {
  id: 5,
  alarmsRulesTypeId: 4,
  ruleType: {
    requiredProperty: false,
    requiredProcessed: true
  },
  name: 'Medição diária',
  description:
    'Esta regra soma os valores enviados e processados no dia atual.Retorno: Inteiro ou float.',
  backendRules: 'required',
  aggregate: 'sum',
  status: true
}
