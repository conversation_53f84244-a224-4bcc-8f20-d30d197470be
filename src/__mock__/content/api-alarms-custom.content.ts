import { IAlarmsCustom } from '@/@core/domain/AlarmsCustom'
import { AlarmsCustomResponse } from '@/@core/infra/api/AlarmsCustomApiV3/AlarmsCustomApiV3.types'

export const alarmCustomResponseMock1: AlarmsCustomResponse = {
  id: 1,
  entities: [101, 102, 103],
  rule_id: 10,
  rule_name: 'Regra de Teste',
  name: 'Nome do Mock',
  operator_trigger_id: 5,
  operator_trigger_name: 'Maior que',
  value_trigger: '100',
  operator_normalized_id: 2,
  operator_normalized_name: 'Igual',
  value_normalized: '200',
  frequency_notify: 'diário',
  status: 1,
  emails: ['<EMAIL>', '<EMAIL>'],
  notify_method: 'email'
}

export const alarmCustomResponseMock2: AlarmsCustomResponse = {
  id: 1,
  entities: null,
  rule_id: null,
  rule_name: null,
  name: 'Nome do Mock',
  operator_trigger_id: null,
  operator_trigger_name: null,
  value_trigger: null,
  operator_normalized_id: null,
  operator_normalized_name: null,
  value_normalized: null,
  frequency_notify: null,
  status: null,
  emails: null,
  notify_method: null
}

export const alarmCustomMock1: IAlarmsCustom = {
  id: 1,
  entities: [101, 102, 103],
  ruleId: 10,
  ruleName: 'Regra de Teste',
  name: 'Nome do Mock',
  operatorTriggerId: 5,
  operatorTriggerName: 'Maior que',
  valueTrigger: '100',
  operatorNormalizedId: 2,
  operatorNormalizedName: 'Igual',
  valueNormalized: '200',
  frequencyNotify: 'diário',
  status: 1,
  emails: ['<EMAIL>', '<EMAIL>'],
  notifyMethod: 'email'
}

export const alarmCustomMock2: IAlarmsCustom = {
  id: 1,
  entities: null,
  ruleId: null,
  ruleName: null,
  name: 'Nome do Mock',
  operatorTriggerId: null,
  operatorTriggerName: null,
  valueTrigger: null,
  operatorNormalizedId: null,
  operatorNormalizedName: null,
  valueNormalized: null,
  frequencyNotify: null,
  status: null,
  emails: null,
  notifyMethod: null
}
