type AlarmStatus = { slug: string; label: string; css: string }

export const alarmStatusMap: Record<string, AlarmStatus> = {
  pre_alarm: {
    slug: 'pre_alarm',
    label: 'Pré alarme',
    css: 'is-under_observation'
  },
  alarm: { slug: 'alarm', label: '<PERSON>arm<PERSON>', css: 'is-alarm' },
  normalized: { slug: 'normalized', label: 'Normalizado', css: 'is-connected' },
  recognized: { slug: 'recognized', label: 'Reconhecido', css: 'is-stand_by' },
  standBy: { slug: 'standBy', label: 'Stand By', css: 'is-stand_by' },
  default: { slug: 'default', label: 'Desconhecido', css: 'is-alarm' }
}
