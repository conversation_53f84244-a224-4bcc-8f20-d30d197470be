export interface IDashboardTab {
  label: string
  xWidgets: IDashboardTabWidgetX[]
}
export interface IDashboardTabWidgetX {
  key: string
  yWidgets: IDashboardTabWidgetY[]
}
export interface IDashboardTabWidgetY {
  key: string
  name: string
  dataStructure: IDashboardTabStructure
  order: number
  triggerByHeadField: boolean
}
export interface IDashboardTabStructure {
  dataProcessing: boolean
  head: IDashboardTabStructureHead[]
  body: IDashboardTabStructureBody[]
}
export interface IDashboardTabStructureHead {
  type: string
  name: string
  dataStructure: Record<string, any> & {
    values?: Record<string, any>
    rules?: {
      group?: [
        {
          fieldType: string
          label: string
          model: string
        }
      ]
    }[]
  }
  style?: Record<string, string>
  list?: {
    spanTitle: string
    spanPrefix: string
    spanSuffix: string
    data: string
  }[]
}
export interface IDashboardTabStructureBody {
  name: string
  order: number
  dataStructure: Record<string, any>
  triggerRender: boolean
}
