import { Order } from '@/types/system'

export default interface IAlarmsNotifications {
  id: number
  alarm: {
    id: number
    name: string
  }
  alarmId: number
  alarmName: string
  channel: {
    id: number
    name: string
  }
  channelId: number
  channelName: string
  frequency: {
    id: number
    name: string
  }
  frequencyId: number
  frequencyName: string
  user: {
    id: number
    name: string
  }
  userId: number
  userName: string
  configs: string[]
}

export interface IAlarmsNotificationsSearch {
  sort: 'id'
  order: Order
  limit: number
  page: number
  alarm_id?: number
  frequency_id?: number
  alarm_notification_channel_id?: number
}
