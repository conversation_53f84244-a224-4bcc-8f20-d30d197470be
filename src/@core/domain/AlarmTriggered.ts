import { Order } from '@/types/system'

export interface IAlarmTriggered {
  id: number
  alarmId: number | null
  alarmName: string | null
  companyId: number
  companyName: string
  equipmentId: number | null
  equipmentName: string | null
  triggeredAt: string
  triggeredValues: string
  normalizedAt: string
  normalizedValues: string
  status: string
  actionSupport: {
    deviceStatusAfterTrigger: number | null
    deviceStatusAfterNormalize: number | null
  }
}

export type IAlarmTriggeredSort =
  | 'id'
  | 'triggered_at'
  | 'normalized_at'
  | 'status'

export interface IAlarmTriggeredSearch {
  order: Order
  sort: IAlarmTriggeredSort
  page: number
  limit: number
  status: string
  equipmentId?: number
}
