import { featchDataItemUser } from './featchDataItem'

describe('@core/utils/featchDataItem', () => {
  it('should return values ​​of type IUser', () => {
    const dataInput = { id: 1, firstName: 'Sistema' }
    const dataOutput = { value: '1', name: '<PERSON><PERSON><PERSON>', label: '<PERSON><PERSON><PERSON>' }

    const result = featchDataItemUser(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
