import { normalizeRule } from './normalizesOperationalRule'

describe('normalizeRule', () => {
  it('must normalize "subtração" for "subtracao"', () => {
    expect(normalizeRule('subtração')).toBe('subtracao')
  })

  it('must normalize "subtracao" for "subtração"', () => {
    expect(normalizeRule('subtracao')).toBe('subtração')
  })

  it('should return the string itself if there is no match', () => {
    expect(normalizeRule('soma')).toBe('soma')
    expect(normalizeRule('')).toBe('')
  })
})
