import { mergeDeep } from './mergeDeep'

describe('@core/utils/mergeDeep', () => {
  it('group data object values', () => {
    const dataInput1 = {
      id: null,
      name: 'name test',
      permissions: { created: true, edit: false },
      account: undefined
    }
    const dataInput2 = {
      status: true,
      permissions: { edit: true },
      account: { id: 1 }
    }
    const dataInput3 = {
      id: 33,
      permissions: { delete: true },
      deviceId: 3,
      equipmentId: 3,
      logIds: [33]
    }

    const dataOutput = {
      id: 33,
      name: 'name test',
      status: true,
      permissions: { created: true, edit: true, delete: true },
      account: { id: 1 },
      deviceId: 3,
      equipmentId: 3,
      logIds: [33]
    }

    const result = mergeDeep(dataInput1, dataInput2, dataInput3)

    expect(result).toEqual(dataOutput)
  })

  it('substitui array anterior se currentValue existir, senão mantém prevValue', () => {
    const obj1 = { arr: [1, 2, 3] }
    const obj2 = { arr: [4, 5] }
    const obj3 = { arr: undefined }

    // Deve substituir pelo novo array
    expect(mergeDeep(obj1, obj2)).toEqual({ arr: [4, 5] })

    // Deve manter o array anterior se currentValue for undefined
    expect(mergeDeep(obj1, obj3)).toEqual({ arr: [1, 2, 3] })
  })
})
