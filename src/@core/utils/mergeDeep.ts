export function mergeDeep<T extends Record<string, unknown>[]>(...objects: T) {
  function isObject(item: unknown): item is Record<string, unknown> {
    return item !== null && typeof item === 'object' && !Array.isArray(item)
  }
  return objects
    .filter((obj) => !!obj)
    .reduce((prev, obj) => {
      Object.keys(obj).forEach((key) => {
        const prevValue = prev[key] as Record<string, unknown>
        const currentValue = obj[key] as Record<string, unknown>

        if (Array.isArray(prevValue) && Array.isArray(currentValue)) {
          prev[key] = currentValue ?? prevValue
        } else if (isObject(prevValue) && isObject(currentValue)) {
          prev[key] = mergeDeep(prevValue, currentValue)
        } else {
          prev[key] =
            prevValue === undefined
              ? currentValue
              : currentValue || prevValue || null
        }
      })

      return prev
    }, {})
}
