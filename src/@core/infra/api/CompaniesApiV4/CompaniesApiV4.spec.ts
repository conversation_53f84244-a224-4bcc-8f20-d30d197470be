import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { Response } from '@/@core/infra/api/CompaniesApiV4/CompaniesApiV4.types'
import { httpFake, IHttpResponseList } from '@/@core/infra/http'
import {
  companyMock1,
  companyMock2,
  companyResponseMock1,
  companyResponseMock2
} from '@/__mock__/content/api-companies.content'
import { cleanup } from '@testing-library/react'

cleanup()

describe('src/@core/infra/api/CompaniesApiV4/CompaniesApiV4', () => {
  const responseAllMock: IHttpResponseList<Response> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const result = await companiesApiV4(httpFake).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(companyResponseMock1)
    responseAllMock.items.push(companyResponseMock2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const result = await companiesApiV4(httpFake).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      accountId: 1,
      externalId: [1],
      parentId: 1
    })

    expect(result.data.items[0]).toEqual(companyMock1)
    expect(result.data.items[1]).toEqual(companyMock2)
  })

  test('error request get', async () => {
    httpFake.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await companiesApiV4(httpFake).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      accountId: 1,
      externalId: [1],
      parentId: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    const responseMock = {
      status: 200,
      data: companyResponseMock1
    }

    httpFake.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await companiesApiV4(httpFake).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(companyMock1)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: companyResponseMock1
    }

    httpFake.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await companiesApiV4(httpFake).create({
      name: 'LEGAL EMBALAGENS',
      accountId: 1,
      parentId: 1,
      cnpj: '*************',
      corporateName: 'Esther e Rodrigo Vidros Ltda',
      zipCode: '********',
      number: '521',
      complement: '',
      district: 'Itapetininga',
      cityId: 3520,
      stateId: 26,
      cnae: 'SP',
      unitCode: '',
      codeIbge: 2,
      address: 'address',
      timezone: -3,
      type: 'company'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(companyMock1)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: companyResponseMock1
    }

    httpFake.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await companiesApiV4(httpFake).update(1, {
      name: 'LEGAL EMBALAGENS',
      accountId: 1,
      parentId: 1,
      cnpj: '*************',
      corporateName: 'Esther e Rodrigo Vidros Ltda',
      address: 'Rua Antônio Fernandes da Silva',
      zipCode: '********',
      number: '521',
      complement: '',
      district: 'Itapetininga',
      cityId: 3520,
      stateId: 26,
      cnae: 'SP',
      unitCode: '',
      codeIbge: 2,
      timezone: -3,
      type: 'company'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(companyMock1)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    httpFake.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await companiesApiV4(httpFake).delete(1)

    expect(result.status).toBe(204)
  })
})
