import {
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsTriggeredApiV4.helper'
import { TdoSearch } from './AlarmsTriggeredApiV4.types'

describe('parseSearchHelper', () => {
  it('should map TdoSearch fields to API query params', () => {
    const search: TdoSearch = {
      order: 'asc',
      sort: 'id',
      limit: 10,
      page: 2,
      status: 'alarm',
      alarmId: 7,
      companyId: 5,
      equipmentId: 1
    }
    expect(parseSearchHelper(search)).toEqual({
      _sort: 'id',
      _order: 'asc',
      _limit: 10,
      _page: 2,
      company_id: 5,
      equipment_id: 1,
      alarm_id: 7,
      status: 'alarm'
    })
  })

  it('should handle missing optional fields', () => {
    const search = {}
    expect(parseSearchHelper(search)).toEqual({
      _sort: undefined,
      _order: undefined,
      _limit: undefined,
      _page: undefined,
      company_id: undefined,
      equipment_id: undefined,
      alarm_id: undefined,
      status: undefined
    })
  })
})

describe('parseResponseDataHelper', () => {
  it('should map Response to IAlarmTriggered correctly', () => {
    const response = {
      id: 1,
      alarm: { id: 2, name: 'Alarm A' },
      triggered_at: '2024-01-01T00:00:00Z',
      triggered_values: 'value1',
      normalized_at: '2024-01-02T00:00:00Z',
      normalized_values: 'value2',
      status: 'active',
      action_support: {
        device_status_after_trigger: 10,
        device_status_after_normalize: 20
      },
      company: { id: 3, name: 'Company X' },
      equipment: { id: 4, name: 'Equipment Y' }
    }
    expect(parseResponseDataHelper(response)).toEqual({
      id: 1,
      alarmId: 2,
      alarmName: 'Alarm A',
      companyId: 3,
      companyName: 'Company X',
      equipmentId: 4,
      equipmentName: 'Equipment Y',
      triggeredAt: '2024-01-01T00:00:00Z',
      triggeredValues: 'value1',
      normalizedAt: '2024-01-02T00:00:00Z',
      normalizedValues: 'value2',
      status: 'active',
      actionSupport: {
        deviceStatusAfterTrigger: 10,
        deviceStatusAfterNormalize: 20
      }
    })
  })

  it('should handle missing optional nested fields', () => {
    const response = {
      id: 1,
      alarm: undefined,
      triggered_at: '2024-01-01T00:00:00Z',
      triggered_values: 'value1',
      normalized_at: '2024-01-02T00:00:00Z',
      normalized_values: 'value2',
      status: 'inactive',
      action_support: undefined,
      company: { id: 3, name: 'Company X' },
      equipment: undefined
    } as any
    expect(parseResponseDataHelper(response)).toEqual({
      id: 1,
      alarmId: null,
      alarmName: null,
      companyId: 3,
      companyName: 'Company X',
      equipmentId: null,
      equipmentName: null,
      triggeredAt: '2024-01-01T00:00:00Z',
      triggeredValues: 'value1',
      normalizedAt: '2024-01-02T00:00:00Z',
      normalizedValues: 'value2',
      status: 'inactive',
      actionSupport: {
        deviceStatusAfterTrigger: null,
        deviceStatusAfterNormalize: null
      }
    })
  })
})
