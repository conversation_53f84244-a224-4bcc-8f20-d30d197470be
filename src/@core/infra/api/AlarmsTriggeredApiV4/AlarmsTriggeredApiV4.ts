import { IAlarmTriggered } from '@/@core/domain/AlarmTriggered'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsTriggeredApiV4.helper'
import { Response, TdoSearch } from './AlarmsTriggeredApiV4.types'

export const alarmsTriggeredApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-triggered',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAlarmTriggered[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IAlarmTriggered>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  create: async (triggeredId: number) => {
    const url = httpPrepareUrl({
      url: `/api/v4/alarms-triggered/recognize/${triggeredId}`
    })

    const { status, data } = await http.post<Response>(url, {})

    const dataParsed: IAlarmTriggered = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  }
})
