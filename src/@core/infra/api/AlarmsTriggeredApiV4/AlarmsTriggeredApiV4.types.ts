import { Order } from '@/types/system'

export interface TdoSearch {
  order?: Order
  sort?: 'id' | 'triggered_at' | 'normalized_at' | 'status'
  limit?: number
  page?: number
  companyId?: number
  equipmentId?: number | null
  alarmId?: number
  status?: string
}

export interface Response {
  id: number
  alarm: {
    id: number
    name: string
  }
  triggered_at: string
  triggered_values: string
  normalized_at: string
  normalized_values: string
  status: string
  action_support: {
    device_status_after_trigger: number
    device_status_after_normalize: number
  }
  company: {
    id: number
    name: string
  }
  equipment: {
    id: number
    name: string
  }
}
