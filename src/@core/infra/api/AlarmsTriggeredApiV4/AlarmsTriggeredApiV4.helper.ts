import { IAlarmTriggered } from '@/@core/domain/AlarmTriggered'
import { Response, TdoSearch } from './AlarmsTriggeredApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  status: search?.status,
  company_id: search?.companyId,
  equipment_id: search?.equipmentId,
  alarm_id: search?.alarmId
})

export const parseResponseDataHelper = (data: Response): IAlarmTriggered => {
  return {
    id: data.id,
    alarmId: data.alarm?.id ?? null,
    alarmName: data.alarm?.name ?? null,
    companyId: data.company.id,
    companyName: data.company.name,
    equipmentId: data.equipment?.id ?? null,
    equipmentName: data.equipment?.name ?? null,
    triggeredAt: data.triggered_at,
    triggeredValues: data.triggered_values,
    normalizedAt: data.normalized_at,
    normalizedValues: data.normalized_values,
    status: data.status,
    actionSupport: {
      deviceStatusAfterTrigger:
        data?.action_support?.device_status_after_trigger ?? null,
      deviceStatusAfterNormalize:
        data?.action_support?.device_status_after_normalize ?? null
    }
  }
}
