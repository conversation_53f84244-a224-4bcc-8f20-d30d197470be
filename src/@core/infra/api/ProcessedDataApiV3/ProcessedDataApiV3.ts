import { IProcessedData } from '@/@core/domain/ProcessedData'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseResponseDataHelper,
  parseSearchHelper
} from './ProcessedDataApiV3.helper'
import { Response, TdoSearch } from './ProcessedDataApiV3.types'

export const processedDataApiV3 = (http: IHttpClient) => ({
  get: async (search: TdoSearch) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v3/processed-data',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IProcessedData[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IProcessedData>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
