import { IProcessedData } from '@/@core/domain/ProcessedData'
import { Response, TdoSearch } from './ProcessedDataApiV3.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _sort: search?.sort,
  _order: search?.order,
  _page: search?.page,
  data_entity_id: search.dataEntityId
})

export const parseResponseDataHelper = ({
  id,
  name,
  collection
}: Response): IProcessedData => ({
  id,
  name,
  collection
})
