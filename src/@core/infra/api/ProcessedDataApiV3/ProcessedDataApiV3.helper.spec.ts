import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  processedDataMock1,
  processedDataMock2,
  processedDataResponseMock1,
  processedDataResponseMock2
} from '@/__mock__/content/api-processed-data.content'

import { processedDataApiV3 } from './ProcessedDataApiV3'

cleanup()

describe('@core/infra/api/ProcessedDataApiV3', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await processedDataApiV3(http).get({
      dataEntityId: 1
    })

    expect(resultError.status).toBe(204)
    expect(resultError.data.items).toHaveLength(0)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [processedDataResponseMock1, processedDataResponseMock2]
      }
    })

    const resultSuccess = await processedDataApiV3(http).get({
      dataEntityId: 1
    })

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data.items).toHaveLength(2)
    expect(resultSuccess.data.items[0]).toEqual(processedDataMock1)
    expect(resultSuccess.data.items[1]).toEqual(processedDataMock2)
  })
})
