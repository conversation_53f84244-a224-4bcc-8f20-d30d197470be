import { IAlarmsRules } from '@/@core/domain/AlarmsRules'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsRulesApiV4.helper'
import { Response, TdoSearch } from './AlarmsRulesApiV4.types'

export const alarmsRulesApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-rules',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAlarmsRules[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IAlarmsRules>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
