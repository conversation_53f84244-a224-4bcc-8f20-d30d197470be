import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  alarmsRulesResponseMock1,
  alarmsRulesResponseMock2
} from '@/__mock__/content/api-alarms-rules.content'

import { alarmsRulesApiV4 } from './AlarmsRulesApiV4'

cleanup()

describe('@core/infra/api/AlarmsTargetsTypesApiV4', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await alarmsRulesApiV4(http).get()

    expect(resultError.status).toBe(204)
    expect(resultError.data.items).toHaveLength(0)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [alarmsRulesResponseMock1, alarmsRulesResponseMock2]
      }
    })

    const resultSuccess = await alarmsRulesApiV4(http).get()

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data.items).toHaveLength(2)
  })
})
