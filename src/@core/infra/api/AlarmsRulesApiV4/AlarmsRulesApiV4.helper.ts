import { IAlarmsRules } from '@/@core/domain/AlarmsRules'
import { Response, TdoSearch } from './AlarmsRulesApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _sort: search?.sort,
  _order: search?.order,
  data_entity_id: search?.dataEntityId
})

export const parseResponseDataHelper = ({
  id,
  name,
  description,
  alarms_rules_type_id: alarmsRulesTypeId,
  rule_type: ruleType,
  backend_rules: backendRules,
  aggregate,
  status
}: Response): IAlarmsRules => {
  return {
    id,
    name,
    description,
    alarmsRulesTypeId,
    ruleType: {
      requiredProperty: ruleType.required_property,
      requiredProcessed: ruleType.required_processed
    },
    backendRules,
    aggregate,
    status
  }
}
