import { cleanup } from '@testing-library/react'

import { httpFake } from '@/@core/infra/http'
import {
  alarmsNotificationsChannelsMock1,
  alarmsNotificationsChannelsResponseMock1
} from '@/__mock__/content/api-alarms-notifications-channels.content'

import { listAlarmsNotificationsChannelsV4 } from './ListAlarmsNotificationsChannelsV4'

cleanup()

describe('src/@core/infra/api/AlarmsNotificationsChannelsV4/AlarmsNotificationsChannelsV4', () => {
  test('request get without result', async () => {
    const responseMock = {
      status: 200,
      data: {
        items: []
      }
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await listAlarmsNotificationsChannelsV4(
      httpFake
    ).get()

    expect(status).toBe(200)
    expect(data.items).toHaveLength(0)
  })
  test('request get with result', async () => {
    const responseMock = {
      status: 200,
      data: {
        items: [alarmsNotificationsChannelsMock1]
      }
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await listAlarmsNotificationsChannelsV4(
      httpFake
    ).get()

    expect(status).toEqual(200)
    expect(data.items).toHaveLength(1)
    expect(data.items[0]).toEqual(alarmsNotificationsChannelsResponseMock1)
  })
  test('error request', async () => {
    const responseMock = {
      status: 500,
      data: null
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await listAlarmsNotificationsChannelsV4(
      httpFake
    ).get()

    expect(status).toEqual(500)
    expect(data.items).toHaveLength(0)
  })
})
