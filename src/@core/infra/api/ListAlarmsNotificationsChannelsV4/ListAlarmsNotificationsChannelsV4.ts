import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpParseList from '@/@core/utils/httpParseList'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { IAlarmsNotificationsChannels } from '@/@core/domain/AlarmsNotificationsChannels'

import { parseResponseDataHelper } from './ListAlarmsNotificationsChannelsV4.helpers'
import { AlarmsNotificationsChannelsResponse } from './ListAlarmsNotificationsChannelsV4.types'

export const listAlarmsNotificationsChannelsV4 = (http: IHttpClient) => ({
  get: async () => {
    const url = httpPrepareUrl({
      url: '/api/v4/alarms-notifications-channels'
    })

    const { status, data } = await http.get<
      IHttpResponseList<AlarmsNotificationsChannelsResponse>
    >(url)

    const itemsParsed: IAlarmsNotificationsChannels[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IAlarmsNotificationsChannels>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
