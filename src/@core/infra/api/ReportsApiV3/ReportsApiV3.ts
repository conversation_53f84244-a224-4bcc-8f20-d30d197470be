import { IReport, IReportsList } from '@/@core/domain/Report'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  reportGeIdtParseResponse,
  reportGetParseResponse,
  reportsParseSearch
} from './ReportsApiV3.helpers'
import {
  IReportsListResponse,
  IReportsPayloadSearch,
  IReportsResponse
} from './ReportsApiV3.types'

export const reportsApiV3 = (http: IHttpClient) => ({
  get: async (search: IReportsPayloadSearch = {}) => {
    const searchParsed = reportsParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/reports',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IReportsListResponse>
    >(url)

    const itemsParsed: IReportsList[] = (data?.items ?? []).map(
      reportGetParseResponse
    )

    const dataParsed = httpParseList<IReportsList>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },

  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v3/reports',
      id
    })

    const { status, data } = await http.get<IReportsResponse>(url)

    const dataParsed: IReport = reportGeIdtParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },

  create: async (payload: object) => {
    const url = httpPrepareUrl({
      url: '/api/v3/reports'
    })

    const { status, data } = await http.post<IReportsResponse>(url, payload)

    const dataParsed: IReport = reportGeIdtParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },

  update: async (id: number, payload: object) => {
    const url = httpPrepareUrl({
      url: '/api/v3/reports',
      id
    })

    const { status } = await http.put<IReportsListResponse>(url, payload)

    return {
      status,
      data: null
    }
  },

  delete: async (id: number) => {
    const { status } = await http.delete(`/api/v3/reports/${id}`)

    return { status }
  }
})
