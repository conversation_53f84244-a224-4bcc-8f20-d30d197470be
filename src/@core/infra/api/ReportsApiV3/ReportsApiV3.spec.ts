import { http } from '@/@core/infra/http'
import {
  reportsListMock1,
  reportsMock1,
  reportsResponseMock1
} from '@/__mock__/content/api-reports.content'
import { cleanup } from '@testing-library/react'
import { reportsApiV3 } from './ReportsApiV3'

cleanup()

beforeAll(() => {
  jest.useFakeTimers()
  jest.setSystemTime(new Date('2024-05-01T10:00:00Z'))
})

afterAll(() => {
  jest.useRealTimers()
})

describe('src/@core/infra/api/AccountsApiV3/ReportsApiV3', () => {
  test('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await reportsApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(0)
  })

  test('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [reportsListMock1],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await reportsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      model: true
    })

    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(1)
  })

  test('request getById', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: reportsResponseMock1
    })

    const result = await reportsApiV3(http).getById(2)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(reportsMock1)
  })

  test('request delete', async () => {
    http.delete = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const result = await reportsApiV3(http).delete(1)

    expect(result.status).toBe(204)
  })
})
