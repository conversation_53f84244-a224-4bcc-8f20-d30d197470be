import { IReport, IReportsList } from '@/@core/domain/Report'
import {
  IReportsListResponse,
  IReportsPayloadSearch,
  IReportsResponse
} from './ReportsApiV3.types'

export const reportsParseSearch = (search: IReportsPayloadSearch) => ({
  _q: search?.q,
  _order: search?.order,
  _sort: search?.sort,
  _page: search?.page,
  _limit: search?.limit,
  model: search?.model
})
export const reportGetParseResponse = (
  data: IReportsListResponse
): IReportsList => ({
  id: data.id,
  name: data.name,
  updatedAt: data.updated_at,

  entityId: data.entity_id,
  entityType: data.entity_type,
  entity: {
    id: data.entity.id,
    name: data.entity.name,
    type: data.entity.name
  },
  entityName: data.entity.name
})
export const reportGeIdtParseResponse = (data: IReportsResponse): IReport => {
  const {
    entity_fields = {},
    entity_type_fields = {},
    entity_data_fields: entity_data_fieldsResponse,
    period_fields = {},
    aggregate_fields = {},
    type_data = {}
  } = data.filter

  const entity_data_fields = {
    entity_data: entity_data_fieldsResponse.entity_data?.map((e) => ({
      id: e.id,
      name: e?.name ?? e?.nome
    }))
  }

  return {
    id: data.id,
    name: data.name,
    updatedAt: data.updated_at,
    filter: {
      entity_fields,
      entity_type_fields,
      entity_data_fields,
      period_fields,
      aggregate_fields,
      type_data
    }
  }
}
