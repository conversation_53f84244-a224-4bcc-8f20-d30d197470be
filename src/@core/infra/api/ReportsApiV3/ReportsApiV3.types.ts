export interface IReportsPayloadSearch {
  q?: string
  order?: 'asc' | 'desc'
  sort?: 'id' | 'name' | 'created_at'
  page?: number
  limit?: number
  model?: boolean
}
export interface IReportsListResponse {
  id: number
  entity_id: number
  entity_type: string
  name: string
  updated_at: string
  entity: {
    id: number
    name: string
    type: string
  }
}
export interface IReportsResponse {
  id: number
  name: string
  updated_at: string
  filter: {
    entity_fields: object
    entity_type_fields: object
    entity_data_fields: object & {
      entity_data: { id: number; nome?: string; name?: string }[]
    }
    period_fields: object
    aggregate_fields: object
    type_data: object
  }
}
export interface IReportsPayloadCreate {
  entityId: number | null
  typeData: {
    consumption: {
      value: boolean
      fields: {
        consumption: string[]
      }
    }
  }
  entityFields: {
    entity: string
  }
  periodFields: {
    initialDate: string
    finalDate: string
    syntaxDate: string
  }
  aggregateFields: {
    type: string
    dateInterval: string
    dateIntervalNumber: string
  }
  entityDataFields: {
    entityData: number[]
  }
  entityTypeFields: {
    typeMeasurement: string
  }
}
export interface IReportsRequestCreate {
  entity_id: number | null
  type_data: {
    consumption: {
      value: boolean
      fields: {
        consumption: string[]
      }
    }
  }
  entity_fields: {
    entity: string
  }
  period_fields: {
    initial_date: string
    final_date: string
    syntax_date: string
  }
  aggregate_fields: {
    type: string
    date_interval: string
    date_interval_number: string
  }
  entity_data_fields: {
    entity_data: number[]
  }
  entity_type_fields: {
    type_measurement: string
  }
}
