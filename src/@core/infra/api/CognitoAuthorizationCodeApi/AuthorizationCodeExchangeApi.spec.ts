import { cleanup } from '@testing-library/react'

import { httpFake } from '@/@core/infra/http'
import { authorizationCodeExchangeMock1 } from '@/__mock__/content/api-authorization-codeExchange.content'
import { authorizationCodeExchangeApi } from './AuthorizationCodeExchangeApi'

cleanup()

test('request create', async () => {
  httpFake.post = jest.fn().mockImplementationOnce(() => ({
    status: 201,
    data: authorizationCodeExchangeMock1
  }))

  const result = await authorizationCodeExchangeApi(httpFake).login({
    code: 'comerc_00a04f74-6738-4669-94fb-fd193bebea30',
    clientId: '222',
    redirectUri: 'www.teste.com'
  })

  expect(result.status).toBe(201)
  expect(result.data).toEqual({
    accessToken: process.env.NEXT_PUBLIC_TEST_ACCESS_TOKEN,
    tokenType: 'Bearer',
    code: process.env.NEXT_PUBLIC_TEST_CODE
  })
})
