import ILoginCognito from '@/@core/domain/LoginCognito'
import {
  PayloadLogin,
  Response,
  TdoLogin
} from '@/@core/infra/api/CognitoAuthorizationCodeApi/AuthorizationCodeExchangeApi.types'

export const parsePayloadHelper = (tdo: TdoLogin): PayloadLogin => ({
  code: tdo.code,
  client_id: tdo.clientId
  // redirectUri: tdo.redirectUri
})

export const parseResponseDataHelper = (data: Response): ILoginCognito => ({
  accessToken: data.access_token,
  tokenType: data.token_type,
  code: data.code
})
