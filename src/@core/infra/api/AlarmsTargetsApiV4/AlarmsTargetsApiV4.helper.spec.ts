import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  alarmsTargetsMock1,
  alarmsTargetsMock2
} from '@/__mock__/content/api-alarms-targets.content'

import { alarmsTargetsApiV4 } from './AlarmsTargetsApiV4'
import { TdoSearch } from './AlarmsTargetsApiV4.types'

cleanup()

describe('src/@core/infra/api/AlarmsTargetsApiV4', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await alarmsTargetsApiV4(http).get()

    expect(resultError.status).toBe(204)
    expect(resultError.data.items).toHaveLength(0)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: { items: [alarmsTargetsMock1, alarmsTargetsMock2] }
    })

    const search: TdoSearch = {
      order: 'asc',
      sort: 'name',
      dynamicEntities: 1,
      status: 1
    }
    const resultSuccess = await alarmsTargetsApiV4(http).get(search)

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data.items).toHaveLength(2)
  })
})
