import { IAlarmsTargets } from '@/@core/domain/AlarmsTargets'
import { Response, TdoSearch } from './AlarmsTargetsApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _sort: search?.sort,
  _order: search?.order,
  dynamic_entities: search?.dynamicEntities,
  status: search?.status
})

export const parseResponseDataHelper = ({
  id,
  targetType,
  name,
  description,
  dynamic_entities: dynamicEntities,
  status
}: Response): IAlarmsTargets => {
  return {
    id,
    targetType,
    name,
    description,
    dynamicEntities,
    status
  }
}
