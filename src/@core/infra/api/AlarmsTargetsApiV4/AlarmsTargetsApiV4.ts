import { IAlarmsTargets } from '@/@core/domain/AlarmsTargets'
import { IHttpClient, IHttpResponseItems } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsTargetsApiV4.helper'
import { Response, TdoSearch } from './AlarmsTargetsApiV4.types'

export const alarmsTargetsApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-targets',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseItems<Response>>(url)

    const itemsParsed: IAlarmsTargets[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    return {
      status,
      data: {
        items: itemsParsed
      }
    }
  }
})
