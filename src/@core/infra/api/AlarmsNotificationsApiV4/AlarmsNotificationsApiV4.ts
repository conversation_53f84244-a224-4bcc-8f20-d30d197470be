import { IHttpClient, IHttpResponseList } from '@/@core/infra/http/'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import {
  parseAlarmsNotificationsSearchHelper,
  parsePayloadAlarmsNotificationsHelper,
  parseResponseAlarmsNotificationsDataHelper
} from './AlarmsNotificationsApiV4.helpers'

import IAlarmsNotifications from '@/@core/domain/AlarmsNotifications'
import {
  AlarmsNotificationsPayload,
  AlarmsNotificationsResponse,
  AlarmsNotificationsTdo,
  TdoSearchAlarmsNotifications
} from './AlarmsNotificationsApiV4.types'

export const alarmsNotificationsApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearchAlarmsNotifications = {}) => {
    const searchParsed = parseAlarmsNotificationsSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-notifications',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<AlarmsNotificationsResponse>
    >(url)

    const itemsParsed = (data?.items ?? []).map(
      parseResponseAlarmsNotificationsDataHelper
    )

    const dataParsed = httpParseList<IAlarmsNotifications>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },

  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v4/alarms-notifications`,
      id
    })

    const { status, data } = await http.get<AlarmsNotificationsResponse>(url)

    const dataParsed: IAlarmsNotifications =
      parseResponseAlarmsNotificationsDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },

  create: async (tdo: AlarmsNotificationsTdo) => {
    const payload: AlarmsNotificationsPayload =
      parsePayloadAlarmsNotificationsHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v4/alarms-notifications`
    })

    const { status, data } = await http.post<AlarmsNotificationsResponse>(
      url,
      payload
    )

    const dataParsed: IAlarmsNotifications =
      parseResponseAlarmsNotificationsDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },

  update: async (id: number, tdo: AlarmsNotificationsTdo) => {
    const payload: AlarmsNotificationsPayload =
      parsePayloadAlarmsNotificationsHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v4/alarms-notifications`,
      id
    })

    const { status, data } = await http.put<AlarmsNotificationsResponse>(
      url,
      payload
    )

    const dataParsed: IAlarmsNotifications =
      parseResponseAlarmsNotificationsDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },

  delete: async (id: number) => {
    const { status } = await http.delete(`/api/v4/alarms-notifications/${id}`)

    return { status }
  }
})
