import { Order } from '@/types/system'

export interface TdoSearchAlarmsNotifications {
  sort?: 'id'
  order?: Order
  limit?: number
  page?: number
  alarmId?: number
  frequencyId?: number
  alarmNotificationChannelId?: number
}

export interface AlarmsNotificationsResponse {
  id: number
  alarm: {
    id: number
    name: string
  }
  channel: {
    id: number
    name: string
  }
  frequency: {
    id: number
    name: string
  }
  user: {
    id: number
    name: string
  }
  configs: string[]
}

export interface AlarmsNotificationsTdo {
  alarmId: number
  alarmNotificationChannelId: number
  userId: number
  frequencyId: number
  configs: string[]
}

export interface AlarmsNotificationsPayload {
  alarm_id: number
  alarm_notification_channel_id: number
  user_id: number
  frequency_id: number
  configs: string[]
}
