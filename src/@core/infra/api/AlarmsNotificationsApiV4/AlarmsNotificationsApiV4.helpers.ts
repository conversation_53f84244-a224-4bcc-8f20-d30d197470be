import IAlarmsNotifications from '@/@core/domain/AlarmsNotifications'
import {
  AlarmsNotificationsPayload,
  AlarmsNotificationsResponse,
  AlarmsNotificationsTdo,
  TdoSearchAlarmsNotifications
} from './AlarmsNotificationsApiV4.types'

export const parseAlarmsNotificationsSearchHelper = (
  search: TdoSearchAlarmsNotifications
) => ({
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  alarm_id: search?.alarmId,
  frequency_id: search?.frequencyId,
  alarm_notification_channel_id: search?.alarmNotificationChannelId
})

export const parseResponseAlarmsNotificationsDataHelper = (
  data: AlarmsNotificationsResponse
): IAlarmsNotifications => {
  return {
    id: data.id,
    alarm: data.alarm,
    alarmId: data.alarm.id,
    alarmName: data.alarm.name,
    channel: data.channel,
    channelId: data.channel.id,
    channelName: data.channel.name,
    frequency: data.frequency,
    frequencyId: data.frequency.id,
    frequencyName: data.frequency.name,
    user: data.user,
    userId: data.user.id,
    userName: data.user.name,
    configs: data.configs
  }
}

export const parsePayloadAlarmsNotificationsHelper = (
  tdo: AlarmsNotificationsTdo
): AlarmsNotificationsPayload => ({
  alarm_id: tdo.alarmId,
  alarm_notification_channel_id: tdo.alarmNotificationChannelId,
  user_id: tdo.userId,
  frequency_id: tdo.frequencyId,
  configs: tdo.configs
})
