import {
  alarmsNotificationsMock1,
  alarmsNotificationsResponseMock
} from '@/__mock__/content/api-alarms-notifications.content'
import { cleanup } from '@testing-library/react'
import { http, IHttpResponseList } from '../../http'
import { alarmsNotificationsApiV4 } from './AlarmsNotificationsApiV4'
import { AlarmsNotificationsResponse } from './AlarmsNotificationsApiV4.types'

cleanup()

describe('src/@core/infra/api/DevicesApiV4/AlarmsNotificationsApiV4', () => {
  const responseAllMock: IHttpResponseList<AlarmsNotificationsResponse> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await alarmsNotificationsApiV4(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(alarmsNotificationsResponseMock)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await alarmsNotificationsApiV4(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      alarmId: 1,
      alarmNotificationChannelId: 2,
      frequencyId: 3
    })

    expect(status).toEqual(200)
    expect(data.items[0]).toEqual(alarmsNotificationsMock1)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await alarmsNotificationsApiV4(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      alarmId: 1,
      alarmNotificationChannelId: 2,
      frequencyId: 3
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    const responseMock = {
      status: 200,
      data: alarmsNotificationsResponseMock
    }

    http.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsNotificationsApiV4(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(alarmsNotificationsMock1)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: alarmsNotificationsResponseMock
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsNotificationsApiV4(http).create({
      alarmId: 1,
      alarmNotificationChannelId: 1,
      userId: 1,
      frequencyId: 1,
      configs: ['<EMAIL>', '<EMAIL>']
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(alarmsNotificationsMock1)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: alarmsNotificationsResponseMock
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsNotificationsApiV4(http).update(1, {
      alarmId: 1,
      alarmNotificationChannelId: 1,
      userId: 1,
      frequencyId: 1,
      configs: ['<EMAIL>', '<EMAIL>']
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(alarmsNotificationsMock1)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.delete = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsNotificationsApiV4(http).delete(1)

    expect(result.status).toBe(204)
  })
})
