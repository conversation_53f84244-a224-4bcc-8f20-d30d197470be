export interface TdoSearch {
  q: string
  sort: 'id' | 'nome'
  order: 'asc' | 'desc'
  limit: number
  page: number
  companies: number[]
  companiesGroup: number[]
  accountId: number
  withoutDevice?: 1
  typeId: string
}
export interface Response {
  id: number
  name: string
  account: { id: number; name: string } | null
  company: { id: number; name: string }
  device: {
    id: number
    code: string
    type_id: number
    model_id: number
  } | null
  model: {
    id: number
    name: string
  } | null
  distributor: { id: number; name: string } | null
  rule_operational: { id: number; title: string }
  is_virtual: boolean
  scde_key: string | null
}
export interface TdoCreate {
  name: string
  ruleOperationalId: number
  companyId: number
  distributorId: number
  scdeKey: string | null
}
export interface TdoUpdate {
  name: string
  ruleOperationalId: number
  companyId: number
  distributorId: number
  scdeKey: string | null
}
export interface PayloadCreate {
  name: string
  rule_operational_id: number
  company_id: number
  distributor_id: number
  scde_key: string | null
}
export interface PayloadUpdate {
  name: string
  rule_operational_id: number
  company_id: number
  distributor_id: number
  scde_key: string | null
}
