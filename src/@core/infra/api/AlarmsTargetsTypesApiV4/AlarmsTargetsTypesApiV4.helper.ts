import { IAlarmsTargetsTypes } from '@/@core/domain/AlarmsTargetsTypes'
import { Response, TdoSearch } from './AlarmsTargetsTypesApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _sort: search?.sort,
  _order: search?.order
})

export const parseResponseDataHelper = ({
  id,
  dataEntity,
  name,
  slug
}: Response): IAlarmsTargetsTypes => {
  return {
    id,
    dataEntity,
    name,
    slug
  }
}
