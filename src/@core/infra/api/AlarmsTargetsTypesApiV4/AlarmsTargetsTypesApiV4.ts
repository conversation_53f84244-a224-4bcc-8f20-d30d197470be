import { IAlarmsTargetsTypes } from '@/@core/domain/AlarmsTargetsTypes'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsTargetsTypesApiV4.helper'
import { Response, TdoSearch } from './AlarmsTargetsTypesApiV4.types'

export const alarmsTargetsTypesApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-targets-types',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAlarmsTargetsTypes[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    return {
      status,
      data: {
        items: itemsParsed
      }
    }
  }
})
