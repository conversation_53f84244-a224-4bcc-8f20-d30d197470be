import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  alarmsTargetsTypesMock1,
  alarmsTargetsTypesMock2
} from '@/__mock__/content/api-alarms-targets-types.content'

import { alarmsTargetsTypesApiV4 } from './AlarmsTargetsTypesApiV4'
import { TdoSearch } from './AlarmsTargetsTypesApiV4.types'

cleanup()

describe('@core/infra/api/AlarmsTargetsTypesApiV4', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await alarmsTargetsTypesApiV4(http).get()

    expect(resultError.status).toBe(204)
    expect(resultError.data.items).toHaveLength(0)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: { items: [alarmsTargetsTypesMock1, alarmsTargetsTypesMock2] }
    })

    const search: TdoSearch = {
      order: 'asc',
      sort: 'id'
    }
    const resultSuccess = await alarmsTargetsTypesApiV4(http).get(search)

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data.items).toHaveLength(2)
  })
})
