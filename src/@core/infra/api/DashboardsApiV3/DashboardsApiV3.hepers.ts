import {
  IDashboardTab,
  IDashboardTabStructure,
  IDashboardTabStructureBody,
  IDashboardTabStructureHead,
  IDashboardTabWidgetX,
  IDashboardTabWidgetY
} from '@/@core/domain/DashboardTabs'

import {
  IDashboardsPayloadCreate,
  IDashboardsPayloadDelete,
  IDashboardsRequestCreate,
  IDashboardsRequestDelete,
  IDashboardTabResponse,
  IDashboardTabStructureResponse
} from './DashboardsApiV3.types'

export const parseDashboardsPayloadCreate = (
  data: IDashboardsPayloadCreate
): IDashboardsRequestCreate => ({
  entities: data.entities,
  entities_type: data.entitiesType,
  template_id: data.templateId,
  replace: data.replace
})
export const parseDashboardsPayloadDelete = (
  data: IDashboardsPayloadDelete
): IDashboardsRequestDelete => ({
  ids: data.entityIds,
  account_id: data.accountId
})
export class ParseDashboardTabHelpers {
  protected key: string = ''

  constructor(
    private readonly data: IDashboardTabResponse,
    { entityId, entityType }: Record<string, number | string>
  ) {
    this.key = `${data.label}.${entityType}.${entityId}`
      .replaceAll(' ', '_')
      .toLocaleLowerCase()
  }

  public handler(): IDashboardTab {
    return {
      label: this.data.label,
      xWidgets: this.data.structure.map((e, ix) => this.mapWidgetsX(e, ix))
    }
  }

  private mapWidgetsX(
    widgetsX: IDashboardTabStructureResponse[],
    ix: number
  ): IDashboardTabWidgetX {
    return {
      key: `${this.key}|${ix}`,
      yWidgets: widgetsX.map((e, iy) => this.mapWidgetsY(e, iy, ix))
    }
  }

  private mapWidgetsY(
    widget: IDashboardTabStructureResponse,
    iy: number,
    ix: number
  ): IDashboardTabWidgetY {
    return {
      key: `${this.key}|${ix}-${iy}`,
      name: widget.name,
      order: widget.position,
      dataStructure: this.parseStructure(widget),
      triggerByHeadField: false
    }
  }

  private parseStructure(
    widget: IDashboardTabStructureResponse
  ): IDashboardTabStructure {
    let data: unknown = {}

    data = this.normalizeStructureToDynamicWidget(widget)
    data = this.idenfifyHeadTypes(data)

    return data as IDashboardTabStructure
  }

  private normalizeStructureToDynamicWidget(
    payload: unknown
  ): IDashboardTabStructure {
    const widget = payload as IDashboardTabStructureResponse

    const currentTabStructure: IDashboardTabStructure = {
      dataProcessing: false,
      head: [],
      body: []
    }

    const isDynamicWidget: boolean = widget.name === 'DynamicWidget'

    if (isDynamicWidget) {
      currentTabStructure.dataProcessing = true
      currentTabStructure

      /** increment CONTENT-HEAD */
      widget.dataStructure?.head?.forEach((el: IDashboardTabStructureHead) => {
        currentTabStructure.head.push(el)
      })

      /** increment CONTENT-BODY */
      widget.dataStructure?.body?.forEach((el: IDashboardTabStructureBody) => {
        currentTabStructure.body.push({ ...el, triggerRender: false })
      })
    } else {
      currentTabStructure.dataProcessing = false

      /** initial CONTENT-HEAD */
      currentTabStructure.head = []

      /** apply unique CONTENT-HEAD */
      currentTabStructure.body = [
        {
          name: widget.name,
          order: widget.position,
          dataStructure: {
            ...widget.dataStructure
          },
          triggerRender: false
        }
      ]
    }

    return { ...currentTabStructure }
  }

  private idenfifyHeadTypes(payload: unknown): IDashboardTabStructure {
    const widget = payload as IDashboardTabStructure

    const currentHead = widget.head
      .map((el) => {
        el.type =
          el.type === 'dynamic' && el.name !== 'DynamicIcon' ? el.name : el.type

        return el
      })
      .filter((el) => el.type !== 'dynamic')

    return { ...widget, head: currentHead }
  }
}
