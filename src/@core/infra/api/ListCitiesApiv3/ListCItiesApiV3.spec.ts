import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  listCItyMock1,
  listCityResponseMock1
} from '@/__mock__/content/api-list-cities.content'

import { listCitiesApiv3 } from './ListCitiesApiv3'

cleanup()

describe('src/@core/infra/api/ListCitiesApiv3', () => {
  test('request get without result', async () => {
    const responseMock = {
      status: 200,
      data: {
        items: []
      }
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await listCitiesApiv3(http).get()

    expect(status).toBe(200)
    expect(data.items).toHaveLength(0)
  })
  test('request get with result', async () => {
    const responseMock = {
      status: 200,
      data: {
        data: [listCityResponseMock1]
      }
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await listCitiesApiv3(http).get()

    expect(status).toEqual(200)
    expect(data.items).toHaveLength(1)
    expect(data.items[0]).toEqual(listCItyMock1)
  })

  test('error request', async () => {
    const responseMock = {
      status: 500,
      data: null
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await listCitiesApiv3(http).get()

    expect(status).toEqual(500)
    expect(data.items).toHaveLength(0)
  })
})
