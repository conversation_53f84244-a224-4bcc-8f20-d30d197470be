import { IListCities } from '@/@core/domain/ListCities'
import { IHttpClient, IHttpResponseData } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { parseResponseDataHelper } from './ListCitiesApiv3.helpers'
import { Response } from './ListCitiesApiv3.types'

export const listCitiesApiv3 = (http: IHttpClient) => ({
  get: async () => {
    const url = httpPrepareUrl({
      url: '/api/v3/list-cities'
    })

    const { status, data } = await http.get<IHttpResponseData<Response>>(url)

    const dataParsed: IListCities[] = (data?.data ?? []).map(
      parseResponseDataHelper
    )

    return {
      status,
      data: {
        items: dataParsed
      }
    }
  },

  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v3/list-cities',
      id
    })

    const { status, data } = await http.get<IHttpResponseData<Response>>(url)
    const dataParsed: IListCities[] = (data?.data ?? []).map(
      parseResponseDataHelper
    )

    return {
      status,
      data: dataParsed
    }
  }
})
