import { IAlarmsCustom } from '@/@core/domain/AlarmsCustom'
import { IHttpClient, IHttpResponseData } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseAlarmsCustomResponseDataHelper,
  parseAlarmsCustomSearchHelper
} from './AlarmsCustomApiV3.helper'
import {
  AlarmsCustomResponse,
  TdoSearchAlarmsCustom
} from './AlarmsCustomApiV3.types'

export const alarmsCustomV3 = (http: IHttpClient) => ({
  get: async ({
    entity,
    entityId,
    limit,
    ...rest
  }: TdoSearchAlarmsCustom = {}) => {
    const searchParsed = parseAlarmsCustomSearchHelper({
      entity,
      entityId,
      limit,
      ...rest
    })

    const url = httpPrepareUrl({
      url: `/api/v3/alarms-custom-${entity}/${entityId}/list/${limit}`,
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseData<AlarmsCustomResponse>
    >(url)

    const itemsParsed: IAlarmsCustom[] = (data?.data ?? []).map(
      parseAlarmsCustomResponseDataHelper
    )

    const dataParsed = httpParseList<IAlarmsCustom>({
      ...data,
      items: itemsParsed,
      current_page: data?.current_page ?? 1,
      last_page: data?.last_page ?? 1,
      per_page: data?.per_page ?? itemsParsed.length,
      total: data?.total ?? itemsParsed.length
    })

    return {
      status,
      data: dataParsed
    }
  }
})
