export interface TdoSearchAlarmsCustom {
  page?: number
  limit?: number
  entity?: string
  entityId?: number
}

export interface AlarmsCustomResponse {
  id: number
  entities: number[] | null
  rule_id: number | null
  rule_name: string | null
  name: string
  operator_trigger_id: number | null
  operator_trigger_name: string | null
  value_trigger: string | null
  operator_normalized_id: number | null
  operator_normalized_name: string | null
  value_normalized: string | null
  frequency_notify: string | null
  status: number | null
  emails: string[] | null
  notify_method: string | null
}
