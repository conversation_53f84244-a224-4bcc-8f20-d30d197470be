import { IAlarmsCustom } from '@/@core/domain/AlarmsCustom'
import {
  AlarmsCustomResponse,
  TdoSearchAlarmsCustom
} from './AlarmsCustomApiV3.types'

export const parseAlarmsCustomSearchHelper = (
  search: TdoSearchAlarmsCustom
) => ({
  page: search?.page
})

export const parseAlarmsCustomResponseDataHelper = (
  data: AlarmsCustomResponse
): IAlarmsCustom => {
  return {
    id: data.id,
    entities: data.entities,
    ruleId: data.rule_id,
    ruleName: data.rule_name,
    name: data.name,
    operatorTriggerId: data.operator_trigger_id,
    operatorTriggerName: data.operator_trigger_name,
    valueTrigger: data.value_trigger,
    operatorNormalizedId: data.operator_normalized_id,
    operatorNormalizedName: data.operator_normalized_name,
    valueNormalized: data.value_normalized,
    frequencyNotify: data.frequency_notify,
    status: data.status,
    emails: data.emails,
    notifyMethod: data.notify_method
  }
}
