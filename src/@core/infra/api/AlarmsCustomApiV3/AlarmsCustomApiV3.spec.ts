import { cleanup } from '@testing-library/react'

import { http, IHttpResponseData } from '@/@core/infra/http'
import {
  alarmCustomMock1,
  alarmCustomMock2,
  alarmCustomResponseMock1,
  alarmCustomResponseMock2
} from '@/__mock__/content/api-alarms-custom.content'

import { alarmsCustomV3 } from './AlarmsCustomApiV3'
import { AlarmsCustomResponse } from './AlarmsCustomApiV3.types'

cleanup()

describe('src/@core/infra/api/AlarmsCustomApiV3', () => {
  const responseAllMock: IHttpResponseData<AlarmsCustomResponse> = {
    data: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await alarmsCustomV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.data.push(alarmCustomResponseMock1)
    responseAllMock.data.push(alarmCustomResponseMock2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await alarmsCustomV3(http).get({
      limit: 100,
      page: 1
    })

    expect(status).toEqual(200)
    expect(data.items[0]).toEqual(alarmCustomMock1)
    expect(data.items[1]).toEqual(alarmCustomMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await alarmsCustomV3(http).get({
      limit: 100,
      page: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      lastPage: 1,
      limit: 0,
      page: 1,
      total: 0
    })
  })
})
