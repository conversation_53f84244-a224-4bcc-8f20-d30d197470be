import { IEquipmentVirtual } from '@/@core/domain/EquipmentVirtual'
import {
  PayloadCreate,
  Response,
  TdoCreate,
  TdoSearch
} from './EquipmentVirtualApiV4.types'

export const parseSearchHelper = (search: Partial<TdoSearch>) => ({
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  equipment_id: search?.equipmentId
})

export const parseResponseDataHelper = (item: Response): IEquipmentVirtual => {
  return {
    id: item.id,
    equipmentVirtual: item.equipment_virtual,
    equipmentComposition: item.equipment_composition,
    rule: item.rule
  }
}

export const parsePayloadHelper = (tdo: TdoCreate): PayloadCreate => ({
  equipment_id: tdo.equipmentId,
  equipment_composition_id: tdo.equipmentCompositionId,
  rule: tdo.rule
})
