export interface TdoSearch {
  sort: 'id'
  order: 'asc' | 'desc'
  limit: number
  page: number
  equipmentId: number
}

export interface Response {
  id: number
  equipment_virtual: {
    id: number
    name: string
  }
  equipment_composition: {
    id: number
    name: string
  }
  rule: string
}

export interface TdoCreate {
  equipmentId: number
  equipmentCompositionId: number
  rule: string
}

export interface TdoUpdate extends TdoCreate {}

export interface PayloadCreate {
  equipment_id: number
  equipment_composition_id: number
  rule: string
}

export interface PayloadUpdate extends PayloadCreate {}
