import { IEquipmentVirtual } from '@/@core/domain/EquipmentVirtual'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import {
  parsePayloadHelper,
  parseResponseDataHelper,
  parseSearchHelper
} from './EquipmentVirtualApiV4.helper'
import {
  PayloadCreate,
  PayloadUpdate,
  Response,
  TdoCreate,
  TdoSearch,
  TdoUpdate
} from './EquipmentVirtualApiV4.types'

export const equipmentVirtualApiV4 = (http: IHttpClient) => ({
  get: async (search: Partial<TdoSearch> = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/equipment-composition-virtual',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IEquipmentVirtual[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IEquipmentVirtual>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v4/equipment-composition-virtual',
      id
    })

    const { status, data } = await http.get<Response>(url)

    const dataParsed: IEquipmentVirtual = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: TdoCreate) => {
    const payload: PayloadCreate = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: '/api/v4/equipment-composition-virtual'
    })

    const { status, data } = await http.post<Response>(url, payload)

    const dataParsed: IEquipmentVirtual = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: TdoUpdate) => {
    const payload: PayloadUpdate = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: '/api/v4/equipment-composition-virtual',
      id
    })

    const { status, data } = await http.put<Response>(url, payload)

    const dataParsed: IEquipmentVirtual = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v4/equipment-composition-virtual',
      id
    })

    const { status } = await http.delete(url)

    return { status }
  }
})
