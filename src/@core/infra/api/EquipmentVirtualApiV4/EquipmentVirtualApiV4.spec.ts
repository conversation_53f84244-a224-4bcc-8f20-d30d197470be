import { cleanup } from '@testing-library/react'

import { Response } from '@/@core/infra/api/EquipmentVirtualApiV4/EquipmentVirtualApiV4.types'
import { http, IHttpResponseList } from '@/@core/infra/http'
import {
  equipmentVirtualMock1,
  equipmentVirtualMock2,
  equipmentVirtualResponseMock1,
  equipmentVirtualResponseMock2
} from '@/__mock__/content/api-equipment-virtual.content'
import { equipmentVirtualApiV4 } from './EquipmentVirtualApiV4'

cleanup()

describe('src/@core/infra/api/EquipmentVirtualApiV4', () => {
  const responseAllMock: IHttpResponseList<Response> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await equipmentVirtualApiV4(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(equipmentVirtualResponseMock1)
    responseAllMock.items.push(equipmentVirtualResponseMock2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result2 = await equipmentVirtualApiV4(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      equipmentId: 1
    })

    expect(result2.data.items[0]).toEqual(equipmentVirtualMock1)
    expect(result2.data.items[1]).toEqual(equipmentVirtualMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await equipmentVirtualApiV4(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      equipmentId: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    const responseMock = {
      status: 200,
      data: equipmentVirtualResponseMock1
    }

    http.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await equipmentVirtualApiV4(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(equipmentVirtualMock1)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: equipmentVirtualResponseMock1
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await equipmentVirtualApiV4(http).create({
      equipmentCompositionId: 1,
      equipmentId: 1,
      rule: 'rule'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(equipmentVirtualMock1)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: equipmentVirtualResponseMock2
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await equipmentVirtualApiV4(http).update(13748, {
      equipmentCompositionId: 2,
      equipmentId: 2,
      rule: 'rule'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(equipmentVirtualMock2)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.delete = jest.fn().mockImplementationOnce(() => responseMock)
    const result = await equipmentVirtualApiV4(http).delete(1)
    expect(result.status).toBe(204)
  })
})
