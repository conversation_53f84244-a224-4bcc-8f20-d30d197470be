import IDeviceConstant from '@/@core/domain/DeviceConstant'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import {
  parsePayloadHelper,
  parseResponseDataHelper,
  parseSearchHelper
} from './DeviceConstantsApiV4.hepers'
import { Payload, Response, Tdo, TdoSearch } from './DeviceConstantsApiV4.types'

export const deviceConstantsApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/device-constants',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const dataParsed = httpParseList<IDeviceConstant>({
      ...data,
      items: data?.items?.map(parseResponseDataHelper)
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v4/device-constants`,
      id
    })

    const { status, data } = await http.get<Response>(url)

    const dataParsed: IDeviceConstant = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: Tdo) => {
    const payload: Payload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v4/device-constants`
    })

    const { status, data } = await http.post<Response>(url, payload)

    const dataParsed: IDeviceConstant = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: Tdo) => {
    const payload: Payload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v4/device-constants`,
      id
    })

    const { status, data } = await http.put<Response>(url, payload)

    const dataParsed: IDeviceConstant = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const { status } = await http.delete(`/api/v4/device-constants/${id}`)

    return { status }
  }
})
