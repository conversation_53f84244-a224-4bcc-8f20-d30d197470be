import IDeviceConstant from '@/@core/domain/DeviceConstant'
import { Order } from '@/types/system'

export interface TdoSearch {
  sort?: 'id' | 'initial_date'
  order?: Order
  limit?: number
  page?: number
  deviceId?: number
}

export interface Response {
  id: number
  device_id: number
  potential_relation: number
  current_relation: number
  meter_constant: number
  loss_factor: number | null
  initial_date: string
}

export interface Tdo extends Omit<IDeviceConstant, 'id'> { }

export interface Payload {
  device_id: number
  potential_relation: number
  current_relation: number
  meter_constant: number
  loss_factor: number | null
  initial_date: string
}
