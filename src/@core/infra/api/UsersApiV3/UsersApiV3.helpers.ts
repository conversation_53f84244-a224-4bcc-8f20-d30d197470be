import { mapEmailProviders } from '@/@core/content/providers.content'
import { IUser } from '@/@core/domain/User'
import { formatPhoneNumber } from '@/@core/presentation/views/users.id/page.utils'
import {
  IUserPayload,
  IUserRequest,
  IUserResponse,
  IUserSearch
} from './UsersApiV3.types'

export const parseUserSearch = (search: IUserSearch) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  account_id: search?.accountId
})
export const parseUserResponse = (data: IUserResponse): IUser => {
  const accountUser = data?.account_user
    ? {
        id: data.account_user.id,
        userId: data.account_user.user_id,
        admin: data.account_user.admin
      }
    : null

  const cellphone = data.cellphone ? formatPhoneNumber(data.cellphone) : ''

  return {
    id: data.id,
    firstName: data.name,
    lastName: data.last_name,
    email: data.email,
    account: data.account,
    accountId: data.account?.id,
    accountName: data.account?.name,
    accountUser,
    accountUserId: accountUser ? accountUser.id : null,
    accountUserAdmin: !!data?.account_user?.admin || false,
    active: data.active,
    admin: data.admin,
    avatar: data.avatar,
    cellphone,
    receiveAlertEmails: data.receive_alert_emails,
    lastAccess: data.last_access ?? null,
    cognitoUserId: data.cognito_user_id ?? null
  }
}
export const parseUserRequest = (tdo: IUserPayload): IUserRequest => {
  let data: IUserRequest = {
    account_id: tdo.accountId,
    admin: tdo.admin,
    active: tdo.active
  }

  const isProvider = !![...mapEmailProviders].filter((provider) =>
    tdo.email.includes(provider)
  ).length

  if (isProvider) {
    return data
  }

  data = {
    ...data,
    name: tdo.firstName,
    last_name: tdo.lastName,
    email: tdo.email,
    cellphone: tdo.cellphone
  }

  if (tdo.pincode) {
    return {
      ...data,
      pincode: tdo.pincode
    }
  }

  return data
}
