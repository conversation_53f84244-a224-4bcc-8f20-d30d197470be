import { IAlarmsMultiTargets } from '@/@core/domain/AlarmsMultiTargets'
import { IHttpClient } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parsePayloadHelper,
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsMultiTargetsApiV4.helper'
import {
  Payload,
  Response,
  Tdo,
  TdoSearch
} from './AlarmsMultiTargetsApiV4.types'

export const alarmsMultiTargetsApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-multi-targets',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<Response>(url)

    const dataParsed: IAlarmsMultiTargets | null = data
      ? parseResponseDataHelper(data)
      : null

    return {
      status,
      data: dataParsed
    }
  },

  create: async (tdo: Tdo) => {
    const payload: Payload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-multi-targets'
    })

    const { status, data } = await http.post<Response>(url, payload)

    const dataParsed: IAlarmsMultiTargets | null = data
      ? parseResponseDataHelper(data)
      : null

    return {
      status,
      data: dataParsed
    }
  }
})
