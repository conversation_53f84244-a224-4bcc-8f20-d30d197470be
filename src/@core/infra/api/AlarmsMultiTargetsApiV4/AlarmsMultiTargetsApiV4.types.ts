import { Order } from '@/types/system'

export interface TdoSearch {
  order?: Order
  sort?: 'id'
  alarmId?: number
}

export interface Tdo {
  alarmId: number
  type: string
  targets: number[]
  equipments: number[]
  companies: number[]
}

export interface Response {
  alarm_id: number
  companies: { id: number; name: string }[]
  equipments: { id: number; name: string }[]
  targets: [
    {
      id: number
      name: string
      targetType: {
        id: number
        name: string
        slug: string
        dataEntity: {
          id: number
          name: string
        }
      }
    }
  ]
}

export interface Payload {
  alarm_id: number
  targets: number[]
  equipments?: number[]
  companies?: number[]
}
