import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  alarmsMultiTargetsMock1,
  alarmsMultiTargetsResponseMock1
} from '@/__mock__/content/api-alarms-multi-targets.content'

import { alarmsMultiTargetsApiV4 } from './AlarmsMultiTargetsApiV4'

cleanup()

describe('@core/infra/api/AlarmsTargetsTypesApiV4', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await alarmsMultiTargetsApiV4(http).get({
      order: 'desc',
      sort: 'id',
      alarmId: 1
    })

    expect(resultError.status).toBe(204)
    expect(resultError.data).toEqual(null)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: { ...alarmsMultiTargetsResponseMock1 }
    })

    const resultSuccess = await alarmsMultiTargetsApiV4(http).get()

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data).toEqual(alarmsMultiTargetsMock1)
  })
})
