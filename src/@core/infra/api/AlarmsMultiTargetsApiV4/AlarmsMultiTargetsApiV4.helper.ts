import { IAlarmsMultiTargets } from '@/@core/domain/AlarmsMultiTargets'
import { Payload, Response, TdoSearch } from './AlarmsMultiTargetsApiV4.types'

const CONST_TARGET_TYPE_EQUIPMENT_ID = 3
const CONST_TARGET_TYPE_COMPANY_ID = 4

export const parseSearchHelper = (search: TdoSearch) => ({
  _sort: search?.sort,
  _order: search?.order,
  alarm_id: search?.alarmId
})

export const parseResponseDataHelper = ({
  alarm_id: alarmId,
  targets,
  companies = [],
  equipments = []
}: Response): IAlarmsMultiTargets => {
  return {
    alarmId,
    targets,
    companies,
    equipments
  }
}

export const parsePayloadHelper = ({
  alarmId: alarm_id,
  type,
  targets,
  companies,
  equipments
}: {
  alarmId: number
  type: string
  targets: number[]
  equipments: number[]
  companies: number[]
}): Payload => {
  /**
   * Combinações para enviar:
   *
   * type TEMPLATE
   * {alarmId, targets}
   *
   * type EQUIPMENT
   * {alarmId, targets[3], equipments}
   *
   * type COMPANY
   * {alarmId, targets[4], companies}
   */

  const values: Omit<Payload, 'alarm_id'> = {
    targets,
    equipments,
    companies
  }

  if (values?.equipments?.length === 0) {
    delete values.equipments
  }
  if (values?.companies?.length === 0) {
    delete values.companies
  }

  if (['equipments', 'equipment'].includes(type)) {
    values.targets = [CONST_TARGET_TYPE_EQUIPMENT_ID]
  }
  if (['companies', 'company'].includes(type)) {
    values.targets = [CONST_TARGET_TYPE_COMPANY_ID]
  }

  return { ...values, alarm_id }
}
