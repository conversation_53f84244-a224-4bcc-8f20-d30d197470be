import { IApportionmentMeasuresUnit } from '@/@core/domain/ApportionmentMeasuresUnit'
import {
  IApportionmentMeasuresUnitResponse,
  IApportionmentMeasuresUnitTdoSearch
} from './ApportionmentMeasuresUnitsApiV3.types'

export const parseApportionmentMeasuresUnitSearch = (
  search: IApportionmentMeasuresUnitTdoSearch
) => ({
  type_id: search?.typeId
})

export const parseApportionmentMeasuresUnitResponse = (
  { id, name, typeId }: IApportionmentMeasuresUnitResponse & { typeId: number }
): IApportionmentMeasuresUnit => {
  return { id, name, typeId }
}
