import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseApportionmentMeasuresUnitResponse,
  parseApportionmentMeasuresUnitSearch
} from './ApportionmentMeasuresUnitsApiV3.hepers'
import {
  IApportionmentMeasuresUnitResponse,
  IApportionmentMeasuresUnitTdoSearch
} from './ApportionmentMeasuresUnitsApiV3.types'

export const apportionmentMeasuresUnitsApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentMeasuresUnitTdoSearch) => {
    const searchParsed = parseApportionmentMeasuresUnitSearch(search)

    const { typeId } = search

    const url = httpPrepareUrl({
      url: '/api/v3/apportionment-measures-units',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentMeasuresUnitResponse>
    >(url)

    const dataParsed = (data?.items ?? []).map(item =>
      parseApportionmentMeasuresUnitResponse({ ...item, typeId })
    )

    return {
      status,
      data: dataParsed
    }
  }
})
