import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  alarmsOperatorLogicResponseMock1,
  alarmsOperatorLogicResponseMock2
} from '@/__mock__/content/api-alarms-operator-logic.content'

import { alarmsOperatorLogicApiV4 } from './AlarmsOperatorLogicApiV4'

cleanup()

describe('@core/infra/api/AlarmsTargetsTypesApiV4', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await alarmsOperatorLogicApiV4(http).get()

    expect(resultError.status).toBe(204)
    expect(resultError.data.items).toHaveLength(0)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          alarmsOperatorLogicResponseMock1,
          alarmsOperatorLogicResponseMock2
        ]
      }
    })

    const resultSuccess = await alarmsOperatorLogicApiV4(http).get()

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data.items).toHaveLength(2)
  })
})
