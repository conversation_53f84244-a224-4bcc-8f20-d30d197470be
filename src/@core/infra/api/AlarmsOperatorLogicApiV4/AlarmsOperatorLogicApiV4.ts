import { IAlarmsOperatorLogic } from '@/@core/domain/AlarmsOperatorLogic'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { parseResponseDataHelper } from './AlarmsOperatorLogicApiV4.helper'
import { Response } from './AlarmsOperatorLogicApiV4.types'

export const alarmsOperatorLogicApiV4 = (http: IHttpClient) => ({
  get: async () => {
    const url = httpPrepareUrl({
      url: '/api/v4/alarms-operator-logic'
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAlarmsOperatorLogic[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    return {
      status,
      data: {
        items: itemsParsed
      }
    }
  }
})
