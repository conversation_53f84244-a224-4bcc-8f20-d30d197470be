import { IAlarmsConditionals } from '@/@core/domain/AlarmsConditionals'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parsePayloadHelper,
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsConditionalsApiV4.helper'
import {
  Payload,
  Response,
  Tdo,
  TdoSearch
} from './AlarmsConditionalsApiV4.types'

export const alarmsConditionalsApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-conditionals',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAlarmsConditionals[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IAlarmsConditionals>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: Tdo) => {
    const payload: Payload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-conditionals'
    })

    const { status, data } = await http.post<Response>(url, payload)

    const dataParsed: IAlarmsConditionals = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: Tdo) => {
    const payload: Payload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: '/api/v4/alarms-conditionals',
      id
    })

    const { status, data } = await http.put<Response>(url, payload)

    const dataParsed: IAlarmsConditionals = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v4/alarms-conditionals',
      id
    })

    const { status } = await http.delete(url)

    return { status }
  }
})
