import { IAlarmsConditionals } from '@/@core/domain/AlarmsConditionals'
import {
  Payload,
  Response,
  Tdo,
  TdoSearch
} from './AlarmsConditionalsApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  alarm_stage_id: search?.alarmStageId,
  alarm_id: search?.alarmId
})

export const parseResponseDataHelper = ({
  id,
  alarm_stage_id: alarmStageId,
  value,
  alarm,
  rule,
  operator,
  property,
  processed
}: Response): IAlarmsConditionals => ({
  id,
  alarmStageId,
  value,
  alarm: {
    id: alarm.id,
    name: alarm.name
  },
  rule: {
    id: rule.id,
    name: rule.name
  },
  operator: {
    id: operator.id,
    name: operator.name
  },
  property: property
    ? {
        id: property.id,
        name: property.name
      }
    : null,
  processed: processed
    ? {
        id: processed.id,
        name: processed.name,
        autoNormalized: processed.auto_normalized
      }
    : null
})

export const parsePayloadHelper = ({
  alarmId: alarm_id,
  alarmRuleId: alarm_rule_id,
  alarmStageId: alarm_stage_id,
  operatorLogicId: operator_logic_id,
  value,
  propertyId: property_id,
  processedId: processed_id
}: Tdo): Payload => ({
  alarm_id,
  alarm_rule_id,
  alarm_stage_id,
  operator_logic_id,
  value,
  property_id,
  processed_id
})
