import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  alarmsConditionalsMock1,
  alarmsConditionalsResponseMock1,
  alarmsConditionalsResponseMock2
} from '@/__mock__/content/api-alarms-conditionals.content'

import { alarmsConditionalsApiV4 } from './AlarmsConditionalsApiV4'

cleanup()

describe('@core/infra/api/AlarmsConditionalsApiV4', () => {
  it('request get', async () => {
    /* request successful without data **/
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const resultError = await alarmsConditionalsApiV4(http).get({
      alarmId: 1,
      alarmStageId: 1
    })

    expect(resultError.status).toBe(204)
    expect(resultError.data.items).toHaveLength(0)

    /* request successful with data **/
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          alarmsConditionalsResponseMock1,
          alarmsConditionalsResponseMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const resultSuccess = await alarmsConditionalsApiV4(http).get({
      alarmId: 1
    })

    expect(resultSuccess.status).toBe(200)
    expect(resultSuccess.data.items).toHaveLength(2)
    expect(resultSuccess.data.items[0]).toEqual(alarmsConditionalsMock1)
  })
  it('request create', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 201,
      data: alarmsConditionalsResponseMock1
    })

    const result = await alarmsConditionalsApiV4(http).create({
      alarmId: 144,
      alarmRuleId: 1,
      alarmStageId: 1,
      operatorLogicId: 1,
      processedId: 1,
      propertyId: 1,
      value: '123'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(alarmsConditionalsMock1)
  })
  it('request update', async () => {
    http.put = jest.fn().mockReturnValue({
      status: 201,
      data: alarmsConditionalsResponseMock1
    })

    const result = await alarmsConditionalsApiV4(http).update(1, {
      alarmId: 144,
      alarmRuleId: 1,
      alarmStageId: 1,
      operatorLogicId: 1,
      processedId: 1,
      propertyId: 1,
      value: '123'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(alarmsConditionalsMock1)
  })
  it('request delete', async () => {
    http.delete = jest.fn().mockReturnValue({
      status: 204
    })

    const result = await alarmsConditionalsApiV4(http).delete(1)

    expect(result.status).toBe(204)
  })
})
