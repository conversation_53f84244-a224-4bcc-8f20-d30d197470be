export interface TdoSearch {
  alarmId: number
  alarmStageId?: 1 | 2
}
export interface Response {
  id: number
  alarm_stage_id: number
  value: string
  alarm: { id: number; name: string }
  rule: { id: number; name: string }
  operator: { id: number; name: string }
  property: { id: number; name: string } | null
  processed: { id: number; name: string; auto_normalized: boolean } | null
}
export interface Tdo {
  alarmId: number
  alarmRuleId: number
  alarmStageId: number
  operatorLogicId: number
  value: string
  propertyId: number | null
  processedId: number | null
}
export interface Payload {
  alarm_id: number
  alarm_rule_id: number
  alarm_stage_id: number
  operator_logic_id: number
  value: string
  property_id: number | null
  processed_id: number | null
}
