import {
  ALARMS_CATEGORY_SYSTEM_ID,
  ALARMS_CATEGORY_USER_ID
} from '@/@core/constants'
import IAlarm from '@/@core/domain/Alarm'
import {
  IAlarmsRequestUpdate,
  Payload,
  Response,
  Tdo,
  TdoSearch,
  TdoUpdate
} from './AlarmsApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  status: search?.status,
  alarms_category_id: search?.alarmsCategoryId
})
export const parseResponseDataHelper = (data: Response): IAlarm => ({
  id: data.id,
  name: data.name,
  description: data.description,
  timeConfirmation: data.time_confirmation,
  initialHour: data.initial_hour,
  finalHour: data.final_hour,
  daysWeek: data.days_week,
  daysRetention: data.days_retention,
  status: data.status,
  account: data.account ?? null,
  accountId: data.account?.id ?? null,
  category: data.category,
  categoryId: data.category.id,
  readonly: data.category.id === ALARMS_CATEGORY_SYSTEM_ID
})
export const parsePayloadHelper = (tdo: Tdo): Payload => {
  const convertToString = (value: string | number | null) =>
    value ? String(value) : null

  return {
    name: tdo.name,
    description: tdo.description,
    initial_hour: convertToString(tdo.initialHour),
    final_hour: convertToString(tdo.finalHour),
    days_week: tdo.daysWeek.map(Number),
    days_retention: convertToString(tdo.daysRetention),
    alarms_category_id: tdo.categoryId,
    time_confirmation: tdo.timeConfirmation,
    account_id:
      tdo.categoryId === ALARMS_CATEGORY_USER_ID
        ? undefined
        : Number(tdo.accountId),
    status: tdo.status
  }
}

export const alarmsParsePayloadUpdate = (
  tdo: TdoUpdate
): IAlarmsRequestUpdate => {
  return {
    name: tdo.name,
    description: tdo.description,
    initial_hour: tdo.initialHour ?? null,
    final_hour: tdo.finalHour ?? null,
    days_week: tdo.daysWeek.map(Number),
    days_retention: tdo.daysRetention ?? null,
    time_confirmation: tdo.timeConfirmation,
    status: tdo.status
  }
}
