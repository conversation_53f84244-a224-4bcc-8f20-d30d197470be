import { IMe } from '@/@core/domain'
import { IHttpClient } from '@/@core/infra/http'

import { LogoutResponse, MeResponse } from './AuthApiV3.types'

export const authApiV3 = (http: IHttpClient) => ({
  logout: async () => {
    const url = '/api/v3/auth/logout'

    const { status, data } = await http.post<LogoutResponse>(url, {})

    return { status, data }
  },
  me: async () => {
    const url = '/api/v3/auth/me'

    const { status, data } = await http.get<MeResponse>(url)

    return {
      status,
      data: meParseRequest(data)
    }
  }
})

export const meParseRequest = (data: MeResponse): IMe => {
  const cognitoUserId = data.cognito_user_id ? data.cognito_user_id : null

  return {
    user: data.user,
    account: data.account,
    accountId: data.account.id,
    accountName: data.account.name,
    accountUserId: data.account_user.id,
    accountUserAdmin: data.account_user.admin,
    notification: !!data.notification,
    cognitoUserId,
    groups: data.groups
  }
}
