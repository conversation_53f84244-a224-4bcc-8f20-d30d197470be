import IDevice from '@/@core/domain/Device'
import {
  Payload,
  Response,
  Tdo,
  TdoSearch,
  TdoStatus
} from './DevicesApiV4.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  equipment: search?.equipment,
  company: search?.company,
  account_id: search?.accountId
})
export const parseResponseDataHelper = (data: Response): IDevice => {
  return {
    id: data.id,
    code: data.code,
    connection: data.connection ?? '',
    urlFirmware: data.url_firmware ?? '',
    iccid: data.iccid ?? '',
    version: data.version ?? '',
    ip: data.ip ?? '',
    alertSeconds: data.alert_seconds,
    ssidOp: data.ssid_op ?? '',
    hourlyFrequency: data.hourly_frequency,
    modelId: data.model?.id ?? null,
    model: data.model ?? null,
    companyId: data.company?.id ?? null,
    company: data.company ?? null,
    equipmentId: data.equipment?.id ?? null,
    equipment: data.equipment ?? null,
    typeId: data.type.id,
    type: data.type,
    subtypeId: data.subtype.id,
    subtype: data.subtype,
    statusId: data.status.id,
    status: data.status,
    masterId: data.master?.id ?? null,
    master: data.master ?? null,
    comment: data.comment ?? null
  }
}
export const parsePayloadHelper = (tdo: Tdo): Payload => ({
  code: tdo.code,
  master_id: tdo.masterId,
  equipment_id: tdo.equipmentId,
  subtype_id: tdo.subtypeId,
  device_model_id: tdo.deviceModelId,
  alert_seconds: tdo.alertSeconds,
  connection: tdo.connection,
  url_firmware: tdo.urlFirmware ?? null,
  version: tdo.version ?? null,
  hourly_frequency: tdo.hourlyFrequency,
  ssid_op: tdo.ssidOp ?? null,
  ip: tdo.ip ?? null,
  iccid: tdo.iccid
  // status_id: tdo.statusId
})
export const parsePayloadChangeStatus = (tdo: TdoStatus) => {
  const status_id = tdo.statusId

  return 'comment' in tdo ? { status_id, comment: tdo.comment } : { status_id }
}
