import { Order } from '@/types/system'

export interface TdoSearch {
  q?: string
  sort?: 'id' | 'code' | 'company' | 'equipment' | 'type'
  order?: Order
  limit?: number
  page?: number
  equipment?: number
  company?: number
  accountId?: number
}
export interface Response {
  id: number
  code: string
  connection: string | null
  url_firmware: string | null
  iccid: string | null
  version: string | null
  ip: string | null
  alert_seconds: number
  current_relation: number
  potential_relation: number
  meter_constant: string
  ssid_op: string | null
  hourly_frequency: number
  model: { id: number; name: string } | null
  equipment: { id: number; name: string } | null
  company: { id: number; name: string } | null
  type: { id: number; name: string }
  subtype: { id: number; name: string }
  status: { id: number; name: string }
  master: { id: number; code: string } | null
  comment: { value: string; date: string } | null
}
export interface Tdo {
  code: string
  masterId: number | null
  equipmentId: number
  subtypeId: number
  deviceModelId: number
  hourlyFrequency: number
  connection: string | null
  urlFirmware: string | null
  iccid: string | null
  ssidOp: string | null
  ip: string | null
  version: string | null
  alertSeconds: number
  statusId: number
}
export interface Payload {
  code: string
  master_id: number | null
  equipment_id: number
  subtype_id: number
  device_model_id: number
  alert_seconds: number
  connection: string | null
  url_firmware: string | null
  version: string | null
  iccid: string | null
  ssid_op: string | null
  ip: string | null
  hourly_frequency: number
  // status_id: number
}

export interface TdoStatus {
  statusId: number
  comment?: string
}
