import { cleanup } from '@testing-library/react'

import { devicesApiV4 } from '@/@core/infra/api/DevicesApiV4'
import { Response } from '@/@core/infra/api/DevicesApiV4/DevicesApiV4.types'
import { http, IHttpResponseList } from '@/@core/infra/http'
import {
  deviceMock1,
  deviceMock2,
  deviceResponseMock1,
  deviceResponseMock2
} from '@/__mock__/content/api-devices.content'

import {
  parsePayloadChangeStatus,
  parsePayloadHelper,
  parseResponseDataHelper,
  parseSearchHelper
} from './DevicesApiV4.hepers'

cleanup()

describe('src/@core/infra/api/DevicesApiV4/DevicesApiV4', () => {
  const responseAllMock: IHttpResponseList<Response> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await devicesApiV4(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(deviceResponseMock1)
    responseAllMock.items.push(deviceResponseMock2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await devicesApiV4(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      equipment: 1,
      accountId: 2,
      company: 3
    })

    expect(status).toEqual(200)
    expect(data.items[0]).toEqual(deviceMock1)
    expect(data.items[1]).toEqual(deviceMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await devicesApiV4(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      equipment: 1,
      accountId: 2,
      company: 3
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    const responseMock = {
      status: 200,
      data: deviceResponseMock1
    }

    http.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await devicesApiV4(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(deviceMock1)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: deviceResponseMock1
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await devicesApiV4(http).create({
      code: '4C752584D5E4_mdb_rtu_108',
      masterId: 1,
      equipmentId: 446,
      subtypeId: 1,
      deviceModelId: 1,
      alertSeconds: 3600,
      connection: 'gsm',
      urlFirmware: '',
      version: '123.456T7',
      iccid: '',
      ssidOp: 'Claro',
      statusId: 2,
      ip: '',
      hourlyFrequency: 1
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(deviceMock1)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: deviceResponseMock1
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await devicesApiV4(http).update(1, {
      code: '4C752584D5E4_mdb_rtu_108',
      masterId: 1,
      equipmentId: 446,
      subtypeId: 1,
      deviceModelId: 1,
      alertSeconds: 3600,
      connection: 'gsm',
      urlFirmware: '',
      version: '123.456T7',
      iccid: '',
      ssidOp: 'Claro',
      statusId: 2,
      ip: '',
      hourlyFrequency: 1
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(deviceMock1)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.delete = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await devicesApiV4(http).delete(1)

    expect(result.status).toBe(204)
  })

  test('request changeStatus', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result1 = await devicesApiV4(http).changeStatus(1, { statusId: 2 })

    expect(result1.status).toBe(204)

    http.post = jest.fn().mockImplementationOnce(() => ({
      status: 204,
      data: null
    }))
  })
})

describe('src/@core/infra/api/DevicesApiV4/DevicesApiV4 | helpers', () => {
  it('check return helper parseSearchHelper', () => {
    expect(
      parseSearchHelper({
        q: 'Test',
        sort: 'id',
        order: 'asc',
        limit: 15,
        page: 1,
        equipment: 1,
        company: 2,
        accountId: 3
      })
    ).toEqual({
      _q: 'Test',
      _sort: 'id',
      _order: 'asc',
      _limit: 15,
      _page: 1,
      equipment: 1,
      company: 2,
      account_id: 3
    })
  })

  it('check return helper parseResponseDataHelper full params', () => {
    expect(
      parseResponseDataHelper({
        id: 1,
        code: '000_water_532',
        connection: 'connection',
        url_firmware: 'url_firmware',
        iccid: 'iccid',
        version: 'version',
        ip: 'ip',
        alert_seconds: 3600,
        current_relation: 1,
        potential_relation: 1,
        meter_constant: 'meter_constant',
        ssid_op: 'ssid_op',
        hourly_frequency: 4,
        model: { id: 4, name: 'ITC-100' },
        equipment: { id: 546, name: 'Tópico físico' },
        company: { id: 144, name: 'Empresa Métricas' },
        type: { id: 7, name: 'Tópico Físico' },
        subtype: { id: 24, name: 'Tópico Físico' },
        status: { id: 6, name: 'Stand By' },
        master: { id: 512, code: '000' },
        comment: { value: 'teste', date: '2024-02-02 15:10:50' }
      })
    ).toEqual({
      id: 1,
      code: '000_water_532',
      connection: 'connection',
      urlFirmware: 'url_firmware',
      iccid: 'iccid',
      version: 'version',
      ip: 'ip',
      alertSeconds: 3600,
      ssidOp: 'ssid_op',
      hourlyFrequency: 4,
      modelId: 4,
      model: { id: 4, name: 'ITC-100' },
      companyId: 144,
      company: { id: 144, name: 'Empresa Métricas' },
      equipmentId: 546,
      equipment: { id: 546, name: 'Tópico físico' },
      typeId: 7,
      type: { id: 7, name: 'Tópico Físico' },
      subtypeId: 24,
      subtype: { id: 24, name: 'Tópico Físico' },
      statusId: 6,
      status: { id: 6, name: 'Stand By' },
      masterId: 512,
      master: { id: 512, code: '000' },
      comment: { value: 'teste', date: '2024-02-02 15:10:50' }
    })
  })

  it('check return helper parseResponseDataHelper optional params', () => {
    expect(
      parseResponseDataHelper({
        id: 1,
        code: '000_water_532',
        connection: null,
        url_firmware: null,
        iccid: null,
        version: null,
        ip: null,
        alert_seconds: 3600,
        current_relation: 1,
        potential_relation: 1,
        meter_constant: 'meter_constant',
        ssid_op: null,
        hourly_frequency: 4,
        model: null,
        equipment: null,
        company: null,
        type: { id: 7, name: 'Tópico Físico' },
        subtype: { id: 24, name: 'Tópico Físico' },
        status: { id: 6, name: 'Stand By' },
        master: null,
        comment: null
      })
    ).toEqual({
      id: 1,
      code: '000_water_532',
      connection: '',
      urlFirmware: '',
      iccid: '',
      version: '',
      ip: '',
      alertSeconds: 3600,
      ssidOp: '',
      hourlyFrequency: 4,
      modelId: null,
      model: null,
      equipmentId: null,
      equipment: null,
      companyId: null,
      company: null,
      typeId: 7,
      type: { id: 7, name: 'Tópico Físico' },
      subtypeId: 24,
      subtype: { id: 24, name: 'Tópico Físico' },
      statusId: 6,
      status: { id: 6, name: 'Stand By' },
      masterId: null,
      master: null,
      comment: null
    })
  })

  it('check return helper parsePayloadHelper full params', () => {
    expect(
      parsePayloadHelper({
        code: '000_water_532',
        masterId: 512,
        equipmentId: 546,
        subtypeId: 24,
        deviceModelId: 1,
        hourlyFrequency: 4,
        connection: 'connection',
        urlFirmware: 'url_firmware',
        iccid: 'iccid',
        ssidOp: 'ssid_op',
        ip: '***********',
        version: 'version',
        alertSeconds: 3600,
        statusId: 6
      })
    ).toEqual({
      code: '000_water_532',
      master_id: 512,
      equipment_id: 546,
      subtype_id: 24,
      device_model_id: 1,
      alert_seconds: 3600,
      connection: 'connection',
      url_firmware: 'url_firmware',
      version: 'version',
      hourly_frequency: 4,
      ssid_op: 'ssid_op',
      ip: '***********',
      iccid: 'iccid'
    })
  })

  it('check return helper parsePayloadHelper optional params', () => {
    expect(
      parsePayloadHelper({
        code: '000_water_532',
        masterId: null,
        equipmentId: 546,
        subtypeId: 24,
        deviceModelId: 1,
        hourlyFrequency: 4,
        connection: null,
        urlFirmware: null,
        iccid: null,
        ssidOp: null,
        ip: null,
        version: null,
        alertSeconds: 3600,
        statusId: 6
      })
    ).toEqual({
      code: '000_water_532',
      master_id: null,
      equipment_id: 546,
      subtype_id: 24,
      device_model_id: 1,
      alert_seconds: 3600,
      connection: null,
      url_firmware: null,
      version: null,
      hourly_frequency: 4,
      ssid_op: null,
      ip: null,
      iccid: null
    })
  })

  it('check return helper parsePayloadChangeStatus full params', () => {
    expect(
      parsePayloadChangeStatus({
        statusId: 4,
        comment: 'comment ...'
      })
    ).toEqual({
      status_id: 4,
      comment: 'comment ...'
    })
  })

  it('check return helper parsePayloadChangeStatus optional params', () => {
    expect(
      parsePayloadChangeStatus({
        statusId: 4
      })
    ).toEqual({
      status_id: 4
    })
  })
})
