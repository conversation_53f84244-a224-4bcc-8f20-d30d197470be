import { redirect<PERSON>oginCognito } from '@/@core/utils/redirectLoginCognito'
import axios from 'axios'
import { memoryApp } from '../memory'
import { Http } from './http'

// Mock dependencies
jest.mock('axios', () => ({
  create: jest.fn().mockReturnThis(),
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  interceptors: {
    response: {
      use: jest.fn()
    }
  }
}))

jest.mock('../memory', () => ({
  memoryApp: {
    down: jest.fn()
  }
}))

jest.mock('@/@core/utils/redirectLoginCognito', () => ({
  redirectLoginCognito: jest.fn(() => 'http://fake-login-url.com')
}))

const mockedAxios = axios as jest.Mocked<typeof axios>

describe('Http', () => {
  let http: Http
  const originalLocation = window.location

  beforeAll(() => {
    // Mock window.location
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { ...originalLocation, href: '' }
    })
  })

  beforeEach(() => {
    jest.clearAllMocks()
    mockedAxios.create.mockReturnThis()
    http = new Http()
  })

  afterAll(() => {
    // Restore original window.location
    Object.defineProperty(window, 'location', {
      writable: true,
      value: originalLocation
    })
  })

  it('should create an axios instance with default configurations', () => {
    expect(mockedAxios.create).toHaveBeenCalledWith({
      baseURL: process.env.NEXT_PUBLIC_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json'
      },
      signal: undefined
    })
    expect(mockedAxios.interceptors.response.use).toHaveBeenCalledWith(
      expect.any(Function),
      expect.any(Function)
    )
  })

  it('should set the authorization token and recreate the instance', () => {
    const token = 'test-token'
    http.setToken(token)

    expect(mockedAxios.create).toHaveBeenCalledTimes(2)
    expect(mockedAxios.create).toHaveBeenLastCalledWith(
      expect.objectContaining({
        headers: expect.objectContaining({
          Authorization: `Bearer ${token}`
        })
      })
    )
  })

  it('should remove the authorization token and recreate the instance', () => {
    http.setToken('some-token')
    http.setToken('')

    const lastCallConfig = mockedAxios.create.mock.calls.pop()?.[0]
    expect(lastCallConfig?.headers).not.toHaveProperty('Authorization')
    expect(mockedAxios.create).toHaveBeenCalledTimes(2)
  })

  it('should set the signal and recreate the instance', () => {
    const abortController = new AbortController()
    const signal = abortController.signal
    http.setSignal(signal)

    expect(mockedAxios.create).toHaveBeenCalledTimes(2)
    expect(mockedAxios.create).toHaveBeenLastCalledWith(
      expect.objectContaining({
        signal: signal
      })
    )
  })

  it('should call axios.get with the correct parameters', async () => {
    const url = '/test-get'
    const config = { headers: { 'X-Test': 'true' } }
    await http.get(url, config)
    expect(mockedAxios.get).toHaveBeenCalledWith(url, config)
  })

  it('should call axios.post with the correct parameters', async () => {
    const url = '/test-post'
    const data = { name: 'test' }
    const config = { headers: { 'X-Test': 'true' } }
    await http.post(url, data, config)
    expect(mockedAxios.post).toHaveBeenCalledWith(url, data, config)
  })

  it('should call axios.put with the correct parameters', async () => {
    const url = '/test-put'
    const data = { name: 'test' }
    const config = { headers: { 'X-Test': 'true' } }
    await http.put(url, data, config)
    expect(mockedAxios.put).toHaveBeenCalledWith(url, data, config)
  })

  it('should call axios.delete with the correct parameters', async () => {
    const url = '/test-delete'
    const data = { id: 1 }
    await http.delete(url, data)
    expect(mockedAxios.delete).toHaveBeenCalledWith(url, { data })
  })

  describe('Response Interceptor', () => {
    it('should handle 401 error by redirecting to login', async () => {
      const error = {
        response: { status: 401 }
      }
      const errorHandler = (mockedAxios.interceptors.response.use as jest.Mock)
        .mock.calls[0][1]

      await expect(errorHandler(error)).rejects.toEqual(
        new Error(JSON.stringify(error))
      )

      expect(memoryApp.down).toHaveBeenCalled()
      expect(redirectLoginCognito).toHaveBeenCalled()
      expect(window.location.href).toBe('http://fake-login-url.com')
    })

    it('should not redirect on other errors', async () => {
      const error = {
        response: { status: 500 }
      }
      const errorHandler = (mockedAxios.interceptors.response.use as jest.Mock)
        .mock.calls[0][1]

      await expect(errorHandler(error)).rejects.toEqual(
        new Error(JSON.stringify(error))
      )

      expect(memoryApp.down).not.toHaveBeenCalled()
      expect(redirectLoginCognito).not.toHaveBeenCalled()
    })

    it('should pass through successful responses', () => {
      const response = { data: 'success' }
      const successHandler = (
        mockedAxios.interceptors.response.use as jest.Mock
      ).mock.calls[0][0]

      expect(successHandler(response)).toBe(response)
    })
  })
  it('should call axios.get with an empty config object if none is provided', async () => {
    const url = '/test-get'
    await http.get(url)
    expect(mockedAxios.get).toHaveBeenCalledWith(url, {})
  })

  it('should call axios.get with an empty config object if none is provided', async () => {
    const url = '/test-get'
    await http.get(url)
    expect(mockedAxios.get).toHaveBeenCalledWith(url, {})
  })

  it('should call axios.post with an empty config object if none is provided', async () => {
    const url = '/test-post'
    const data = { name: 'test' }
    await http.post(url, data)
    expect(mockedAxios.post).toHaveBeenCalledWith(url, data, {})
  })

  it('should call axios.put with an empty config object if none is provided', async () => {
    const url = '/test-put'
    const data = { name: 'test' }
    await http.put(url, data)
    expect(mockedAxios.put).toHaveBeenCalledWith(url, data, {})
  })

  it('should call axios.delete without data if no input is provided', async () => {
    const url = '/test-delete'
    await http.delete(url)
    expect(mockedAxios.delete).toHaveBeenCalledWith(url, { data: undefined })
  })
})
