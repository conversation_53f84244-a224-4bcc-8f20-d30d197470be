import axios, {
  AxiosInstance,
  CreateAxiosDefaults,
  GenericAbortSignal
} from 'axios'

import { IHttpClient } from '@/@core/infra/http'
import { redirectLoginCognito } from '@/@core/utils/redirectLoginCognito'
import { memoryApp } from '../memory'

export class Http implements IHttpClient {
  private request: AxiosInstance
  private requestDefaults: Partial<
    Omit<CreateAxiosDefaults, 'headers'> & {
      headers: Record<string, string>
    }
  >

  constructor() {
    this.requestDefaults = {
      baseURL: process.env.NEXT_PUBLIC_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json'
      },
      signal: undefined
    }

    this.createInstance()
  }

  private createInstance() {
    this.request = axios.create({ ...this.requestDefaults })

    this.request.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          memoryApp.down()
          window.location.href = redirectLoginCognito()
        }
        return Promise.reject(
          error instanceof Error ? error : new Error(JSON.stringify(error))
        )
      }
    )
  }
  public setToken(token: string) {
    if (token) {
      this.requestDefaults = {
        ...this.requestDefaults,
        headers: {
          ...this.requestDefaults?.headers,
          Authorization: `Bearer ${token}`
        }
      }
    } else {
      delete this.requestDefaults.headers?.['Authorization']
    }

    this.createInstance()
  }
  public setSignal(currentSignal: GenericAbortSignal | undefined) {
    this.requestDefaults = {
      ...this.requestDefaults,
      signal: currentSignal
    }

    this.createInstance()
  }

  get<R>(url: string, config = {}) {
    return this.request.get<R>(url, config)
  }
  post<T, R>(url: string, input: T, config = {}) {
    return this.request.post<R>(url, input, config)
  }
  put<T>(url: string, input: T, config = {}) {
    return this.request.put(url, input, config)
  }
  delete(url: string, input?: unknown) {
    return this.request.delete(url, { data: input })
  }
}

export const http = new Http()
