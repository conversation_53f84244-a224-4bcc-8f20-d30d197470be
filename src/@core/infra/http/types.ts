import { AxiosRequestConfig } from 'axios'

export interface IHttpResponseList<T> {
  items: T[]
  current_page: number
  last_page: number
  per_page: number
  total: number
}
export interface IHttpResponseItems<T> {
  items: T[]
}

export interface IHttpResponseData<T> {
  data: T[]
  current_page?: number
  per_page?: number
  total?: number
  last_page?: number
}

export type IHttpClientConfig = AxiosRequestConfig
export interface IHttpClient {
  get: <R>(
    url: string,
    config?: IHttpClientConfig
  ) => Promise<{ data: R; status: number }>
  post: <R>(
    url: string,
    input: unknown,
    config?: IHttpClientConfig
  ) => Promise<{ data: R; status: number }>
  put: <R>(
    url: string,
    input: unknown,
    config?: IHttpClientConfig
  ) => Promise<{ data: R; status: number }>
  delete: (url: string, input?: unknown) => Promise<{ status: number }>
}

export interface DataSSR<T> {
  props: T
  redirect?: {
    destination: string
    permanent: false
  }
}
