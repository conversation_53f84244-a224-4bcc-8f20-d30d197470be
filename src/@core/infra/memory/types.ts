import { IAuthModuleData } from './cookie/_modules/auth'
import { IApportionmentModalModuleData } from './localStorage/_modules/component.apportionmentModal'
import { ICostCenterModalModuleData } from './localStorage/_modules/component.costCenterModal'
import { IAlarmsNotificationsChannelsListModuleData } from './localStorage/_modules/list.alarmsNotificationsChannels'
import { IAlarmsOperatorLogicListModuleData } from './localStorage/_modules/list.alarmsOperatorLogic'
import { IAlarmsRulesListModuleData } from './localStorage/_modules/list.alarmsRules'
import { IAlarmsTargetsListModuleData } from './localStorage/_modules/list.alarmsTargets'
import { IAlarmsTargetsTypesListModuleData } from './localStorage/_modules/list.alarmsTargetsType'
import { IApportionmentMeasuresUnitsListModuleData } from './localStorage/_modules/list.apportionmentMeasuresUnits'
import { IApportionmentsFeeDivisionTypesListModuleData } from './localStorage/_modules/list.apportionmentsFeeDivisionTypes'
import { IApportionmentsTariffTypesListModuleData } from './localStorage/_modules/list.apportionmentsTariffTypes'
import { IApportionmentsTypesListModuleData } from './localStorage/_modules/list.apportionmentsTypes'
import { ICitiesListModuleData } from './localStorage/_modules/list.cities'
import { ICompaniesTypesListModuleData } from './localStorage/_modules/list.companiesTypes'
import { ICompaniesCategoriesListModuleData } from './localStorage/_modules/list.companyCategories'
import { ICostCompositionTypesListModuleData } from './localStorage/_modules/list.costCompositionTypes'
import { IDeviceInstallationFieldListModuleData } from './localStorage/_modules/list.deviceInstallationField'
import { IDeviceModelListModuleData } from './localStorage/_modules/list.deviceModal'
import { IExternalCodeListModuleData } from './localStorage/_modules/list.externalCode'
import { IOperationalRuleListModuleData } from './localStorage/_modules/list.operationsRule'
import { IStatesListModuleData } from './localStorage/_modules/list.states'
import { ISubmarketListModuleData } from './localStorage/_modules/list.submarket'
import { ISubTypesModuleData } from './localStorage/_modules/list.subTypes'
import { ITypesModuleData } from './localStorage/_modules/list.type'
import { ITypeSubTypeGroupedModuleData } from './localStorage/_modules/list.typeSubTypeGrouped'
import { IAccountsModuleData } from './localStorage/_modules/page.accounts'
import { IAlarmsModuleData } from './localStorage/_modules/page.alarms'
import { IApportionmentsModuleData } from './localStorage/_modules/page.apportionments'
import { ICompaniesModuleData } from './localStorage/_modules/page.companies'
import { IDashboardModuleData } from './localStorage/_modules/page.dashboard'
import { IDevicesModuleData } from './localStorage/_modules/page.devices'
import { IDistributorsModuleData } from './localStorage/_modules/page.distributors'
import { IEquipmentsModuleData } from './localStorage/_modules/page.equipments'
import { IInstallationsDocumentModuleData } from './localStorage/_modules/page.installationsDocument'
import { IIntegrationsModuleData } from './localStorage/_modules/page.integrations'
import { IMonitoringModuleData } from './localStorage/_modules/page.monitoring'
import { IPropertiesModuleData } from './localStorage/_modules/page.properties'
import { IReportsModuleData } from './localStorage/_modules/page.reports'
import { IStatisticsModuleData } from './localStorage/_modules/page.statistics'
import { IUsersModuleData } from './localStorage/_modules/page.users'
import { ISystemModuleData } from './localStorage/_modules/system'

export type ICookieKeyData = {
  auth: IAuthModuleData
}
export type ILocalKeyData = {
  /** components */
  apportionmentModal: IApportionmentModalModuleData
  costCenterModal: ICostCenterModalModuleData
  /** list */
  alarmsNotificationsChannelsList: IAlarmsNotificationsChannelsListModuleData
  alarmsOperatorLogicList: IAlarmsOperatorLogicListModuleData
  alarmsRulesList: IAlarmsRulesListModuleData
  alarmsTargetsList: IAlarmsTargetsListModuleData
  alarmsTargetsTypesList: IAlarmsTargetsTypesListModuleData
  apportionmentMeasuresUnitsList: IApportionmentMeasuresUnitsListModuleData
  apportionmentsFeeDivisionTypesList: IApportionmentsFeeDivisionTypesListModuleData
  apportionmentsTariffTypesList: IApportionmentsTariffTypesListModuleData
  apportionmentsTypesList: IApportionmentsTypesListModuleData
  citiesList: ICitiesListModuleData
  companiesTypesList: ICompaniesTypesListModuleData
  companiesCategoriesList: ICompaniesCategoriesListModuleData
  costCompositionTypesList: ICostCompositionTypesListModuleData
  deviceInstallationFieldList: IDeviceInstallationFieldListModuleData
  deviceModelList: IDeviceModelListModuleData
  externalCodeList: IExternalCodeListModuleData
  operationalRuleList: IOperationalRuleListModuleData
  statesList: IStatesListModuleData
  submarketList: ISubmarketListModuleData
  subTypesList: ISubTypesModuleData
  typeList: ITypesModuleData
  typeSubTypeGroupedList: ITypeSubTypeGroupedModuleData
  /** pages */
  accounts: IAccountsModuleData
  alarms: IAlarmsModuleData
  apportionments: IApportionmentsModuleData
  companies: ICompaniesModuleData
  dashboard: IDashboardModuleData
  devices: IDevicesModuleData
  distributors: IDistributorsModuleData
  equipments: IEquipmentsModuleData
  installationsDocument: IInstallationsDocumentModuleData
  integrations: IIntegrationsModuleData
  monitoring: IMonitoringModuleData
  properties: IPropertiesModuleData
  reports: IReportsModuleData
  statistics: IStatisticsModuleData
  users: IUsersModuleData
  system: ISystemModuleData
}
