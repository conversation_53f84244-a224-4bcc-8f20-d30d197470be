import { mergeDeep } from '@/@core/utils/mergeDeep'
import { DeepPartial } from '@/types/system'
import {
  MemoryCookiesMap,
  memoryCookiesMap,
  MemoryLocalStorageMap,
  memoryLocalStorageMap
} from './content'
import { MemoryAbstract } from './memoryAbstract'
import { ICookieKeyData, ILocalKeyData } from './types'

const Memory = () => {
  const _get = <T>(
    memoryObj: Record<string, MemoryAbstract<Partial<unknown>>>
  ) => {
    const _data = {} as Record<string, Partial<unknown> | null>

    Object.keys(memoryObj).forEach((_key) => {
      const _module = memoryObj[_key]
      _data[_key] = _module.get()
    })
    return _data as T
  }
  const _set = (
    value: DeepPartial<Record<string, unknown>>,
    mapData: Record<string, MemoryAbstract<Partial<unknown>>>
  ) => {
    Object.keys(mapData).forEach((key) => {
      const oldData = mapData[key].get() as {}
      const newData = value[key] as {}
      mapData[key].set(mergeDeep(oldData, newData))
    })
  }
  const _base = <D, R extends string>(
    memoryMap: Record<R, MemoryAbstract<Partial<unknown>>>
  ) => {
    return {
      get: () => {
        return _get<D>(memoryMap)
      },
      set: (values: DeepPartial<D>) => {
        _set(values, memoryMap)
      },
      reset: (arr?: R | R[]) => {
        let _names = Object.keys(memoryMap) as R[]

        if (arr) _names = Array.isArray(arr) ? arr : [arr]

        _names.forEach((moduleName) => {
          const _module = memoryMap[moduleName]
          _module.set(_module.initialData)
        })
      }
    }
  }

  const cookie = _base<ICookieKeyData, MemoryCookiesMap>(memoryCookiesMap)

  const local = _base<ILocalKeyData, MemoryLocalStorageMap>(
    memoryLocalStorageMap
  )

  return { cookie, local }
}

const MemoryApp = () => {
  const init = () => {
    Array.from([memoryCookiesMap, memoryLocalStorageMap]).forEach(
      (memoryMap) => {
        Object.keys(memoryMap).forEach((key) => {
          const _key = key as keyof typeof memoryMap
          const _module = memoryMap[_key] as MemoryAbstract<Partial<unknown>>
          const oldData = _module.get() as {}
          const newData = _module.initialData as {}
          _module.set(mergeDeep(oldData, newData))
        })
      }
    )
  }
  const down = () => {
    Array.from([memoryCookiesMap, memoryLocalStorageMap]).forEach(
      (memoryMap) => {
        Object.keys(memoryMap).forEach((key) => {
          const _key = key as keyof typeof memoryMap
          const _module = memoryMap[_key] as MemoryAbstract<Partial<unknown>>
          _module.destroy()
        })
      }
    )
  }
  return { init, down }
}

const memory = Memory()
const memoryApp = MemoryApp()

export { memory, memoryApp }
