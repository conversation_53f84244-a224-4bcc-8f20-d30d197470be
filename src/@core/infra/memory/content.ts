import AuthModule from './cookie/_modules/auth'
import CookieEstrategy from './cookie/extrategy'

import ApportionmentModalModule from './localStorage/_modules/component.apportionmentModal'
import CostCenterModalModule from './localStorage/_modules/component.costCenterModal'
import AlarmsNotificationsChannelsListModule from './localStorage/_modules/list.alarmsNotificationsChannels'
import AlarmsOperatorLogicListModule from './localStorage/_modules/list.alarmsOperatorLogic'
import AlarmsRulesListModule from './localStorage/_modules/list.alarmsRules'
import AlarmsTargetsListModule from './localStorage/_modules/list.alarmsTargets'
import AlarmsTargetsTypesListModule from './localStorage/_modules/list.alarmsTargetsType'
import ApportionmentMeasuresUnitsListModule from './localStorage/_modules/list.apportionmentMeasuresUnits'
import ApportionmentsFeeDivisionTypesListModule from './localStorage/_modules/list.apportionmentsFeeDivisionTypes'
import ApportionmentsTariffTypesListModule from './localStorage/_modules/list.apportionmentsTariffTypes'
import ApportionmentsTypesListModule from './localStorage/_modules/list.apportionmentsTypes'
import CitiesListModule from './localStorage/_modules/list.cities'
import CompaniesTypesListModule from './localStorage/_modules/list.companiesTypes'
import CompaniesCategoriesListModule from './localStorage/_modules/list.companyCategories'
import CostCompositionTypesListModule from './localStorage/_modules/list.costCompositionTypes'
import DeviceInstallationFieldListModule from './localStorage/_modules/list.deviceInstallationField'
import DeviceModelListModule from './localStorage/_modules/list.deviceModal'
import ExternalCodeListModule from './localStorage/_modules/list.externalCode'
import OperationalRuleListModule from './localStorage/_modules/list.operationsRule'
import StatesListModule from './localStorage/_modules/list.states'
import SubmarketListModule from './localStorage/_modules/list.submarket'
import SubTypesModule from './localStorage/_modules/list.subTypes'
import TypesModule from './localStorage/_modules/list.type'
import TypeSubTypeGroupedModule from './localStorage/_modules/list.typeSubTypeGrouped'
import AccountsModule from './localStorage/_modules/page.accounts'
import AlarmsModule from './localStorage/_modules/page.alarms'
import ApportionmentsModule from './localStorage/_modules/page.apportionments'
import CompaniesModule from './localStorage/_modules/page.companies'
import DashboardModule from './localStorage/_modules/page.dashboard'
import DevicesModule from './localStorage/_modules/page.devices'
import DistributorsModule from './localStorage/_modules/page.distributors'
import EquipmentsModule from './localStorage/_modules/page.equipments'
import InstallationsDocumentModule from './localStorage/_modules/page.installationsDocument'
import IntegrationsModule from './localStorage/_modules/page.integrations'
import MonitoringModule from './localStorage/_modules/page.monitoring'
import PropertiesModule from './localStorage/_modules/page.properties'
import ReportsModule from './localStorage/_modules/page.reports'
import StatisticsModule from './localStorage/_modules/page.statistics'
import UsersModule from './localStorage/_modules/page.users'
import SystemModule from './localStorage/_modules/system'
import LocalStorageEstrategy from './localStorage/extrategy'

/** memoryCookiesMap */
const cookieEstrategy = new CookieEstrategy()

export const memoryCookiesMap = {
  auth: new AuthModule(cookieEstrategy)
}
export type MemoryCookiesMap = keyof typeof memoryCookiesMap

/** memoryLocalStorageMap */
const localStorageEstrategy = new LocalStorageEstrategy()

export const memoryLocalStorageMap = {
  /** components */
  apportionmentModal: new ApportionmentModalModule(localStorageEstrategy),
  costCenterModal: new CostCenterModalModule(localStorageEstrategy),
  /** list */
  alarmsNotificationsChannelsList: new AlarmsNotificationsChannelsListModule(
    localStorageEstrategy
  ),
  alarmsOperatorLogicList: new AlarmsOperatorLogicListModule(
    localStorageEstrategy
  ),
  alarmsRulesList: new AlarmsRulesListModule(localStorageEstrategy),
  alarmsTargetsList: new AlarmsTargetsListModule(localStorageEstrategy),
  alarmsTargetsTypesList: new AlarmsTargetsTypesListModule(
    localStorageEstrategy
  ),
  apportionmentMeasuresUnitsList: new ApportionmentMeasuresUnitsListModule(
    localStorageEstrategy
  ),
  apportionmentsFeeDivisionTypesList:
    new ApportionmentsFeeDivisionTypesListModule(localStorageEstrategy),
  apportionmentsTariffTypesList: new ApportionmentsTariffTypesListModule(
    localStorageEstrategy
  ),
  apportionmentsTypesList: new ApportionmentsTypesListModule(
    localStorageEstrategy
  ),
  citiesList: new CitiesListModule(localStorageEstrategy),
  companiesTypesList: new CompaniesTypesListModule(localStorageEstrategy),
  companiesCategoriesList: new CompaniesCategoriesListModule(
    localStorageEstrategy
  ),
  costCompositionTypesList: new CostCompositionTypesListModule(
    localStorageEstrategy
  ),
  deviceInstallationFieldList: new DeviceInstallationFieldListModule(
    localStorageEstrategy
  ),
  deviceModelList: new DeviceModelListModule(localStorageEstrategy),
  externalCodeList: new ExternalCodeListModule(localStorageEstrategy),
  operationalRuleList: new OperationalRuleListModule(localStorageEstrategy),
  statesList: new StatesListModule(localStorageEstrategy),
  submarketList: new SubmarketListModule(localStorageEstrategy),
  subTypesList: new SubTypesModule(localStorageEstrategy),
  typeList: new TypesModule(localStorageEstrategy),
  typeSubTypeGroupedList: new TypeSubTypeGroupedModule(localStorageEstrategy),
  /** pages */
  accounts: new AccountsModule(localStorageEstrategy),
  alarms: new AlarmsModule(localStorageEstrategy),
  apportionments: new ApportionmentsModule(localStorageEstrategy),
  companies: new CompaniesModule(localStorageEstrategy),
  dashboard: new DashboardModule(localStorageEstrategy),
  devices: new DevicesModule(localStorageEstrategy),
  distributors: new DistributorsModule(localStorageEstrategy),
  equipments: new EquipmentsModule(localStorageEstrategy),
  installationsDocument: new InstallationsDocumentModule(localStorageEstrategy),
  integrations: new IntegrationsModule(localStorageEstrategy),
  monitoring: new MonitoringModule(localStorageEstrategy),
  properties: new PropertiesModule(localStorageEstrategy),
  reports: new ReportsModule(localStorageEstrategy),
  statistics: new StatisticsModule(localStorageEstrategy),
  users: new UsersModule(localStorageEstrategy),
  system: new SystemModule(localStorageEstrategy)
}
export type MemoryLocalStorageMap = keyof typeof memoryLocalStorageMap
