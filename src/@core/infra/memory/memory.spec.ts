import { memory, memoryApp } from './memory'

describe('@core/infra/memory/memory', () => {
  beforeEach(() => memoryApp.init())

  it('check the return using GET of CookieEstrategy', () => {
    /** GET values */
    const dataResult = {
      token: null,
      userId: null
    }
    expect(memory.cookie.get().auth).toEqual(dataResult)
  })

  it('check the return using SET of CookieEstrategy', () => {
    expect(memory.cookie.get().auth?.token).toBeNull()

    /** SET values */
    memory.cookie.set({
      auth: {
        token: String(process.env.NEXT_PUBLIC_TEST_ACCESS_TOKEN),
        userId: null
      }
    })

    expect(memory.cookie.get().auth?.token).toBe(
      process.env.NEXT_PUBLIC_TEST_ACCESS_TOKEN
    )
  })

  it('check the return using GET of LocalStorageEstrategy', () => {
    const dataResult = {
      menu: {
        open: null,
        itemActive: null,
        inputSearch: null,
        inputAccount: null,
        menuFixed: null,
        isDesktopMenuOpen: null
      },
      page: {
        tab: null,
        period: { final: null, initial: null }
      }
    }
    expect(memory.local.get().dashboard).toEqual(dataResult)
  })

  it('check the return using SET of LocalStorageEstrategy', () => {
    expect(memory.local.get().dashboard?.menu?.open).toBeFalsy()

    /** SET values */
    memory.local.set({
      dashboard: {
        menu: {
          open: true
        }
      }
    })

    expect(memory.local.get().dashboard?.menu?.open).toBeTruthy()
  })

  it('check the return using RESET of LocalStorageEstrategy', () => {
    expect(memory.local.get().dashboard?.menu?.open).toBeFalsy()

    /** RESET single argumento */
    memory.local.set({
      dashboard: {
        menu: {
          open: true
        }
      }
    })
    expect(memory.local.get().dashboard?.menu?.open).toBeTruthy()

    memory.local.reset('dashboard')

    expect(memory.local.get().dashboard?.menu?.open).toBeFalsy()

    /** RESET multiple argumento */
    memory.local.set({
      dashboard: {
        menu: {
          open: true
        }
      },
      accounts: {
        listing: { search: { page: 2 } }
      }
    })
    expect(memory.local.get().dashboard?.menu?.open).toBeTruthy()
    expect(memory.local.get().accounts.listing.search.page).toBe(2)

    memory.local.reset(['dashboard', 'accounts'])

    expect(memory.local.get().dashboard?.menu?.open).toBeFalsy()
    expect(memory.local.get().accounts.listing.search.page).toBe(1)

    /** RESET without argumento */
    memory.local.set({
      dashboard: {
        menu: {
          open: true
        }
      },
      accounts: {
        listing: { search: { page: 2 } }
      }
    })
    expect(memory.local.get().dashboard?.menu?.open).toBeTruthy()
    expect(memory.local.get().accounts.listing.search.page).toBe(2)

    memory.local.reset()

    expect(memory.local.get().dashboard?.menu?.open).toBeFalsy()
    expect(memory.local.get().accounts.listing.search.page).toBe(1)
  })
})
