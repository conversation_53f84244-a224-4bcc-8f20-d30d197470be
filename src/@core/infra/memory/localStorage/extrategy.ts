import MemoryEstrategyInterface from '../memoryEstrategyInterface'

export default class LocalStorageEstrategy<T>
  implements MemoryEstrategyInterface
{
  get(key: string): Partial<T> | null {
    const data = window.localStorage.getItem(key)
    return data ? (JSON.parse(data) as T) : {}
  }
  set(key: string, payload: Partial<T>) {
    // try {
    window.localStorage.setItem(key, JSON.stringify(payload))
    // } catch (error) {
    //   console.log('error', { key, payload })
    // }
  }
  destroy(key: string) {
    window.localStorage.removeItem(key)
  }
}
