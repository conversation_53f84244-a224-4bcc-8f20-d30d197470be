import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IMonitoringModuleData {
  listing: {
    search: {
      q: string
      order: Order
      sort: 'equipamento_id' | 'status_id' | 'last_message'
      page: number
      limit: number
      statusId?: number
      subtypeId?: number
      typeId?: number
    }
    modalMeasurementCount: {
      q: string
      page: number
      limit: number
      statusId?: number
      subtypeId?: number
      typeId?: number
    }
  }
}

class MonitoringModule extends MemoryAbstract<IMonitoringModuleData> {
  key = 'memory.monitoring'

  initialData: IMonitoringModuleData = {
    listing: {
      search: {
        q: '',
        sort: 'equipamento_id',
        order: 'desc',
        limit: 15,
        page: 1,
      },
      modalMeasurementCount: {
        q: '',
        page: 1,
        limit: 15
      }
    },
  }
}

export type { IMonitoringModuleData }

export default MonitoringModule