import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IInstallationsDocumentModuleData {
  listing: {
    search: {
      q: string
      order: string
      sort: string
      page: number
      limit: number
      company?: { id: number; name: string }[]
      status: string
    }

  }
}

class InstallationsDocumentModule extends MemoryAbstract<IInstallationsDocumentModuleData> {
  key = 'memory.installationsDocument'

  initialData: IInstallationsDocumentModuleData = {
    listing: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15,
        company: [],
        status: ''
      },

    },

  }
}

export type { IInstallationsDocumentModuleData }

export default InstallationsDocumentModule