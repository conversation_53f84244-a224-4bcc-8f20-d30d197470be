import { IApportionmentTypes } from '@/@core/domain/ApportionmentTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IApportionmentsTypesListModuleData {
  list: IApportionmentTypes[]
}

class ApportionmentsTypesListModule extends MemoryAbstract<IApportionmentsTypesListModuleData> {
  key = 'memory.list.apportionmentsTypes'

  initialData: IApportionmentsTypesListModuleData = {
    list: []
  }
}

export type { IApportionmentsTypesListModuleData }

export default ApportionmentsTypesListModule