import { IListExternalCodes } from '@/@core/domain/ListExternalCodes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IExternalCodeListModuleData {
  list: IListExternalCodes
}

class ExternalCodeListModule extends MemoryAbstract<IExternalCodeListModuleData> {
  key = 'memory.list.externalCode'

  initialData: IExternalCodeListModuleData = {
    list: {
      comerc: '',
      zordon: ''
    }
  }
}

export type { IExternalCodeListModuleData }

export default ExternalCodeListModule