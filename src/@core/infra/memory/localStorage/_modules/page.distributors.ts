import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IDistributorsModuleData {
  listing: {
    search: {
      q: string
      sort: 'id'
      order: Order
      limit: number
      page: number
      typeId?: number
    }
  }
  record: {
    tabTariffs: {
      page?: number
      limit?: number
    }
  }
}

class DistributorsModule extends MemoryAbstract<IDistributorsModuleData> {
  key = 'memory.distributors'

  initialData: IDistributorsModuleData = {
    listing: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15
      }
    },
    record: {
      tabTariffs: {
        limit: 15,
        page: 1
      }
    }
  }
}

export type { IDistributorsModuleData }

export default DistributorsModule