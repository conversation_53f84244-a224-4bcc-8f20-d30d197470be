import { IAlarmsTargetsTypes } from '@/@core/domain/AlarmsTargetsTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IAlarmsTargetsTypesListModuleData {
  list: IAlarmsTargetsTypes[]
}

class AlarmsTargetsTypesListModule extends MemoryAbstract<IAlarmsTargetsTypesListModuleData> {
  key = 'memory.list.alarmsTargetsTypes'

  initialData: IAlarmsTargetsTypesListModuleData = {
    list: []
  }
}

export type { IAlarmsTargetsTypesListModuleData }

export default AlarmsTargetsTypesListModule