import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IUsersModuleData {
  listing: {
    search: {
      q: string
      sort: 'id' | 'name' | 'email' | 'last_access'
      order: Order
      limit: number
      page: number
      accountId: { id: number; name: string }[]
    }
  }
}

class UsersModule extends MemoryAbstract<IUsersModuleData> {
  key = 'memory.users'

  initialData: IUsersModuleData = {
    listing: {
      search: {
        q: '',
        sort: 'id',
        order: 'desc',
        limit: 15,
        page: 1,
        accountId: []
      }
    }
  }
}

export type { IUsersModuleData }

export default UsersModule
