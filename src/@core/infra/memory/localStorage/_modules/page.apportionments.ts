import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IApportionmentsModuleData {
  listing: {
    search: {
      q: string
      order: Order
      sort: 'id',
      page: number
      limit: number
    }
  }
  record: {
    sectionGroups: {
      q: string
      page: number
      limit: number
      order: Order
      sort: 'id'
    }
    sectionCostCenters: {
      q: string
      page: number
      limit: number
      order: Order
      sort: 'id'
    }
    sectionResultsHistory: {
      page: number
      limit: number
      order: Order
      sort: 'date'
    }
  }
}

class ApportionmentsModule extends MemoryAbstract<IApportionmentsModuleData> {
  key = 'memory.apportionments'

  initialData: IApportionmentsModuleData = {
    listing: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15
      }
    },
    record: {
      sectionGroups: {
        q: '',
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      },
      sectionCostCenters: {
        q: '',
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      },
      sectionResultsHistory: {
        page: 1,
        limit: 999,
        order: 'desc',
        sort: 'date'
      }
    }
  }
}

export type { IApportionmentsModuleData }

export default ApportionmentsModule