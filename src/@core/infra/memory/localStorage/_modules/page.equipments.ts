import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IEquipmentsModuleData {
  listing: {
    search: {
      q: string
      sort: 'id' | 'nome'
      order: Order
      limit: number
      page: number
      companies: { id: number; name: string }[]
      accounts: { id: number; name: string }[]
      companiesGroup: number[]
      accountId?: number
      withoutDevice?: 1
      typeId?: string
    }
    modalProcess: {
      companies?: { id: number; name: string }[]
      q?: string
      page?: number
      limit?: number
    }
  }
  record: {
    tabProperty: {
      limit: number
      page: number
    }
    tabIntegration: {
      limit: number
      page: number
    }
    tabTelemetryEquipmentVirtual: {
      limit: number
      page: number
    }
  }
}

class EquipmentsModule extends MemoryAbstract<IEquipmentsModuleData> {
  key = 'memory.equipments'

  initialData: IEquipmentsModuleData = {
    listing: {
      search: {
        q: '',
        sort: 'id',
        order: 'desc',
        limit: 15,
        page: 1,
        companies: [],
        accounts: [],
        companiesGroup: []
      },
      modalProcess: {
        q: '',
        page: 1,
        limit: 15,
        companies: []
      }
    },
    record: {
      tabProperty: {
        limit: 10,
        page: 1
      },
      tabIntegration: {
        limit: 10,
        page: 1
      },
      tabTelemetryEquipmentVirtual: {
        limit: 10,
        page: 1
      }
    }
  }
}

export type { IEquipmentsModuleData }

export default EquipmentsModule
