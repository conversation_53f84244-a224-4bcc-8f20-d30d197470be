import { IDashboardTab } from '@/@core/domain/DashboardTabs'
import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IDashboardModuleData {
  menu: {
    open: boolean
    itemActive: Partial<IMenuDashboard> | null
    inputSearch: string
    inputAccount: { id: number; name: string } | null
    menuFixed: boolean
    isDesktopMenuOpen: boolean
  }
  page: {
    tab: IDashboardTab | null
    period: { initial: string; final: string }
  }
}
class DashboardModule extends MemoryAbstract<IDashboardModuleData> {
  key = 'memory.dashboard'

  initialData: IDashboardModuleData = {
    menu: {
      open: false,
      itemActive: null,
      inputSearch: '',
      inputAccount: null,
      menuFixed: false,
      isDesktopMenuOpen: false
    },
    page: {
      tab: null,
      period: { initial: '', final: '' }
    }
  }
}

export type { IDashboardModuleData }

export default DashboardModule