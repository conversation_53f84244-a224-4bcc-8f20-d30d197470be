import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { ITypeSubTypeGrouped } from '@/@core/utils/groupedListTypeSubtypes'

interface ITypeSubTypeGroupedModuleData {
  list: ITypeSubTypeGrouped[]
}

class TypeSubTypeGroupedModule extends MemoryAbstract<ITypeSubTypeGroupedModuleData> {
  key = 'memory.list.typeSubTypeGrouped'

  initialData: ITypeSubTypeGroupedModuleData = {
    list: []
  }
}

export type { ITypeSubTypeGroupedModuleData }

export default TypeSubTypeGroupedModule