import { IAlarmsNotificationsChannels } from '@/@core/domain/AlarmsNotificationsChannels'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IAlarmsNotificationsChannelsListModuleData {
  list: IAlarmsNotificationsChannels[]
}

class AlarmsNotificationsChannelsListModule extends MemoryAbstract<IAlarmsNotificationsChannelsListModuleData> {
  key = 'memory.list.alarmsNotificationsChannels'

  initialData: IAlarmsNotificationsChannelsListModuleData = {
    list: []
  }
}

export type { IAlarmsNotificationsChannelsListModuleData }

export default AlarmsNotificationsChannelsListModule