import { IOperationalRule } from '@/@core/domain/OperationalRule'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IOperationalRuleListModuleData {
  list: IOperationalRule[]
}

class OperationalRuleListModule extends MemoryAbstract<IOperationalRuleListModuleData> {
  key = 'memory.list.operationalRule'

  initialData: IOperationalRuleListModuleData = {
    list: []
  }
}

export type { IOperationalRuleListModuleData }

export default OperationalRuleListModule