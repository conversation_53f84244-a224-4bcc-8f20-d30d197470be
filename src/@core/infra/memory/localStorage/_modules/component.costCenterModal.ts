import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface ICostCenterModalModuleData {
  tabCustomAdditionais: {
    search: {
      costCenterId: number
      page: number
      limit: number
      order: Order
      sort: 'id'
    }
  }
  tabEquipments: {
    search: {
      costCenterIds: number[]
      page: number
      limit: number
      order: Order
      sort: 'id'
    }
  }
  tabCustomTariffs: {
    search: {
      costCenterId: number | null
      page: number
      limit: number
      order: Order
      sort: 'id'
    }
  }
}

class CostCenterModalModule extends MemoryAbstract<ICostCenterModalModuleData> {
  key = 'memory.component.costCenterModal'

  initialData: ICostCenterModalModuleData = {
    tabCustomAdditionais: {
      search: {
        costCenterId: 0,
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      }
    },
    tabEquipments: {
      search: {
        costCenterIds: [],
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      }
    },
    tabCustomTariffs: {
      search: {
        costCenterId: null,
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      }
    }
  }
}

export type { ICostCenterModalModuleData }

export default CostCenterModalModule