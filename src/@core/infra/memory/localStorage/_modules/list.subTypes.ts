import ISubType, { ISubTypeGrouped } from '@/@core/domain/SubType'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface ISubTypesModuleData {
  list: ISubType[]
  listGrouped: ISubTypeGrouped[]
}

class SubTypesModule extends MemoryAbstract<ISubTypesModuleData> {
  key = 'memory.list.subTypes'

  initialData: ISubTypesModuleData = {
    list: [],
    listGrouped: []
  }
}

export type { ISubTypesModuleData }

export default SubTypesModule