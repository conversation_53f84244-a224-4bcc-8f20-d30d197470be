import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IIntegrationsModuleData {
  listing: {
    search: {
      q: string
      sort: 'id' | 'name' | 'created_at'
      order: Order
      limit: number
      page: number
      integrationId?: number
      accountId?: number
    }

  }
}

class IntegrationsModule extends MemoryAbstract<IIntegrationsModuleData> {
  key = 'memory.integrations'

  initialData: IIntegrationsModuleData = {
    listing: {
      search: {
        q: '',
        sort: 'id',
        order: 'desc',
        limit: 15,
        page: 1
      },
    },
  }
}

export type { IIntegrationsModuleData }

export default IntegrationsModule