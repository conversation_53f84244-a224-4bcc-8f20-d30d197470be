import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface ICompaniesModuleData {
  listing: {
    search: {
      q: string
      sort: 'id' | 'nome'
      order: Order
      limit: number
      page: number
      externalId?: string[]
      parentId?: number
      accountId?: { id: number; name: string }[]
      type?: string
    }
  }
  record: {
    tabContacts: {
      page?: number
      limit?: number
    }
    tabSalesforce: {
      page?: number
      limit?: number
    }
  }
}

class CompaniesModule extends MemoryAbstract<ICompaniesModuleData> {
  key = 'memory.componentompanies'

  initialData: ICompaniesModuleData = {
    listing: {
      search: {
        q: '',
        type: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15,
        accountId: []
      }
    },
    record: {
      tabContacts: {
        limit: 15,
        page: 1
      },
      tabSalesforce: {
        limit: 15,
        page: 1
      }
    }
  }
}

export type { ICompaniesModuleData }

export default CompaniesModule