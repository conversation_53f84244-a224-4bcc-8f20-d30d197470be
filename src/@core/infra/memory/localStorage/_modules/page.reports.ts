import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IReportsModuleData {
  Historical: {
    search: {
      q: string
      order: Order
      sort: 'id' | 'name' | 'created_at'
      page: number
      limit: number
      model?: boolean
    }
  }
}

class ReportsModule extends MemoryAbstract<IReportsModuleData> {
  key = 'memory.reports'

  initialData: IReportsModuleData = {
    Historical: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15
      }
    },
  }
}

export type { IReportsModuleData }

export default ReportsModule