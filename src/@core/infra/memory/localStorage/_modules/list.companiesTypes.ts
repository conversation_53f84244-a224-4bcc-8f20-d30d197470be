import ICompaniesTypes from '@/@core/domain/CompaniesTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface ICompaniesTypesListModuleData {
  list: ICompaniesTypes[]
}

class CompaniesTypesListModule extends MemoryAbstract<ICompaniesTypesListModuleData> {
  key = 'memory.list.companiesTypes'

  initialData: ICompaniesTypesListModuleData = {
    list: []
  }
}

export type { ICompaniesTypesListModuleData }

export default CompaniesTypesListModule