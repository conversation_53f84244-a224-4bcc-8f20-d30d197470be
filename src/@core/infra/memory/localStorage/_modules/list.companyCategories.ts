import { IApportionmentTypes } from '@/@core/domain/ApportionmentTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface ICompaniesCategoriesListModuleData {
  list: IApportionmentTypes[]
}

class CompaniesCategoriesListModule extends MemoryAbstract<ICompaniesCategoriesListModuleData> {
  key = 'memory.list.companiesCategories'

  initialData: ICompaniesCategoriesListModuleData = {
    list: []
  }
}

export type { ICompaniesCategoriesListModuleData }

export default CompaniesCategoriesListModule