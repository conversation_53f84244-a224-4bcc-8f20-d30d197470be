import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import dayjs from 'dayjs'

interface IStatisticsModuleData {
  currentMonth: string
  frequency: 'day' | 'month'
}

class StatisticsModule extends MemoryAbstract<IStatisticsModuleData> {
  key = 'memory.statistics'

  initialData: IStatisticsModuleData = {
    currentMonth: dayjs().startOf('month').format('YYYY-MM-DD'),
    frequency: 'month'
  }
}

export type { IStatisticsModuleData }

export default StatisticsModule
