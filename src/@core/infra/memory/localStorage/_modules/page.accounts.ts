import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IAccountsModuleData {
  listing: {
    search: {
      q: string
      order: Order
      sort: 'id' | 'name'
      page: number
      limit: number
      statusIds: number[]
    }
  }
  record: {
    tabManagement: {
      limit: number
      page: number
    }
  }
}
class AccountsModule extends MemoryAbstract<IAccountsModuleData> {
  key = 'memory.accounts'

  initialData: IAccountsModuleData = {
    listing: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        statusIds: [1],
        page: 1,
        limit: 15
      }
    },
    record: {
      tabManagement: {
        limit: 10,
        page: 1
      }
    }
  }
}

export type { IAccountsModuleData }

export default AccountsModule