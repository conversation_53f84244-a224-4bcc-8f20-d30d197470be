import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { LanguageType } from '@/types/language'

interface ISystemModuleData {
  language: LanguageType
  theme: 'dark' | 'light'
}

class SystemModule extends MemoryAbstract<ISystemModuleData> {
  key = 'system'

  initialData: ISystemModuleData = {
    language: 'pt-BR',
    theme: 'light',
  }
}

export type { ISystemModuleData }

export default SystemModule