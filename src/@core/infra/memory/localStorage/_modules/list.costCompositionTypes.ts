import { ICostCompositionType } from '@/@core/domain/CostCompositionTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface ICostCompositionTypesListModuleData {
  list: ICostCompositionType[]
}

class CostCompositionTypesListModule extends MemoryAbstract<ICostCompositionTypesListModuleData> {
  key = 'memory.list.costCompositionTypes'

  initialData: ICostCompositionTypesListModuleData = {
    list: []
  }
}

export type { ICostCompositionTypesListModuleData }

export default CostCompositionTypesListModule