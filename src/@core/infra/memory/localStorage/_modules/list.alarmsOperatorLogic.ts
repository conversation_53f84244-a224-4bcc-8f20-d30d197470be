import { IAlarmsOperatorLogic } from '@/@core/domain/AlarmsOperatorLogic'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IAlarmsOperatorLogicListModuleData {
  list: IAlarmsOperatorLogic[]
}

class AlarmsOperatorLogicListModule extends MemoryAbstract<IAlarmsOperatorLogicListModuleData> {
  key = 'memory.list.alarmsOperatorLogic'

  initialData: IAlarmsOperatorLogicListModuleData = {
    list: []
  }
}

export type { IAlarmsOperatorLogicListModuleData }

export default AlarmsOperatorLogicListModule