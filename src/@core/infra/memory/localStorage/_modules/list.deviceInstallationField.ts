import IDeviceInstallation<PERSON>ield from '@/@core/domain/DeviceInstallationFields'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IDeviceInstallationFieldListModuleData {
  list: IDeviceInstallationField[]
}

class DeviceInstallationFieldListModule extends MemoryAbstract<IDeviceInstallationFieldListModuleData> {
  key = 'memory.list.deviceInstallationField'

  initialData: IDeviceInstallationFieldListModuleData = {
    list: []
  }
}

export type { IDeviceInstallationFieldListModuleData }

export default DeviceInstallationFieldListModule