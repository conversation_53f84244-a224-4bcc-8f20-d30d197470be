import { IApportionmentTariffTypes } from '@/@core/domain/ApportionmentTariffTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IApportionmentsTariffTypesListModuleData {
  list: IApportionmentTariffTypes[]
}

class ApportionmentsTariffTypesListModule extends MemoryAbstract<IApportionmentsTariffTypesListModuleData> {
  key = 'memory.list.apportionmentsTariffTypes'

  initialData: IApportionmentsTariffTypesListModuleData = {
    list: []
  }
}

export type { IApportionmentsTariffTypesListModuleData }

export default ApportionmentsTariffTypesListModule