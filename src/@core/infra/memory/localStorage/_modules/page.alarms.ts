import { IAlarmTriggeredSort } from '@/@core/domain/AlarmTriggered'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IAlarmsModuleData {
  listing: {
    search: {
      q: string
      order: Order
      sort: 'id' | 'name' | 'status'
      page: number
      limit: number
      status: number
      alarmsCategoryId?: number
    }
  }
  record: {
    tabNotifications: {
      page: number
      limit: number
    }
    tabHistorical: {
      limit: number
      page: number
      status: string
      order: Order
      sort: IAlarmTriggeredSort
      equipmentId: { id: number; name: string }[]
      alarmId?: number
    }
  }
}

class AlarmsModule extends MemoryAbstract<IAlarmsModuleData> {
  key = 'memory.alarms'

  initialData: IAlarmsModuleData = {
    listing: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15,
        status: 1
      }
    },
    record: {
      tabNotifications: {
        limit: 15,
        page: 1
      },
      tabHistorical: {
        limit: 15,
        page: 1,
        status: '',
        order: 'desc',
        sort: 'id',
        equipmentId: []
      }
    }
  }
}

export type { IAlarmsModuleData }

export default AlarmsModule