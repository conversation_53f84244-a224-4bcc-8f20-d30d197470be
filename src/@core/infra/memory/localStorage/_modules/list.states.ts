import { IListState } from '@/@core/domain/ListState'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IStatesListModuleData {
  list: IListState[]
}

class StatesListModule extends MemoryAbstract<IStatesListModuleData> {
  key = 'memory.list.states'

  initialData: IStatesListModuleData = {
    list: []
  }
}

export type { IStatesListModuleData }

export default StatesListModule