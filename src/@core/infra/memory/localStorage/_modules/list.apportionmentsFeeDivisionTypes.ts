import { IApportionmentsFeeDivisionTypes } from '@/@core/domain/ApportionmentsFeeDivisionTypes'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IApportionmentsFeeDivisionTypesListModuleData {
  list: IApportionmentsFeeDivisionTypes[]
}

class ApportionmentsFeeDivisionTypesListModule extends MemoryAbstract<IApportionmentsFeeDivisionTypesListModuleData> {
  key = 'memory.list.apportionmentsFeeDivisionTypes'

  initialData: IApportionmentsFeeDivisionTypesListModuleData = {
    list: []
  }
}

export type { IApportionmentsFeeDivisionTypesListModuleData }

export default ApportionmentsFeeDivisionTypesListModule