import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IDevicesModuleData {
  listing: {
    search: {
      q: string
      sort: 'id' | 'code' | 'company' | 'equipment' | 'type'
      order: Order
      limit: number
      page: number
      company?: { id: number; name: string }[]
      equipment?: number
      accountId?: number
    }
  }
  record: {
    tabConstants: {
      order?: Order
      sort?: 'id' | 'initial_date'
      page?: number
      limit?: number
    }
    tabInstallations: {
      page?: number
      limit?: number
    }
    tabBillings: {
      page?: number
      limit?: number
    }
  }
}

class DevicesModule extends MemoryAbstract<IDevicesModuleData> {
  key = 'memory.devices'

  initialData: IDevicesModuleData = {
    listing: {
      search: {
        q: '',
        order: 'desc',
        sort: 'id',
        page: 1,
        limit: 15,
        company: []
      }
    },
    record: {
      tabConstants: {
        order: 'desc',
        sort: 'initial_date',
      },
      tabInstallations: {
        page: 1,
        limit: 15,
      },
      tabBillings: {
        page: 1,
        limit: 15,
      }
    }
  }
}

export type { IDevicesModuleData }

export default DevicesModule