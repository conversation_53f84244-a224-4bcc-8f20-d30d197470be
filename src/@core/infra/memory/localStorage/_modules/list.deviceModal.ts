import IDeviceModel from '@/@core/domain/DeviceModel'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IDeviceModelListModuleData {
  list: IDeviceModel[]
}

class DeviceModelListModule extends MemoryAbstract<IDeviceModelListModuleData> {
  key = 'memory.list.deviceModal'

  initialData: IDeviceModelListModuleData = {
    list: []
  }
}

export type { IDeviceModelListModuleData }

export default DeviceModelListModule