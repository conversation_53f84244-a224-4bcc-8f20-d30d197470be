import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IPropertiesModuleData {
  listing: {
    search: {
      q: string
      sort: 'id' | 'nome' | 'storeable'
      order: Order
      limit: number
      page: number
      type?: number | null
      subType?: number | null
    }
  }
}

class PropertiesModule extends MemoryAbstract<IPropertiesModuleData> {
  key = 'memory.properties'

  initialData: IPropertiesModuleData = {
    listing: {
      search: {
        q: '',
        sort: 'id',
        order: 'desc',
        limit: 15,
        page: 1,
        type: null,
        subType: null
      }
    },
  }
}

export type { IPropertiesModuleData }

export default PropertiesModule