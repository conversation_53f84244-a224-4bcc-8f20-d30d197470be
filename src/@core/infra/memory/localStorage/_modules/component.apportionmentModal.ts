import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'
import { Order } from '@/types/system'

interface IApportionmentModalModuleData {
  tabAdditionalFees: {
    search: {
      apportionmentId: number | null
      apportionmentTypeId: number | null
      page: number
      limit: number
      order: Order
      sort: 'id'
    }
  }
  tabCustomTariffs: {
    search: {
      apportionmentId: number | null
      page: number
      limit: number
      order: Order
      sort: 'id' | 'vigency_end'
    }
  }
  tabPeriod: {
    search: {
      apportionmentId: number | null
      page: 1
      limit: 10
      order: 'desc'
      sort: 'id'
    }
  }
}

class ApportionmentModalModule extends MemoryAbstract<IApportionmentModalModuleData> {
  key = 'memory.component.apportionmentModal'

  initialData: IApportionmentModalModuleData = {
    tabAdditionalFees: {
      search: {
        apportionmentId: null,
        apportionmentTypeId: null,
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      }
    },
    tabCustomTariffs: {
      search: {
        apportionmentId: null,
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'vigency_end'
      }
    },
    tabPeriod: {
      search: {
        apportionmentId: null,
        page: 1,
        limit: 10,
        order: 'desc',
        sort: 'id'
      }
    }
  }
}

export type { IApportionmentModalModuleData }

export default ApportionmentModalModule
