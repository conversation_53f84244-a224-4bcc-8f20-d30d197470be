import { IAlarmsTargets } from '@/@core/domain/AlarmsTargets'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IAlarmsTargetsListModuleData {
  list: IAlarmsTargets[]
}

class AlarmsTargetsListModule extends MemoryAbstract<IAlarmsTargetsListModuleData> {
  key = 'memory.list.alarmsTargets'

  initialData: IAlarmsTargetsListModuleData = {
    list: []
  }
}

export type { IAlarmsTargetsListModuleData }

export default AlarmsTargetsListModule