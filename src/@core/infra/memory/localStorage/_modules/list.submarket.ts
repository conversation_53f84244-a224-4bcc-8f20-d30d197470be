import { ISubmarkets } from '@/@core/domain/Submarkets'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface ISubmarketListModuleData {
  list: ISubmarkets[]
}

class SubmarketListModule extends MemoryAbstract<ISubmarketListModuleData> {
  key = 'memory.list.submarket'

  initialData: ISubmarketListModuleData = {
    list: []
  }
}

export type { ISubmarketListModuleData }

export default SubmarketListModule