import { IListCities } from '@/@core/domain/ListCities'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface ICitiesListModuleData {
  list: IListCities[]
}

class CitiesListModule extends MemoryAbstract<ICitiesListModuleData> {
  key = 'memory.list.cities'

  initialData: ICitiesListModuleData = {
    list: []
  }
}

export type { ICitiesListModuleData }

export default CitiesListModule