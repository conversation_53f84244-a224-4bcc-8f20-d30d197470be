import { IApportionmentMeasuresUnit } from '@/@core/domain/ApportionmentMeasuresUnit'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IApportionmentMeasuresUnitsListModuleData {
  list: IApportionmentMeasuresUnit[]
}

class ApportionmentMeasuresUnitsListModule extends MemoryAbstract<IApportionmentMeasuresUnitsListModuleData> {
  key = 'memory.list.apportionmentMeasuresUnits'

  initialData: IApportionmentMeasuresUnitsListModuleData = {
    list: []
  }
}

export type { IApportionmentMeasuresUnitsListModuleData }

export default ApportionmentMeasuresUnitsListModule