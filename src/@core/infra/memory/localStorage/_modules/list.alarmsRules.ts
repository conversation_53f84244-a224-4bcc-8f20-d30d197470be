import { IAlarmsRules } from '@/@core/domain/AlarmsRules'
import { MemoryAbstract } from '@/@core/infra/memory/memoryAbstract'

interface IAlarmsRulesListModuleData {
  list: IAlarmsRules[]
}

class AlarmsRulesListModule extends MemoryAbstract<IAlarmsRulesListModuleData> {
  key = 'memory.list.alarmsRules'

  initialData: IAlarmsRulesListModuleData = {
    list: []
  }
}

export type { IAlarmsRulesListModuleData }

export default AlarmsRulesListModule