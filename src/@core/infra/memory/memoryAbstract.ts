import MemoryEstrategyInterface from './memoryEstrategyInterface'

export class MemoryAbstract<T> {
  key: string
  initialData: T

  constructor(readonly memory: MemoryEstrategyInterface) {}

  get(): Partial<T> | null {
    return this.memory.get(this.key) as Partial<T> | null
  }
  set(payload: Partial<T>) {
    this.memory.set(this.key, payload)
  }
  destroy() {
    this.memory.destroy(this.key)
  }
}
