import { destroyCookie, parseCookies, setCookie } from 'nookies'
import MemoryEstrategyInterface from '../memoryEstrategyInterface'

export default class CookieEstrategy<T> implements MemoryEstrategyInterface {
  get(key: string): Partial<T> | null {
    const data = parseCookies()
    return data?.[key] ? JSON.parse(data[key]) : {}
  }
  set(key: string, payload: Partial<T>) {
    const oldData = this.get(key)
    const newData = { ...oldData, ...payload }

    setCookie(undefined, key, JSON.stringify(newData), {
      maxAge: 60 * 60 * 24,
      path: '/'
    })
  }
  destroy(key: string) {
    destroyCookie(undefined, key)
  }
}
