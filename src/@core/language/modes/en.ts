import { LanguageTree } from '@/types/language'

const tree: LanguageTree = {
  validationFields: {
    requiredField: 'Required field',
    invalidEmail: 'Invalid email',
    MustContainLeastCaracter: (n) => `Must contain at least ${n} character(s)`,
    MustContainLeastItems: (n, v) => `Must contain at least ${n} ${v}`
  },
  errors: {
    request: 'An error occurred, try again later',
    warning: 'Warning'
  },
  btn: {
    add: 'Add',
    save: 'Save',
    update: 'Update',
    edit: 'Edit',
    clean: 'Clean',
    cancel: 'Cancel',
    remove: 'Remove',
    close: 'Close',
    filter: 'Filter',
    confirm: 'Confirm',
    continue: 'Continue',
    back: 'Back'
  },
  form: {
    search: 'Search',
    selectAll: 'All',
    selectAnOption: 'Select an option'
  },
  table: {
    previous: 'Previous',
    next: 'Next',
    of: 'of',
    withoutData: 'No data found',
    newRegister: 'New register',
    totalRegisters: 'registers'
  },
  components: {
    SectionRuleTarget: {
      triggering: {
        title: 'Triggering',
        titleList: 'Triggerings created success'
      },
      normalization: {
        title: 'Normalization',
        titleList: 'Normalization created success'
      },
      form: {
        input: {
          rule: 'Rules',
          processed: 'Processed data',
          property: 'Property',
          operator: 'Operator',
          value: 'Value'
        },
        messages: {
          createSuccessMessage: 'Conditional created success',
          createErrorMessage: 'Error creating conditional',
          updateSuccessMessage: 'Conditional updated success',
          updateErrorMessage: 'Error updating conditional'
        },
        messageAlert:
          'There is already a rule registered using this same logical operator, review the rules before proceeding.',
        requiredConditionalNormalization: 'Add at least one normalization',
        requiredConditionalTriggering: 'Add at least one triggering'
      },
      formDelete: {
        messages: {
          successMessage: 'Conditional successfully removed',
          errorMessage: 'Error removing conditional'
        }
      }
    }
  },
  modalDelete: {
    infoConfirmation: (text: string) =>
      `Are you sure you want to delete this ${text}?`,
    btnCancel: 'Cancel',
    btnConfirm: (text: string) => `Delete ${text}`,
    requestMessage: (text: string) => ({
      successMessage: `${text} deleted success`,
      errorMessage: `Error deleting ${text}`
    })
  },
  modal: {
    apportionment: {
      titleNew: 'Create apportionment',
      titleEdit: 'Edit apportionment',
      tabs: [
        'Information',
        'Closing period',
        'Additional fees',
        'Custom tarrifs'
      ],
      tabData: {
        title: 'Information',
        form: {
          input: {
            name: 'Name',
            description: 'Description',
            company: 'Company',
            consumption: 'Consumption',
            consumptionInfo: 'Complete with telemetry',
            measurementUnit: 'Unit of measurement',
            unitMeasurementNotAvailable: 'Unit of measurement not available',
            tariffType: 'Standard tariff',
            type: 'Type'
          },
          messages: {
            createSuccessMessage: 'Apportionment created success',
            createErrorMessage: 'Error creating apportionment',
            updateSuccessMessage: 'Apportionment updated success',
            updateErrorMessage: 'Error updating apportionment'
          }
        }
      },
      tabPeriod: {
        title: 'Closing period',
        form: {
          input: {
            period: 'Period',
            periodStart: 'Initial Period',
            periodEnd: 'Final Period'
          },
          messages: {
            createSuccessMessage: 'Apportionment equipment created success',
            createErrorMessage: 'Error creating apportionment equipment',
            updateSuccessMessage: 'Apportionment equipment updated success',
            updateErrorMessage: 'Error updating apportionment equipment'
          }
        },
        table: {
          columns: {
            period: 'Period',
            periodStart: 'Initial Period',
            periodEnd: 'Final Period',
            actions: 'Actions'
          },
          modalDelete: {
            title: 'Delete apportionment equipment',
            textInfo:
              'Are you sure you want to delete apportionment equipment?',
            messages: {
              successMessage: 'Apportionment equipment successfully removed',
              errorMessage: 'Error removing apportionment equipment'
            }
          }
        }
      },
      tabAdditionalFees: {
        title: 'Additional fees',
        form: {
          input: {
            period: 'Period',
            name: 'Name',
            value: 'Value',
            type: 'Type'
          },
          messages: {
            createSuccessMessage: 'Additional fees created success',
            createErrorMessage: 'Error creating additional fees',
            updateSuccessMessage: 'Additional fees updated success',
            updateErrorMessage: 'Error updating additional fees'
          }
        },
        table: {
          columns: {
            period: 'Period',
            name: 'Name',
            value: 'Value',
            type: 'Type',
            actions: 'Actions'
          },
          modalDelete: {
            title: 'Delete additional fees',
            textInfo: 'Are you sure you want to delete additional fees?',
            messages: {
              successMessage: 'Additional fees successfully removed',
              errorMessage: 'Error removing additional fees'
            }
          }
        }
      },
      tabCustomTariffs: {
        title: 'Custom tarrifs',
        form: {
          input: {
            vigencyStart: 'Start of surveillance',
            vigencyEnd: 'End of surveillance',
            value: 'Value'
          },
          messages: {
            createSuccessMessage: 'Custom tariff created success',
            createErrorMessage: 'Error creating custom tariff',
            updateSuccessMessage: 'Custom tariff updated success',
            updateErrorMessage: 'Error updating custom tariff'
          }
        },
        table: {
          columns: {
            vigencyStart: 'Start of surveillance',
            vigencyEnd: 'End of surveillance',
            value: 'Value',
            actions: 'Actions'
          },
          modalDelete: {
            title: 'Delete Custom tariff',
            textInfo: 'Are you sure you want to delete custom tariff?',
            messages: {
              successMessage: 'Custom tariff successfully removed',
              errorMessage: 'Error removing custom tariff'
            }
          }
        }
      }
    },
    apportionmentExportData: {
      title: 'Export apportionment (excel)',
      form: {
        input: {
          date: 'Date (month/year)',
          sendCostCenters: 'Export all cost centers'
        },
        messages: {
          successMessage: 'Apportionment exported successfully',
          errorMessage: 'Error exporting apportionment'
        },
        warningWithoutPeriod: 'Warning',
        textWithoutPeriod: 'There is no registered period for the apportionment'
      }
    },
    costCenterMultiple: {
      title: 'Create multiple cost centers',
      form: {
        inputEquipmentsAll: 'Use all available equipment',
        inputEquipmentsAllCompanyGroup: 'Include filiais',
        inputEquipments: 'Equipments',
        inputUsageLimit: 'Composition'
      }
    },
    compositionValue: {
      title: 'New Composition Value',
      form: {
        inputCostCenter: 'Cost center',
        inputEquipments: 'Equipment',
        inputUsageLimit: 'Composition',
        inputCostCompositionType: 'Type',
        inputApportionmentGroup: 'Group',
        messages: {
          successMessage: 'Composition value created successfully',
          errorMessage: 'Error creating composition value'
        }
      }
    },
    apportionmentGroup: {
      titleNew: 'Create group',
      titleEdit: 'Edit group',
      form: {
        inputName: 'Name',
        inputEquipments: 'Equipaments'
      },
      messages: {
        createSuccessMessage: 'Group created success',
        createErrorMessage: 'Error creating group',
        updateSuccessMessage: 'Group updated success',
        updateErrorMessage: 'Error updating group'
      }
    },
    costCenter: {
      titleNew: 'New cost center',
      titleEdit: 'Edit cost center'
    },
    apportionmentCalculate: {
      title: 'Calculate apportionment',
      form: {
        inputCalendar: 'Date (Month/Year)',
        messages: {
          successMessage: 'Apportionment calculated successfully',
          errorMessage: 'Error calculating apportionment'
        },
        warningWithoutPeriod: 'Warning',
        withoutPeriodText: 'No period available for reprocessing.'
      }
    }
  },
  tab: {
    // apportionmentTabAdditionalFees: {
    //   title: 'Additional fees',
    //   form: {
    //     input: {
    //       period: 'Period',
    //       name: 'Name',
    //       value: 'Value',
    //       type: 'Type'
    //     },
    //     messages: {
    //       createSuccessMessage: 'Additional fees created success',
    //       createErrorMessage: 'Error creating additional fees',
    //       updateSuccessMessage: 'Additional fees updated success',
    //       updateErrorMessage: 'Error updating additional fees'
    //     }
    //   },
    //   table: {
    //     columns: {
    //       period: 'Period',
    //       name: 'Name',
    //       value: 'Value',
    //       type: 'Type',
    //       actions: 'Actions'
    //     },
    //     modalDelete: {
    //       title: 'Delete additional fees',
    //       textInfo: 'Are you sure you want to delete additional fees?',
    //       messages: {
    //         successMessage: 'Additional fees successfully removed',
    //         errorMessage: 'Error removing additional fees'
    //       }
    //     }
    //   }
    // },
    // apportionmentTabCustomTariffs: {
    //   title: 'Additional fees',
    //   form: {
    //     input: {
    //       vigencyStart: 'Start of surveillance',
    //       vigencyEnd: 'End of surveillance',
    //       value: 'Value'
    //     },
    //     messages: {
    //       createSuccessMessage: 'Custom tariff created success',
    //       createErrorMessage: 'Error creating custom tariff',
    //       updateSuccessMessage: 'Custom tariff updated success',
    //       updateErrorMessage: 'Error updating custom tariff'
    //     }
    //   },
    //   table: {
    //     columns: {
    //       vigencyStart: 'Start of surveillance',
    //       vigencyEnd: 'End of surveillance',
    //       value: 'Value',
    //       actions: 'Actions'
    //     },
    //     modalDelete: {
    //       title: 'Delete Custom tariff',
    //       textInfo: 'Are you sure you want to delete custom tariff?',
    //       messages: {
    //         successMessage: 'Custom tariff successfully removed',
    //         errorMessage: 'Error removing custom tariff'
    //       }
    //     }
    //   }
    // },
    costCenterTabData: {
      title: 'Informations',
      form: {
        input: {
          inputName: 'Name',
          inputEmail: 'E-mail',
          inputTariffType: 'Tariff default',
          inputTariffTypeInfo: 'There are not tariff for this type!'
        },
        messages: {
          createSuccessMessage: 'Cost center created success',
          createErrorMessage: 'Error creating cost center',
          updateSuccessMessage: 'Cost center updated success',
          updateErrorMessage: 'Error updating cost center'
        }
      }
    },
    costCenterTabCustomTariffs: {
      form: {
        input: {
          vigencyStart: 'Start of surveillance',
          vigencyEnd: 'End of surveillance',
          value: 'Value'
        },
        messages: {
          createSuccessMessage: 'Custom tariff created success',
          createErrorMessage: 'Error creating custom tariff',
          updateSuccessMessage: 'Custom tariff updated success',
          updateErrorMessage: 'Error updating custom tariff'
        }
      },
      table: {
        columns: {
          vigencyStart: 'Start of surveillance',
          vigencyEnd: 'End of surveillance',
          value: 'Value',
          actions: 'Actions'
        },
        modalDelete: {
          title: 'Delete Custom tariff',
          textInfo: 'Are you sure you want to delete custom tariff?',
          messages: {
            successMessage: 'Custom tariff successfully removed',
            errorMessage: 'Error removing custom tariff'
          }
        }
      }
    },
    costCenterTabCustomAdditionais: {
      form: {
        input: {
          period: 'Period',
          value: 'Value'
        },
        messages: {
          createSuccessMessage: 'Period created success',
          createErrorMessage: 'Error creating period',
          updateSuccessMessage: 'Period updated success',
          updateErrorMessage: 'Error updating period'
        }
      },
      table: {
        columns: {
          period: 'Period',
          value: 'Value',
          actions: 'Actions'
        },
        modalDelete: {
          title: 'Delete period',
          textInfo: 'Are you sure you want to delete period?',
          messages: {
            successMessage: 'Period successfully removed',
            errorMessage: 'Error removing period'
          }
        }
      }
    },
    costCenterTabEquipments: {
      form: {
        input: {
          equipment: 'Equipment',
          costCompositionType: 'Type',
          composition: 'Composition',
          group: 'Group'
        },
        messages: {
          createSuccessMessage: 'Equipment cost center created success',
          createErrorMessage: 'Error creating equipment cost',
          updateSuccessMessage: 'Equipment cost center updated success',
          updateErrorMessage: 'Error updating equipment cost'
        }
      },
      table: {
        columns: {
          equipment: 'Equipment',
          composition: 'Composition',
          actions: 'Actions'
        },
        modalDelete: {
          title: 'Delete equipment cost',
          textInfo: 'Are you sure you want to delete equipment cost?',
          messages: {
            successMessage: 'Equipment cost center successfully removed',
            errorMessage: 'Error removing equipment cost'
          }
        }
      }
    }
  },
  pages: {
    developing: 'Page under development',
    notFound: {
      title: 'Page not found'
    },
    accessDenied: {
      userNotPartOfPlatform: {
        title: 'Sorry, access denied!',
        description:
          'You do not have access to this product. If you have already purchased this solution or believe you should have access, please contact our support team.',
        button: 'Know the product'
      },
      isAuthError: {
        title: 'Authentication Failed',
        description:
          'Unable to log in. The authentication service experienced an outage. Please try again later or contact support.',
        button: 'Try again'
      },
      linkSupportEmail: 'Talk to Support'
    },
    accounts: {
      title: 'Accounts',
      btnNew: 'New Account',
      table: {
        search: {
          fieldStatus: 'Status',
          fieldQuery: 'Search'
        },
        columns: {
          name: 'Name',
          actions: 'Actions'
        },
        modalDelete: {
          title: 'Delete account',
          textInfo: 'Are you sure you want to delete the account:',
          subTextInfo:
            'After deleting the account, you will not be able to recover the data.',
          deletionConfirmationText:
            'For security, we ask that you confirm the PIN to complete the delete action:',
          messages: {
            successMessage: 'account successfully removed',
            errorMessage: 'Error removing account'
          }
        }
      }
    },
    accountId: {
      title: 'Account',
      titleNew: 'New account',
      titleEdit: 'Edit account',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data',
        entities: 'Permissions',
        personalization: 'Personalization',
        settings: 'Settings',
        management: 'Management'
      },
      tabData: {
        title: 'Data',
        form: {
          inputManagement: 'Management',
          inputStatus: 'Status',
          inputName: 'Account name'
        },
        formMessages: {
          createSuccessMessage: 'Account created success',
          createErrorMessage: 'Error creating account',
          updateSuccessMessage: 'Account updated success',
          updateErrorMessage: 'Error updating account'
        }
      },
      tabPersonalization: {
        titleColorDefault: 'Default color',
        titleColorSuggestion: 'Suggestion string',
        buttonApply: 'Apply color',
        demo: {
          buttonLabel: 'Buttons',
          buttonDefault: 'Botão padrão',
          buttonPrimary: 'Botão primário',
          icon: 'icons',
          iconLeft: 'left icons',
          iconRight: 'right icons',
          formLabel: 'Form',
          fieldTextLabel: 'text field',
          fieldText: 'Name',
          fieldSelectLabel: 'select field',
          fieldSelect: 'Name',
          fieldCheckboxLabel: 'checkbox field',
          fieldCheckbox: 'Enable',
          fieldSwitchLabel: 'switch field',
          fieldSwitch: 'Enable'
        }
      },
      tabSettings: {
        title: 'Settings',
        form: {
          inputNotification: 'Activate default notification'
        }
      },
      tabManagement: {
        title: 'Management',
        form: {
          inputAccount: 'Management'
        },
        table: {
          columns: {
            name: 'Name',
            actions: 'Actions'
          }
        },
        messages: {
          successMessage: 'Register updated success',
          errorMessage: 'Error updating register'
        }
      }
    },
    'agents-ccee': {
      title: 'Agents CCEE'
    },
    alarms: {
      title: 'Alarms',
      btnNew: 'New Alarm',
      table: {
        search: {
          fieldStatus: 'Status',
          fieldQuery: 'Search'
        },
        columns: {
          status: 'Status',
          name: 'Name',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete alarm',
          textInfo: 'Are you sure you want to delete the alarm:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          deleteSuccessMessage: 'Alarm successfully removed',
          deleteErrorMessage: 'Error removing alarm'
        },
        updateSuccessMessage: 'Alarm successfully updated',
        updateErrorMessage: 'Error when updating alarm'
      }
    },
    alarmsId: {
      title: 'Alarm',
      titleNew: 'New alarm',
      titleEdit: 'Edit alarm',
      tabs: {
        data: 'Data',
        rules: 'Rules',
        monitoring: 'Monitoring',
        notification: 'Notification',
        history: 'History'
      },
      tabData: {
        title: 'Data',
        subtitle: 'Change and/or update registration data',
        form: {
          input: {
            status: 'Status',
            name: 'Name',
            description: 'Description',
            category: 'Category',
            account: 'Account'
          },
          messages: {
            createSuccessMessage: 'Alarm created success',
            createErrorMessage: 'Error creating alarm',
            updateSuccessMessage: 'Alarm updated success',
            updateErrorMessage: 'Error updating alarm'
          }
        }
        /** formMessages: IFormMessages */
      },
      tabRules: {
        title: 'Rules',
        form: {
          target: {
            title: 'Target',
            input: {
              targetType: 'Type',
              target: 'Target',
              company: 'Company',
              placeholderCompany: 'select a company',
              equipment: 'Equipament',
              placeholderEquipment: 'select an equipament',
              template: 'Template',
              placeholderTemplate: 'select a template'
            }
          },
          shot: {
            title: 'Shot',
            registered: 'Registered shot'
          },
          normalized: {
            title: 'Normalized',
            registered: 'Registered normalized'
          }
        }
      },
      tabMonitoring: {
        title: 'Monitoring',
        form: {
          daysWeek: {
            all: 'All',
            monday: 'Monday',
            tuesday: 'Tuesday',
            wednesday: 'Wednesday',
            thursday: 'Thursday',
            friday: 'Friday',
            saturday: 'Saturday',
            sunday: 'Sunday'
          },
          input: {
            days: 'All',
            initialHour: 'Start time',
            fimHour: 'End time',
            preActivation: 'Pre-activation',
            daysRetention: 'Log retention days',
            daysWeekWarning: 'Select at least one day of the week.'
          }
          /** formMessages: IFormMessages */
        }
      },
      tabNotification: {
        title: 'Notification',
        form: {
          input: {
            channel: 'Channel',
            frequency: 'Frequency',
            user: 'User',
            email: 'E-mail'
          },
          messages: {
            createSuccessMessage: 'Notification created success',
            createErrorMessage: 'Error creating notification',
            updateSuccessMessage: 'Notification updated success',
            updateErrorMessage: 'Error updating notification'
          }
        },
        table: {
          columns: {
            channel: 'Channel',
            frequency: 'Frequency',
            user: 'User',
            actions: 'Actions'
          },
          modalDelete: {
            title: 'Delete notification',
            textInfo: 'Are you sure you want to delete the notification:',
            textConfirm: 'Delete',
            textCancel: 'Cancel',
            messages: {
              successMessage: 'Notification deleted successfully',
              errorMessage: 'Erro deleting notification'
            }
          }
        },
        btnCreateNewAlarm: 'Create new alarm'
      },
      tabHistorical: {
        title: 'Alarm history',
        form: {
          input: {
            status: 'Status',
            equipment: 'Equipment'
          },
          formMessages: {
            createSuccessMessage: 'Alarm normalized success',
            createErrorMessage: 'Error normalizing alarm'
          }
        },
        table: {
          columns: {
            status: 'Status',
            company: 'Company',
            equipment: 'Equipment',
            triggered: 'Triggered at:',
            normalized: 'Normalized at:',
            actions: 'Actions'
          }
        },
        modalNormalizeAlarm: {
          title: 'Alarm acknowledgement',
          textInfo:
            'This action will normalize this alarm triggering, be sure of this action before proceeding.',
          buttonConfirm: 'Yes, normalize alarm',
          buttonCancel: 'Cancel'
        }
      }
    },
    apportionments: {
      title: 'Apportionments',
      btnNew: 'New Apportionments',
      table: {
        search: {
          fieldQuery: 'Search'
        },
        columns: {
          name: 'Name',
          company: 'Company',
          type: 'Type',
          tariff: 'Tariff',
          cdcQuantity: 'Cc.Quantity',
          cdcValue: 'Cc Value',
          actions: 'Actions'
        },
        modalDelete: {
          title: 'Delete apportionments',
          textInfo: 'Are you sure you want to delete the apportionments:',
          messages: {
            successMessage: 'Apportionments successfully removed',
            errorMessage: 'Error removing apportionments'
          }
        }
      }
    },
    apportionmentsId: {
      title: 'Apportionment',
      dropdownItems: [
        'Edit apportionment',
        'New cost center',
        'New group',
        'New composition value',
        'New multiple cost centers',
        'Calculate apportionment',
        'Export data',
        'Delete apportionment'
      ],
      sectionApportionment: {
        title: 'General information',
        table: {
          columns: {
            company: 'Company',
            apportionmentType: 'Type',
            tariff: 'Tariff',
            costCenterLength: 'Amount of cost center',
            totalValueCurrent: 'Value of cost center'
          }
        }
      },
      sectionGroups: {
        title: 'Groups',
        table: {
          columns: {
            name: 'Name',
            equipments: 'Amount of equipment',
            actions: 'Actions'
          },
          modalDelete: {
            title: 'group',
            textInfo: 'Are you sure you want to delete the group:',
            textConfirm: 'Delete',
            textCancel: 'Cancel',
            messages: {
              successMessage: 'Group successfully removed',
              errorMessage: 'Error removing group'
            }
          }
        }
      },
      sectionCostCenter: {
        title: 'Cost Center',
        table: {
          columns: {
            name: 'Name',
            tariffs: 'Tariffs',
            equipments: 'Amount of equipment',
            actions: 'Actions'
          },
          modalDelete: {
            title: 'Cost center',
            textInfo: 'Are you sure you want to delete the Cost center:',
            textConfirm: 'Delete',
            textCancel: 'Cancel',
            messages: {
              successMessage: 'Cost center successfully removed',
              errorMessage: 'Error removing cost center'
            }
          }
        }
      },
      sectionResultsHistory: {
        title: 'Results history',
        table: {
          columns: {
            date: 'Date',
            constCenter: 'Cost Center Result',
            tariffType: 'Tariff type',
            tariff: 'Tariff',
            amountConsumer: 'Amount consumer'
          }
        },
        graphic: {
          title: 'Cost composition custos'
        },
        withoutData: 'No data found'
      }
    },
    companies: {
      title: 'Companies',
      btnNew: 'New Company',
      table: {
        search: {
          fieldAccounts: 'Account',
          fieldType: 'Type',
          fieldQuery: 'Search'
        },
        columns: {
          id: 'Id',
          name: 'Name',
          contact: 'Contact',
          account: 'Account',
          parent: 'Parent company',
          state: 'State',
          type: 'Type',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete company',
          textInfo: 'Are you sure you want to delete the company:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          deleteSuccessMessage: 'Company successfully removed',
          deleteErrorMessage: 'Error removing company'
        }
      }
    },
    companyId: {
      title: 'Company',
      titleNew: 'New Company',
      titleEdit: 'Edit Company',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data',
        contacts: 'Contacts',
        salesForce: 'Salesforce'
      },
      tabData: {
        title: 'Data',
        form: {
          inputAccount: 'Account',
          inputSocialName: 'Social Name',
          inputName: 'Fantasy name',
          inputCnpj: 'CNPJ',
          inputCnae: 'CNAE',
          inputConsumerUnitCode: 'Consumer unit code',
          inputCompany: 'Company',
          inputZipCode: 'Zip code',
          inputState: 'State',
          inputCity: 'City',
          inputNeighborhood: 'Neighborhood',
          inputStreet: 'Street/Avenue',
          inputNumber: 'Number',
          inputComplement: 'Complement',
          inputTimezone: 'Timezone',
          inputType: 'Type'
        },
        formMessages: {
          createSuccessMessage: 'Successfully created company',
          createErrorMessage: 'Error when creating company',
          updateSuccessMessage: 'Company successfully updated',
          updateErrorMessage: 'Error when updating company',
          errorWhenSearchingZipCode: 'Error when searching by zip code'
        }
      },
      tabContacts: {
        title: 'Contacts',
        form: {
          inputName: 'Name',
          inputEmail: 'Email',
          inputCellphone: 'Cellphone',
          inputCategory: 'Category'
        },
        formMessages: {
          createSuccessMessage: 'Contact created success',
          createErrorMessage: 'Error creating contact',
          updateSuccessMessage: 'Contact updated success',
          updateErrorMessage: 'Error updating contact'
        },
        table: {
          columns: {
            name: 'Name',
            email: 'Email',
            cellphone: 'Cellphone',
            category: 'Category',
            actions: 'Actions'
          },
          tableWithoutData: 'No data found'
        },
        modalDelete: {
          title: 'Delete contact',
          textInfo: 'Are you sure you want to delete the contact:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          messages: {
            successMessage: 'Contact deleted successfully',
            errorMessage: 'Erro deleting contact'
          }
        }
      },
      tabSalesforce: {
        title: 'Salesforce',
        form: {
          inputExternalId: 'Store code',
          inputSource: 'Source'
        },
        formMessages: {
          createSuccessMessage: 'Salesforce created successfully',
          createErrorMessage: 'Error creating salesforce',
          updateSuccessMessage: 'Salesforce updated successfully',
          updateErrorMessage: 'Error updating salesforce'
        },
        table: {
          columns: {
            externalId: 'Store code',
            source: 'Source',
            actions: 'Actions'
          },
          tableWithoutData: 'No data found'
        },
        modalDelete: {
          title: 'Delete Salesforce',
          textInfo: 'Are you sure you want to delete salesforce:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          messages: {
            successMessage: 'Salesforce deleted successfully',
            errorMessage: 'Erro deleting salesforce'
          }
        }
      }
    },
    contracts: {
      title: 'Contracts'
    },
    dashboard: {
      title: 'Dashboard',
      createDashboardModal: {
        title: 'Create dashboard',
        inputReplace: 'Replace',
        inputReplaceInfo:
          'If this option is selected, the old dashboards will be replaced. Otherwise, the current dashboard will be merged with the existing dashboards.',
        inputEntity: 'Create dashboard for:',
        inputAccount: 'Account',
        inputTemplate: 'Template',
        inputSearch: 'Search',
        submitMessages: {
          successMessage: 'Dashboard created successfully',
          errorMessage: 'Error creating dashboard'
        }
      },
      deleteDashboardModal: {
        title: 'Delete dashboard',
        inputAccount: 'Account',
        inputSearch: 'Search',
        submitMessages: {
          successMessage: 'Dashboard deleted successfully',
          errorMessage: 'Error deleting dashboard'
        }
      },
      createAlarmModal: {
        title: 'New alarm',
        form: {
          input: {
            name: 'Name',
            frequency: 'Hourly frequency',
            email: 'E-mail',
            placeholderEmail: 'Type and press Enter to add'
          },
          messages: {
            successMessage: 'Alarm created success',
            errorMessage: 'Error creating alarm'
          }
        }
      }
    },
    devices: {
      title: 'Devices',
      btnNew: 'New Device',
      formSearch: {
        fieldCompany: 'Company',
        fieldQuery: 'Search'
      },
      table: {
        search: {
          fieldCompany: 'Company',
          fieldQuery: 'Search'
        },
        columns: {
          type: 'Type',
          status: 'Status',
          code: 'Code',
          company: 'Company',
          equipment: 'Equipment',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete device',
          textInfo: 'Are you sure you want to delete the passcode device:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          deleteSuccessMessage: 'Device removed successfully',
          deleteErrorMessage: 'Error removing device'
        }
      }
    },
    devicesId: {
      title: 'Device',
      titleNew: 'New de Device',
      titleEdit: 'Edit de Device',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data',
        constant: 'Constants',
        installation: 'Installations',
        billings: 'Faturamento'
      },
      tabData: {
        title: 'Data',
        form: {
          inputCode: 'Code',
          inputMaster: 'Name account',
          inputEquipment: 'Equipment',
          inputType: 'Type',
          inputModel: 'Model',
          inputHourlyFrequency: 'Hourly frequency',
          inputAlertSeconds: 'Alert seconds',
          inputConnection: 'Connection',
          inputUrlFirmware: 'Url firmware',
          inputVersion: 'Version',
          inputIccid: 'Iccid',
          inputSsidOp: 'Ssid op'
        },
        formMessages: {
          createSuccessMessage: 'Device created success',
          createErrorMessage: 'Error creating device',
          updateSuccessMessage: 'Device updated success',
          updateErrorMessage: 'Error updating device'
        },
        actionObservation: {
          add: 'Put under observation',
          remove: 'Remove observation status'
        },
        modalObservationData: {
          title: 'Observation'
        },
        modalObservation: {
          title: 'Put device under observation',
          textInfo:
            'Describe the reason for placing the device under observation'
        },
        modalRemoveObservation: {
          title: 'Remove device from observation status',
          textInfo:
            'Are you sure you want to remove this device from observation status?',
          textConfirm: 'Yes, remove the observation device.'
        },
        requestStatusObservation: {
          successMessage: 'Device status changed successfully',
          errorMessage: 'Error changing device status'
        }
      },
      tabConstants: {
        title: 'Constants',
        form: {
          inputInitialDate: 'Initial Date',
          inputCurrentRelation: 'Current relation',
          inputPotentialRelation: 'Current potential',
          inputMeterConstant: 'Meter constant',
          inputLossFactor: 'Loss factor'
        },
        formMessages: {
          createSuccessMessage: 'Constant created success',
          createErrorMessage: 'Error creating constant',
          updateSuccessMessage: 'Constant updated success',
          updateErrorMessage: 'Error updating constant'
        },
        table: {
          columns: {
            initialDate: 'Initial Date',
            currentRelation: 'Current relation',
            potentialRelation: 'Current potential',
            meterConstant: 'Meter constant',
            lossFactor: 'Loss factor',
            actions: 'Actions'
          },
          tableWithoutData: 'No data found'
        },
        modalDelete: {
          title: 'Delete Constant',
          textInfo: 'Are you sure you want to delete constant',
          textConfirm: 'Yes, delete constant',
          textCancel: 'Cancel',
          messages: {
            successMessage: 'Constant deleted successfully',
            errorMessage: 'Erro deleting constant'
          },
          textwarning:
            'After deleting the constant, you will not be able to recover the data.',
          values: {
            initialDate: 'Initial Date',
            currentRelation: 'Current relation',
            potentialRelation: 'Current potential',
            meterConstant: 'Meter constant',
            lossFactor: 'Loss factor'
          }
        }
      },
      tabInstallations: {
        title: 'Installation',
        form: {
          key: 'Key',
          value: 'Value'
        },
        formMessages: {
          createSuccessMessage: 'Success installation configuration created',
          createErrorMessage: 'Error creating installation configuration',
          updateSuccessMessage: 'Installation configuration updated success',
          updateErrorMessage: 'Error updating installation configuration'
        },
        table: {
          columns: {
            keyName: 'Key name',
            value: 'Value',
            actions: 'Actions'
          },
          tableWithoutData: 'No data found'
        },
        modalDelete: {
          title: 'Delete Installation configuration',
          textInfo:
            'Are you sure you want to delete installation configuration',
          textConfirm: 'Yes, delete installation configuration',
          textCancel: 'Cancel',
          messages: {
            successMessage: 'Installation configuration deleted successfully',
            errorMessage: 'Erro deleting installation configuration'
          },
          textwarning:
            'After deleting the installation configuration, you will not be able to recover the data.',
          values: {
            keyName: 'Configuration name'
          }
        }
      },
      tabBillings: {
        title: 'Faturamento',
        form: {
          openDate: 'Abertura',
          openComment: 'Comentário de abertura',
          closeDate: 'Fechamento',
          closeComment: 'Comentário de fechamento'
        },
        formMessages: {
          createSuccessMessage: 'Faturamento criado com sucesso',
          createErrorMessage: 'Erro ao criar faturamento',
          updateSuccessMessage: 'Faturamento atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar faturamento'
        },
        table: {
          columns: {
            openDate: 'Abertura',
            openComment: 'Comentário de abertura',
            closeDate: 'Fechamento',
            closeComment: 'Comentário de fechamento'
          }
        }
      }
    },
    distributors: {
      title: 'Distributors',
      btnNew: 'New Distributor',
      table: {
        search: {
          fieldType: 'Type',
          fieldQuery: 'Search',
          buttonModalUpdateTariffs: 'Update tariffs'
        },
        columns: {
          name: 'Name',
          type: 'Type',
          state: 'State',
          submarket: 'Submarket',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete Distributor',
          textInfo: 'Are you sure you want to delete distributor',
          textwarning:
            'After deleting the distributor, you will not be able to recover the data.',
          textConfirm: 'Yes, delete distributor configuration',
          messages: {
            successMessage: 'Distribuidora deleted successfully',
            errorMessage: 'Erro deleting distribuidora'
          }
        }
      },
      modalTariffs: {
        title: 'Update tariffs',
        fieldDate: 'Date'
      }
    },
    distributorId: {
      title: 'Distributor',
      titleNew: 'New Distributor',
      titleEdit: 'Edit Distributor',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data',
        tariffs: 'Tariffs'
      },
      tabData: {
        title: 'Data',
        form: {
          inputName: 'Name',
          inputCnpj: 'CNPJ',
          inputExternalId: 'Code Store ',
          inputType: 'Type',
          inputSubmarket: 'Submarket',
          inputState: 'State',
          inputPeakStartTime: 'Peak start time',
          inputPeakEndTime: 'Peak end time',
          inputReactiveStartTime: 'Reactive start time',
          inputReactiveEndTime: 'Reactive end time',
          inputIntermediateStartTime: 'Intermediate start time',
          inputIntermediateEndTime: 'Intermediate end time'
        },
        formMessages: {
          createSuccessMessage: 'Distributor created success',
          createErrorMessage: 'Error creating distributor',
          updateSuccessMessage: 'Distributor updated success',
          updateErrorMessage: 'Error updating distributor'
        }
      },
      tabTariffs: {
        title: 'Tariffs',
        table: {
          columns: {
            tariffModalityName: 'Modality',
            voltageClassName: 'Class',
            type: 'Type',
            vigencyStart: 'Vigency Start',
            vigencyEnd: 'Vigency End',
            tusdMwhCaptive: 'TUSD MWh captive',
            tusdKwCaptive: 'TUSD kW captive',
            tusdMwh: 'TUSD MWh',
            tusdKw: 'TUSD kW',
            teMwh: 'TE MWh'
          },
          tableWithoutData: 'No data found'
        }
      }
    },
    'energetic-statement': {
      title: 'Energetic statement'
    },
    equipments: {
      title: 'Equipments',
      btnNew: 'New equipment',
      table: {
        search: {
          fieldCompany: 'Company',
          fieldQuery: 'Search',
          fieldAccount: 'Account'
        },
        columns: {
          name: 'Name',
          companyName: 'Company',
          ruleOperationalTitle: 'Rule operational',
          accountName: 'Account',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete equipment',
          textInfo: 'Are you sure you want to delete equipment?',
          textwarning:
            'After deleting the equipment, you will not be able to recover the data.',
          deleteSuccessMessage: 'Equipment removed successfully',
          deleteErrorMessage: 'Error removing equipment'
        },
        modalReprocessEquipment: {
          title: 'Reprocess equipments',
          table: {
            name: 'Name',
            company: 'Company',
            MustContainLeastText: 'equipment'
          },
          search: {
            title: 'Filter',
            inputCompany: 'Company',
            inputQuery: 'Search'
          },
          form: {
            title: 'Form',
            inputInitialDate: 'Initial date',
            inputFinalDate: 'final date',
            messages: {
              errorMessage: 'Depth reprocessing triggered successfully',
              successMessage: 'Depth reprocessing successfully triggered'
            }
          }
        }
      }
    },
    equipmentsId: {
      title: 'Equipment',
      titleNew: 'New Equipment',
      titleEdit: 'Edit Equipment',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data',
        telemetry: 'Telemetry',
        property: 'Property',
        integration: 'Integration'
      },
      tabData: {
        title: 'Data',
        form: {
          inputName: 'Name',
          inputCompany: 'Company',
          inputRuleOperational: 'Rule operational',
          inputDistributor: 'Distributor',
          inputSCDEKey: 'SCDE Key'
        },
        formMessages: {
          createSuccessMessage: 'Equipment created success',
          createErrorMessage: 'Error creating equipment',
          updateSuccessMessage: 'Equipment updated success',
          updateErrorMessage: 'Error updating equipment'
        }
      },
      tabTelemetry: {
        title: 'Telemetry',
        switchTelemetryProcess: 'Enable telemetry process',
        switchAutofillMissingTelemetry:
          'Enable automatic filling of missing telemetry',
        switchConsumptionProjectionProcessing:
          'Enable consumption projection processing',
        switchModulationProcess: 'Enable modulation process',
        formMessages: {
          createSuccessMessage: 'Telemetry created success',
          createErrorMessage: 'Error creating telemetry',
          updateSuccessMessage: 'Telemetry updated success',
          updateErrorMessage: 'Error updating telemetry'
        },
        modal: {
          createDashboard: {
            button: 'Create Dashboard',
            title: 'Create Dashboard',
            inputReplace: 'Replace',
            inputReplaceInfo:
              'If this option is selected, the old dashboards will be deleted. Otherwise, the current dashboard will be merged with the existing dashboards.',
            inputTemplate: 'Template',
            messages: {
              successMessage: 'Create dashboard successfully',
              errorMessage: 'Error in the create dashboard process'
            }
          },
          reprocessData: {
            button: 'Reprocess data',
            title: 'reprocess',
            inputInitialDate: 'Initial date',
            inputFinalDate: 'Final date',
            inputProperty: 'Property',
            checkInfo: 'Reprocess from accumulated variable',
            messages: {
              successMessage: 'Depth reprocessing triggered successfully',
              errorMessage: 'Error triggering reprocessing'
            }
          },
          checkMissingTelemetry: {
            button: 'Check for missing telemetry',
            title: 'Check for missing telemetry',
            alertText:
              'The maximum analysis period is 60 days between start and end.',
            inputInitialDate: 'Initial date',
            inputFinalDate: 'Final date',
            inputCheck: 'Fill points with zero value',
            messages: {
              successMessage:
                'Process of filling missing points successfully triggered.',
              errorMessage: 'Error in the process of filling in missing points.'
            }
          },
          consumptionProjection: {
            button: 'Consumption projection',
            title: 'Consumption projection',
            inputInitialDate: 'Initial date',
            inputFinalDate: 'Final date',
            messages: {
              successMessage:
                'Consumption projection process started successfully.',
              errorMessage: 'Error in the consumption projection process.'
            }
          },
          clearMeasurementdata: {
            button: 'Clear measurement data',
            title: 'Clear measurement data',
            step1: {
              inputProcessed: 'Reprocess deleted period',
              inputReprocess: 'Processed data',
              inputInitialDate: 'Measurement start date',
              inputFinalDate: 'Measurement end date',
              inputProperties: 'Properties',
              inputPropertiesWarning: 'Select processed data or property.'
            },
            step2: {
              columnData: 'Data',
              columnAffectedRows: 'Affected lines'
            },
            step3: {
              info1: 'This action is irreversible',
              info2:
                'If you wish to continue, for security reasons we ask that you confirm the PIN to complete the action!',
              buttonConfirm: 'Yes, clear measurement data'
            },
            messages: {
              successMessage:
                'Measurement removal process successfully triggered.',
              errorMessage: 'Error in the measurement removal process.'
            }
          },
          revertTelemetryFilling: {
            button: 'Reverter preenchimento de telemetria',
            title: 'Reverter preenchimento de telemetria',
            info1:
              'The maximum period to revert telemetry filling is 30 days between start and end.',
            inputInitialDate: 'Initial date',
            inputFinalDate: 'Final date',
            messages: {
              successMessage:
                'Missing points rollback process successfully triggered.',
              errorMessage: 'Error in the missing points rollback process.'
            }
          }
        },
        equipmentVirtual: {
          title: 'Composition',
          form: {
            inputEquipment: 'Equipment',
            inputOperationalRule: 'Operational rule'
          },
          formMessages: {
            createSuccessMessage: 'Composition created successfully',
            createErrorMessage: 'Error creating composition',
            updateSuccessMessage: 'Composition updated successfully',
            updateErrorMessage: 'Error updating composition'
          },
          table: {
            columns: {
              equipment: 'Composition equipment',
              operationalRule: 'Operational rule',
              actions: 'Actions'
            },
            tableWithoutData: 'No data found',
            totalRegisters: 'registers'
          },
          modalDelete: {
            title: 'Delete composition',
            textInfo: 'Are you sure you want to delete the composition?',
            textConfirm: 'Delete',
            textCancel: 'Cancel',
            messages: {
              successMessage: 'Composition removed successfully',
              errorMessage: 'Error removing composition'
            }
          }
        }
      },
      tabProperty: {
        title: 'Property',
        form: {
          inputProperty: 'Property',
          inputOffset: 'Offset',
          inputConversionFactor: 'Conversion Factor'
        },
        formMessages: {
          createSuccessMessage: 'Property created success',
          createErrorMessage: 'Error creating property',
          updateSuccessMessage: 'Property updated success',
          updateErrorMessage: 'Error updating property'
        },
        table: {
          columns: {
            name: 'Name',
            offset: 'Offset',
            conversionFactor: 'Conversion Factor',
            actions: 'Actions'
          }
        }
      },
      tabIntegration: {
        title: 'Integration',
        form: {
          inputIntegration: 'Integration',
          inputRoute: 'Route',
          inputExternalId: 'Identificator'
        },
        formMessages: {
          createSuccessMessage: 'Integration created success',
          createErrorMessage: 'Error creating integration',
          updateSuccessMessage: 'Integration updated success',
          updateErrorMessage: 'Error updating integration'
        },
        table: {
          columns: {
            integration: 'Integration',
            route: 'Route',
            externalId: 'Identificator',
            actions: 'Actions'
          }
        }
      }
    },
    'financial-statement': {
      title: 'Financial statement'
    },
    installationDocument: {
      title: 'Installation Document',
      btnNew: 'New Installation',
      table: {
        search: {
          fieldStatus: 'Period',
          fieldCompany: 'Company'
        },
        title: 'List of Reports',
        inputQuery: 'Search',
        buttonDownload: 'Send',
        columns: {
          equipment: 'Equipment',
          actions: 'Actions',
          identification: 'Identification',
          date: 'Period',
          serialNumber: 'NS Telemetry',
          status: 'Status'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete document',
          textInfo:
            'Are you sure you want to delete the document from the device:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          deleteSuccessMessage: 'Document removed successfully',
          deleteErrorMessage: 'Error removing document'
        },
        errorDownloadingReport: 'Error downloading report'
      },
      reportSubmissionModal: {
        title: 'Send report by email',
        inputEmail: 'Enter an email',
        inputCopyEmail: 'Enter email in copy (optional)',
        inputMessage: 'Enter your message (optional)',
        formMessages: {
          createSuccessMessage: 'Email successfully sent',
          createErrorMessage: 'Error sending email',
          updateSuccessMessage: '',
          updateErrorMessage: ''
        }
      }
    },
    installationDocumentId: {
      title: 'Installation Document',
      titleNew: 'New document',
      titleEdit: 'Edit document',
      tabs: {
        monitoring: 'Information',
        productivity: 'Validation'
      },
      tabMonitoring: {
        title: 'Report information',
        titleFormCustomerInformation: 'Customer Information',
        titleFormMeterConfiguration: 'Meter Setup',
        form: {
          inputCompany: 'Company',
          inputEquipment: 'Equipment',
          inputLuc: 'Luc',
          inputDate: 'Date',
          inputNSTelemetry: 'NS Telemetry',
          inputNSTelemetryInfo:
            'The selected equipment does not have an associated device',
          inputInstaller: 'Installer',
          inputNOC: 'NOC',
          inputModel: 'Model',
          inputProtocol: 'Protocol',
          inputAddress: 'Address',
          inputBaudRate: 'Baud Rate',
          inputParity: 'Parity',
          inputCurrentRelation: 'Current relation',
          inputPotentialRelation: 'Current potential',
          inputMeterConstant: 'Meter constant',
          inputPowerSystem: 'Power System'
        },
        formMessages: {
          createSuccessMessage: 'Document created success',
          createErrorMessage: 'Error creating document',
          updateSuccessMessage: 'Document updated success',
          updateErrorMessage: 'Error updating document'
        }
      },
      tabProductivity: {
        title: 'Validation information',
        columns: {
          information: 'Information',
          variable: 'Information',
          meter: 'Meter',
          pliersAmmeter: 'Pliers Ammeter',
          telemetry: 'Telemetry',
          document: 'Photos'
        },
        formMessages: {
          createSuccessMessage: 'Document created success',
          createErrorMessage: 'Error creating document',
          updateSuccessMessage: 'Document updated success',
          updateErrorMessage: 'Error updating document',
          deleteSuccessMessage: 'Variable deleted successfully',
          deleteErrorMessage: 'Error deleting variable'
        },
        reportModal: {
          title: 'Report preview',
          dateOfIssue: 'Date of issue',
          referenceMonth: 'Reference month',
          customerInformation: {
            titleClient: 'Customer Information',
            titleMeter: 'Meter Settings',
            labelCompany: 'Company',
            labelLuc: 'Identification',
            labelDate: 'Date',
            labelNsTelemetry: 'NS Telemetry',
            labelMeterModel: 'Meter Model',
            labelProtocol: 'Protocol',
            labelAddress: 'Address',
            labelBaudRate: 'Baud Rate',
            labelParity: 'Parity',
            labelCurrentRelation: 'Current Relation',
            labelPotentialRelation: 'Potential Relation',
            labelMeterConstant: 'Meter Constant',
            labelPowerSystem: 'Power System'
          },
          titleValidation: 'Validation',
          Representatives: {
            titleRepresentatives: 'Representatives',
            noc: 'NOC',
            installer: 'Installer'
          },
          backButton: 'Back',
          saveReportButton: 'Save report',
          sendingButton: 'Sending'
        },
        imageDisplayModal: {
          imageDescription: 'Equipment image',
          closeModalButton: 'Close'
        },
        documentUploadModal: {
          title: 'File upload'
        },
        modalDelete: {
          title: 'Delete Variable',
          textInfo: 'Are you sure you want to delete variable?',
          textConfirm: 'Delete',
          textCancel: 'Cancel'
        },
        previewButton: 'view preview',
        updateErrorMessage: 'Error updating document',
        deleteSuccessMessage: 'Document removed successfully',
        deleteErrorMessage: 'Error removing document'
      }
    },
    integrations: {
      title: 'Integrations',
      btnNew: 'New Integration',
      table: {
        columns: {
          name: 'Name',
          integration: 'Integration',
          account: 'Account',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete integration',
          text1: 'Are you sure you want to delete integration',
          text2:
            'After deleting an integration, you will not be able to recover the data.',
          deleteSuccessMessage: 'Integration removed successfully',
          deleteErrorMessage: 'Error removing integration'
        }
      }
    },
    integrationId: {
      title: 'Integration',
      titleNew: 'New Integration',
      titleEdit: 'Edit Integration',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data'
      },
      tabData: {
        title: 'Cadastro',
        form: {
          account: 'Account',
          name: 'Name',
          integration: 'Integration',
          token: 'Token',
          username: 'Username',
          password: 'Password',
          frequency: 'Frequency',
          delay: 'Delay'
        },
        formMessages: {
          createSuccessMessage: 'Integration created success',
          createErrorMessage: 'Error creating integration',
          updateSuccessMessage: 'Integration updated success',
          updateErrorMessage: 'Error updating integration'
        }
      }
    },
    monitoring: {
      title: 'Monitor',
      table: {
        search: {
          fieldStatus: 'Status',
          fieldType: 'Type',
          fieldQuery: 'Search'
        },
        columns: {
          equipmentId: 'Equip.ID',
          equipmentName: 'Equipment',
          serialNumber: 'Serial number',
          status: 'Status',
          version: 'Version',
          lastReading: 'Last reading',
          lastAlarm: 'Last alarm',
          template: 'Template',
          company: 'Company',
          distributor: 'Distributor',
          location: 'Location',
          connection: 'Connection',
          actions: 'Actions'
        },
        modalMeasurementCount: {
          title: 'Reprocess measurement count',
          table: {
            name: 'Equip. Name',
            company: 'Company',
            MustContainLeastText: 'equipment'
          },
          search: {
            title: 'Filter',
            inputStatus: 'Status',
            inputType: 'Type',
            inputQuery: 'Search'
          },
          form: {
            title: 'Reprocessing period',
            inputInitialDate: 'Initial date',
            inputFinalDate: 'Final date',
            messages: {
              errorMessage: 'Measurement count created successfully',
              successMessage: 'Error creating measurement count'
            }
          }
        },
        modalMonitoringDownload: {
          title: 'Download',
          form: {
            inputProbeList: 'Probe list',
            inputCountList: 'Count list',
            messages: {
              successMessage: 'File sent by email',
              errorMessage: 'Error downloading'
            }
          }
        }
      }
    },
    'monitor-alarms': {
      title: 'Monitor alarms'
    },
    'physical-assets': {
      title: 'Physical assets'
    },
    'probes-map': {
      title: 'Probes map'
    },
    properties: {
      title: 'Properties',
      btnNew: 'New Property',
      table: {
        search: {
          fieldQuery: 'Search'
        },
        columns: {
          name: 'Name',
          displayName: 'Display Name',
          actions: 'Actions',
          type: 'Tipo'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete property',
          textInfo: 'Are you sure you want to delete property?',
          deletionConfirmationText:
            'For security, we ask that you confirm the PIN to complete the delete action:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          messages: {
            successMessage: 'Property removed successfully',
            errorMessage: 'Error removing property'
          }
        }
      }
    },
    propertyId: {
      title: 'Property',
      titleNew: 'New Property',
      titleEdit: 'Edit Property',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data'
      },
      tabData: {
        title: 'Data',
        form: {
          name: 'Name',
          displayName: 'Display Name',
          description: 'Description',
          subTypes: 'Subtype',
          storeable: 'Storeable',
          type: 'Format',
          processable: 'Process'
        },
        formMessages: {
          createSuccessMessage: 'Property created success',
          createErrorMessage: 'Error creating property',
          updateSuccessMessage: 'Property updated success',
          updateErrorMessage: 'Error updating property'
        }
      }
    },
    reports: {
      title: 'Reports',
      tabs: {
        reports: 'Reports',
        generate: 'Generate report',
        historical: 'Historical'
      },
      tabReports: {
        panel: {
          title: 'Generate report',
          subTitle: 'What type of report will we generate?',
          buttonGenerateReport: 'Generate blank report'
        },
        latest: {
          title: 'Most Recent',
          seeMore: 'See more'
        },
        errorRequest: 'Error fetching report'
      },
      tabHistorical: {
        title: 'My reports',
        table: {
          search: {
            fieldQuery: 'Search'
          },
          columns: {
            name: 'Name',
            update: 'Update',
            entity: 'Entity',
            typeMeasurements: 'Type measurements',
            actions: 'actions'
          },
          modalDelete: {
            title: 'Delete report',
            textInfo: 'Are you sure you want to delete the report?',
            messages: {
              successMessage: 'report deleted successfully',
              errorMessage: 'Error deleting report'
            }
          }
        }
      },
      tabGenerate: {
        form: {
          title: 'Filter',
          entity_fields: 'Group',
          entity_type_fields: 'Record Type',
          entity_data_fields: 'Entity data',
          period_fields: 'Period',
          aggregate_fields: 'Aggregations',
          consumption: 'Consumption',
          demand: 'Demand',
          power_factor: 'Power factor',
          consumption_account: 'Access log',
          input: {
            'entity_fields.entity': 'Entity',
            'entity_type_fields.typeMeasurement': 'Type of measurements',
            'entity_type_fields.account_type': 'Record Type',
            'entity_data_fields.entity_data': 'Unit',
            'entity_data_fields.entity_data_group': 'Group data by:',
            'entity_data_fields.entity_data_account': 'Accounts',
            'period_fields.initial_date': 'Start date',
            'period_fields.final_date': 'End date',
            'period_fields.syntax_date': 'Relative period',
            'aggregate_fields.date_interval': 'Date range',
            'aggregate_fields.date_interval_number':
              'Date range Date range quantity',
            'aggregate_fields.type': 'Agregar pelo(a)',
            'type_data.consumption.tariff_post': 'Tariff Station',
            'type_data.consumption.capacity': 'Capacity',
            'type_data.consumption.consumption': 'Demand',
            'type_data.consumption.greatness': 'Greatness',
            'type_data.demand.tariff_post': 'Tariff Station',
            'type_data.demand.capacity': 'Capacity',
            'type_data.demand.consumption': 'Demand',
            'type_data.power_factor.tariff_post': 'Tariff Station',
            'type_data.power_factor.capacity': 'Capacity'
          },
          generateReport: 'Generate report',
          requiredFinalDate:
            'The end date field is required when start date is filled in.',
          requiredTypeData: 'The data type field is required',
          downloadCompact: 'Excel - compact',
          downloadDetailed: 'Excel - detailed'
        },
        table: {
          titleInfo: 'Information',
          titleTable: 'Report',
          infoGenerateReport:
            'Use the filters on the side to generate the report',
          infoOrderDate: 'Order date',
          infoRequestedPeriod: 'Requested period'
        },
        modalSave: {
          form: {
            input: {
              name: 'Name'
            },
            messages: {
              createSuccessMessage: 'Report created success',
              createErrorMessage: 'Error creating report',
              updateSuccessMessage: 'Report updated success',
              updateErrorMessage: 'Error updating report'
            }
          }
        }
      }
    },
    statistics: {
      title: "Statistics and KPI's",
      btnDownload: 'Download',
      indicatorNames: {
        'disconnections-by-period': 'Disconnections by period',
        'devices-disconnected-by-company': 'Devices disconnected by company',
        'reconnections-by-period': 'Reconnections by period',
        'disconnections-by-operator': 'Disconnections by operator',
        'new-devices': 'New devices',
        'excluded-devices': 'Excluded devices',
        'new-devices-by-type': 'New devices by type',
        'new-devices-by-subtype': 'New devices by subtype',
        'new-devices-energy-frontier': 'New devices energy frontier',
        'new-devices-energy-sectorized': 'New devices energy sectorized',
        'disconnections-by-distributor': 'Disconnections by distributor',
        'disconnections-by-location': 'Disconnections by location',
        'alarms-by-category': 'Alarms by category',
        'type-energy': 'Energy',
        'type-water': 'Water',
        'subtype-energy-frontier': 'Energy frontier',
        'subtype-energy-sectorized': 'Energy sectorized',
        'subtype-energy-sensorized': 'Energy sensorized',
        'subtype-water-frontier': 'Water frontier',
        'subtype-water-sectorized': 'Water sectorized'
      },
      tabs: {
        productivity: 'Productivity',
        monitoring: 'Monitoring'
      },
      tabProductivity: {},
      tabMonitoring: {}
    },
    statisticsProductivityIndicator: {
      title: 'Installations',
      table: {
        columns: {
          name: 'Name'
        },
        tableWithoutData: 'No data found'
      }
    },
    users: {
      title: 'Users',
      btnNew: 'New user',
      table: {
        search: {
          fieldAccounts: 'Account',
          fieldQuery: 'Search'
        },
        columns: {
          name: 'Name',
          email: 'E-mail',
          account: 'Account',
          lastAcess: 'Last acess',
          actions: 'Actions'
        },
        tableWithoutData: 'No data found',
        modalDelete: {
          title: 'Delete user',
          textInfo: 'Are you sure you want to delete the user:',
          textConfirm: 'Delete',
          textCancel: 'Cancel',
          deleteSuccessMessage: 'User successfully removed',
          deleteErrorMessage: 'Error removing user'
        }
      }
    },
    userId: {
      title: 'User',
      titleNew: 'New user',
      titleEdit: 'Edit user',
      tabs: {
        data: 'Data',
        subtitleData: 'Change and/or update registration data',
        permissions: 'Permissions',
        entities: 'Entities'
      },
      tabData: {
        title: 'Data',
        form: {
          adminField: 'Administrator',
          activeField: 'Active',
          inputFirstName: 'Name',
          inputLastName: 'Last name',
          inputEmail: 'E-mail',
          inputPincode: 'PIN',
          inputCellPhone: 'Cell phone',
          inputPassword: 'Password',
          inputPasswordConfirmation: 'Confirm Password',
          inputAccount: 'Account',
          fieldValidation: {
            inputCellPhone: 'Please enter a valid phone number',
            inputPasswordConfirmation: "The passwords don't match!"
          },

          passwordValidation: {
            passwordValidationTitle:
              'The password must be between 8 and 24 characters long, containing at least one number, one uppercase letter, one lowercase letter, and one special character (@$!%*?&#).',
            minimumCharacters: 'Password must be at least 8 characters long',
            maximumCharacters:
              'Password must be a maximum of 24 characters long',
            number: 'Password must be a maximum of 24 characters long',
            capitalLetter:
              'The password should contain at least 1 uppercase character',
            lowercaseLetter:
              'Password must contain at least one lowercase letter',
            specialCharacter:
              'Password must contain at least one special character (@$!%*?&#)',
            differentPasswords: 'Passwords do not match'
          }
        },
        formMessages: {
          createSuccessMessage: 'User created success',
          createErrorMessage: 'Error creating user',
          updateSuccessMessage: 'User updated success',
          updateErrorMessage: 'Error updating user'
        }
      },
      tabPermissions: {
        title: 'Permissions',
        columns: {
          permission: 'permission',
          list: 'list',
          create: 'create',
          delete: 'delete'
        },
        formMessages: {
          createPermissionSuccessMessage: 'Permission updated successfully',
          createPermissionErrorMessage: 'Error updating permission'
        }
      },
      tabEntities: {
        title: 'Entity',
        informationMessage:
          'No drive permissions are required for your user level.',
        formSearch: {
          fieldAccount: 'Select an account',
          fieldQuery: 'Search'
        },
        formMessages: {
          createEntitySuccessMessage: 'Entity updated successfully',
          createEntityErrorMessage: 'Error updating entity'
        },
        entitiesNotFound:
          'Your account does not yet have any enabled units, please contact our support.',
        entitiesEmpty: 'No units found matching search term.'
      }
    }
  },
  layout: {
    header: {
      menu: {
        menuItemHello: 'Hello',
        menuItemEditProfile: 'Edit profile',
        menuItemLogout: 'Exit'
      }
    },
    modal: {
      progressDeleteAccount: {
        title: 'Delete account',
        step1: {
          text1: 'Do you want to delete',
          text2:
            'After deleting an account, you will not be able to recover the data.',
          btn: 'Yes, delete'
        },
        step2: {
          text1:
            'security we ask you to confirm the PIN to complete the delete account',
          btn: 'Continue'
        }
      }
    }
  },
  pwa: {
    install: 'Install APP',
    installed: 'APP installed',
    installUnavailable: 'APP installation unavailable'
  },
  chart: {
    noData: 'No informations at the moment'
  },
  notifications: {
    title: 'Notification',
    defaulterClient:
      'You have pending payments. Please contact Finance or Manager.'
  }
}

export default tree
