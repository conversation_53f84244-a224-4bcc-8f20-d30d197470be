import { LanguageTree } from '@/types/language'

const tree: LanguageTree = {
  validationFields: {
    requiredField: 'Campo obrigatório',
    invalidEmail: 'E-mail inválido',
    MustContainLeastCaracter: (n) => `Deve conter no mínimo ${n} caractere(s)`,
    MustContainLeastItems: (n, v) => `Deve conter no mínimo ${n} ${v}`
  },
  errors: {
    request: 'Ocorreu um erro, tente novamente mais tarde',
    warning: 'Aviso'
  },
  btn: {
    add: 'Adicionar',
    save: 'Salvar',
    update: 'Atualizar',
    edit: 'Editar',
    clean: 'Limpar',
    cancel: 'Cancelar',
    remove: 'Excluir',
    close: 'Fechar',
    filter: 'Filtrar',
    confirm: 'Confirmar',
    continue: 'Continuar',
    back: 'Voltar'
  },
  form: {
    search: 'Pesquisar',
    selectAll: 'Todos',
    selectAnOption: 'Selecione uma opção'
  },
  table: {
    previous: 'Anterior',
    next: 'Próximo',
    of: 'de',
    withoutData: 'Não existem registros no momento',
    newRegister: 'Novo registro',
    totalRegisters: 'registros'
  },
  components: {
    SectionRuleTarget: {
      triggering: {
        title: 'Disparo',
        titleList: 'Disparos cadastrados'
      },
      normalization: {
        title: 'Normalização',
        titleList: 'Normalizações cadastradas'
      },
      form: {
        input: {
          rule: 'Regras',
          processed: 'Dados processados',
          property: 'Propriedades',
          operator: 'Lógica',
          value: 'Valor'
        },
        messages: {
          createSuccessMessage: 'Condicional criado com sucesso',
          createErrorMessage: 'Erro ao criar condicional',
          updateSuccessMessage: 'Condicional atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar condicional'
        },
        messageAlert:
          'Já existe uma regra cadastrada usando este mesmo operador lógico, revise as regras antes de prosseguir.',
        requiredConditionalNormalization: 'Adicione no mínimo uma normalização',
        requiredConditionalTriggering: 'Adicione no mínimo um disparo'
      },
      formDelete: {
        messages: {
          successMessage: 'Condicional removida com sucesso',
          errorMessage: 'Erro ao remover condicional'
        }
      }
    }
  },
  modalDelete: {
    infoConfirmation: (text: string) =>
      `Tem certeza que deseja deletar esse(a) ${text}?`,
    btnCancel: 'Cancelar',
    btnConfirm: (text: string) => `Deletar ${text}`,
    requestMessage: (text: string) => ({
      successMessage: `${text} deletado sucesso`,
      errorMessage: `Erro ao deletar ${text}`
    })
  },
  modal: {
    apportionment: {
      titleNew: 'Criar rateio',
      titleEdit: 'Editar rateio',
      tabs: [
        'Informações',
        'Periodo de fechamento',
        'Taxas adicionais',
        'Taxas customizada'
      ],
      tabData: {
        title: 'Informações',
        form: {
          input: {
            name: 'Nome',
            description: 'Descrição',
            company: 'Empresa',
            consumption: 'Consumo',
            consumptionInfo: 'Completar com telemetria',
            measurementUnit: 'Unidade de medida',
            unitMeasurementNotAvailable: 'Unidade de medida não disponivel',
            tariffType: 'Tarifa Padrão',
            type: 'Tipo'
          },
          messages: {
            createSuccessMessage: 'Rateio criado com sucesso',
            createErrorMessage: 'Erro ao criar rateio',
            updateSuccessMessage: 'Rateio atualizado com sucesso',
            updateErrorMessage: 'Erro ao atualizar rateio'
          }
        }
      },
      tabPeriod: {
        title: 'Periodo de fechamento',
        form: {
          input: {
            period: 'Período',
            periodStart: 'Início do período',
            periodEnd: 'Fim do período'
          },
          messages: {
            createSuccessMessage:
              'Equipamento do rateio adicionado com sucesso',
            createErrorMessage: 'Erro ao cadastrar equipamento do rateio',
            updateSuccessMessage:
              'Equipamento do rateio atualizado com sucesso',
            updateErrorMessage: 'Erro ao atualizar equipamento do rateio'
          }
        },
        table: {
          columns: {
            period: 'Período',
            periodStart: 'Início do período',
            periodEnd: 'Fim do período',
            actions: 'Ações'
          },
          modalDelete: {
            title: 'Deletar equipamento do rateio',
            textInfo:
              'Tem certeza que deseja deletar esta equipamento do rateio?',
            messages: {
              successMessage: 'Equipamento do rateio removido com sucesso',
              errorMessage: 'Erro ao remover equipamento do rateio'
            }
          }
        }
      },
      tabAdditionalFees: {
        title: 'Taxas adicionais',
        form: {
          input: {
            period: 'Período',
            name: 'Nome',
            value: 'Valor',
            type: 'Tipo'
          },
          messages: {
            createSuccessMessage: 'Taxa adicional adicionada com sucesso',
            createErrorMessage: 'Erro ao cadastrar taxa adicional',
            updateSuccessMessage: 'Taxa adicional atualizada com sucesso',
            updateErrorMessage: 'Erro ao atualizar taxa adicional'
          }
        },
        table: {
          columns: {
            period: 'Período',
            name: 'Nome',
            value: 'Valor',
            type: 'Tipo',
            actions: 'Ações'
          },
          modalDelete: {
            title: 'Deletar taxa adicional',
            textInfo: 'Tem certeza que deseja deletar esta taxa adicional?',
            messages: {
              successMessage: 'Taxa adicional removida com sucesso',
              errorMessage: 'Erro ao remover taxa adicional'
            }
          }
        }
      },
      tabCustomTariffs: {
        title: 'Taxas customizada',
        form: {
          input: {
            vigencyStart: 'Início da vigência',
            vigencyEnd: 'Fim da vigência',
            value: 'Valor'
          },
          messages: {
            createSuccessMessage: 'Tarifa customizada adicionado com sucesso',
            createErrorMessage: 'Erro ao cadastrar tarifa customizada',
            updateSuccessMessage: 'Tarifa customizada atualizado com sucesso',
            updateErrorMessage: 'Erro ao atualizar tarifa customizada'
          }
        },
        table: {
          columns: {
            vigencyStart: 'Início da vigência',
            vigencyEnd: 'Fim da vigência',
            value: 'Valor',
            actions: 'Ações'
          },
          modalDelete: {
            title: 'Deletar tarifa customizada',
            textInfo: 'Tem certeza que deseja deletar esta tarifa customizada?',
            messages: {
              successMessage: 'Tarifa customizada removido com sucesso',
              errorMessage: 'Erro ao remover tarifa customizada'
            }
          }
        }
      }
    },
    apportionmentExportData: {
      title: 'Exportar rateio (excel)',
      form: {
        input: {
          date: 'Data (mês/ano)',
          sendCostCenters: 'Exportar todos os centros de custos'
        },
        messages: {
          successMessage: 'Rateio exportado com sucesso',
          errorMessage: 'Erro ao exportar rateio'
        },
        warningWithoutPeriod: 'Aviso',
        textWithoutPeriod: 'Não existe um período cadastrado para o rateio'
      }
    },
    costCenterMultiple: {
      title: 'Criar múltiplos centros de custos',
      form: {
        inputEquipmentsAll: 'Usar todos equipamentos disponíveis',
        inputEquipmentsAllCompanyGroup: 'Incluir filiais',
        inputEquipments: 'Equipmentos',
        inputUsageLimit: 'Composição'
      }
    },
    compositionValue: {
      title: 'Novo Valor de Composição',
      form: {
        inputCostCenter: 'Centro de custo',
        inputEquipments: 'Equipamento',
        inputUsageLimit: 'Composição',
        inputCostCompositionType: 'Tipo',
        inputApportionmentGroup: 'Grupo',
        messages: {
          successMessage: 'Valor de composição criado com sucesso',
          errorMessage: 'Erro ao criar valor de composição'
        }
      }
    },
    apportionmentCalculate: {
      title: 'Calcular rateio',
      form: {
        inputCalendar: 'Data (Mês/Ano)',
        messages: {
          successMessage: 'Rateio calculado com sucesso',
          errorMessage: 'Erro ao calcular rateio'
        },
        warningWithoutPeriod: 'Aviso',
        withoutPeriodText: 'Nenhum período disponível para reprocessamento.'
      }
    },
    apportionmentGroup: {
      titleNew: 'Criar grupo',
      titleEdit: 'Editar grupo',
      form: {
        inputName: 'Nome',
        inputEquipments: 'Equipamentos'
      },
      messages: {
        createSuccessMessage: 'Grupo criado com sucesso',
        createErrorMessage: 'Erro ao criar grupo',
        updateSuccessMessage: 'Grupo atualizado com sucesso',
        updateErrorMessage: 'Erro ao atualizar grupo'
      }
    },
    costCenter: {
      titleNew: 'Criar Centro de custo',
      titleEdit: 'Editar Centro de custo'
    }
  },
  tab: {
    costCenterTabData: {
      title: 'Informações',
      form: {
        input: {
          inputName: 'Nome',
          inputEmail: 'E-mail',
          inputTariffType: 'Tarifa Padrão',
          inputTariffTypeInfo: 'Não existe tarifas para esse tipo ainda!'
        },
        messages: {
          createSuccessMessage: 'Centro de custo criado com sucesso',
          createErrorMessage: 'Erro ao criar centro de custo',
          updateSuccessMessage: 'Centro de custo atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar centro de custo'
        }
      }
    },
    costCenterTabCustomTariffs: {
      form: {
        input: {
          vigencyStart: 'Início da vigência',
          vigencyEnd: 'Fim da vigência',
          value: 'Valor'
        },
        messages: {
          createSuccessMessage: 'Tarifa customizada adicionado com sucesso',
          createErrorMessage: 'Erro ao cadastrar tarifa customizada',
          updateSuccessMessage: 'Tarifa customizada atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar tarifa customizada'
        }
      },
      table: {
        columns: {
          vigencyStart: 'Início da vigência',
          vigencyEnd: 'Fim da vigência',
          value: 'Valor',
          actions: 'Ações'
        },
        modalDelete: {
          title: 'Deletar tarifa customizada',
          textInfo: 'Tem certeza que deseja deletar esta tarifa customizada?',
          messages: {
            successMessage: 'Tarifa customizada removido com sucesso',
            errorMessage: 'Erro ao remover tarifa customizada'
          }
        }
      }
    },
    costCenterTabCustomAdditionais: {
      form: {
        input: {
          period: 'Periodo',
          value: 'Valor'
        },
        messages: {
          createSuccessMessage: 'Periodo adicionado com sucesso',
          createErrorMessage: 'Erro ao cadastrar periodo',
          updateSuccessMessage: 'Periodo atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar periodo'
        }
      },
      table: {
        columns: {
          period: 'Periodo',
          value: 'Valor',
          actions: 'Ações'
        },
        modalDelete: {
          title: 'Deletar periodo',
          textInfo: 'Tem certeza que deseja deletar esta periodo?',
          messages: {
            successMessage: 'Periodo removido com sucesso',
            errorMessage: 'Erro ao remover periodo'
          }
        }
      }
    },
    costCenterTabEquipments: {
      form: {
        input: {
          equipment: 'Equipamento',
          costCompositionType: 'Tipo',
          composition: 'Composição',
          group: 'Grupo'
        },
        messages: {
          createSuccessMessage:
            'Equipamento do centro de custo adicionado com sucesso',
          createErrorMessage:
            'Erro ao cadastrar equipamento do centro de custo',
          updateSuccessMessage:
            'Equipamento do centro de custo atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar equipamento do centro de custo'
        }
      },
      table: {
        columns: {
          equipment: 'Equipamento',
          composition: 'Composição',
          actions: 'Ações'
        },
        modalDelete: {
          title: 'Deletar equipamento do centro de custo',
          textInfo:
            'Tem certeza que deseja deletar esta equipamento do centro de custo?',
          messages: {
            successMessage:
              'Equipamento do centro de custo removido com sucesso',
            errorMessage: 'Erro ao remover equipamento do centro de custo'
          }
        }
      }
    }
  },
  pages: {
    developing: 'Página em construção',
    notFound: {
      title: 'Página não encontrada'
    },
    accessDenied: {
      userNotPartOfPlatform: {
        title: 'Desculpe, acesso negado!',
        description:
          'Você não possui acesso a este produto. Se você já adquiriu esta solução ou acredita que deveria ter acesso, por favor entre em contato com nosso suporte.',
        button: 'Conhecer o produto'
      },
      isAuthError: {
        title: 'Falha na Autenticação',
        description:
          'Não foi possível realizar o login. O serviço de autenticação apresentou uma indisponibilidade. Tente novamente mais tarde ou entre em contato com o suporte.',
        button: 'Tentar novamente'
      },
      linkSupportEmail: 'Falar com o Suporte'
    },
    accounts: {
      title: 'Contas',
      btnNew: 'Nova Conta',
      table: {
        search: {
          fieldStatus: 'Status',
          fieldQuery: 'Pesquisar'
        },
        columns: {
          name: 'Nome',
          actions: 'Ações'
        },
        modalDelete: {
          title: 'Excluir conta',
          textInfo: 'Tem certeza de que deseja excluir a conta:',
          subTextInfo:
            'Após deletar a conta, você nao poderá recuperar os dados.',
          deletionConfirmationText:
            'Por segurança, pedimos que você confirme o PIN para concluir a ação de exclusão:',
          messages: {
            successMessage: 'Conta removida com sucesso',
            errorMessage: 'Erro ao remover a conta'
          }
        }
      }
    },
    accountId: {
      title: 'Conta',
      titleNew: 'Cadastro de Conta',
      titleEdit: 'Edição de Conta',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro',
        entities: 'Permissões',
        personalization: 'Personalização',
        settings: 'Configuração',
        management: 'Gestão de conta'
      },
      tabData: {
        title: 'Cadastro',
        form: {
          inputManagement: 'Gestão',
          inputStatus: 'Status',
          inputName: 'Nome da conta'
        },
        formMessages: {
          createSuccessMessage: 'Conta criada com sucesso',
          createErrorMessage: 'Erro ao criar conta',
          updateSuccessMessage: 'Conta atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar conta'
        }
      },
      tabPersonalization: {
        titleColorDefault: 'Cor padrão',
        titleColorSuggestion: 'Cor sugestão',
        buttonApply: 'Aplicar cor',
        demo: {
          buttonLabel: 'Botões',
          buttonDefault: 'Default button',
          buttonPrimary: 'Primary button',
          icon: 'ícones',
          iconLeft: 'ícones a esquerda',
          iconRight: 'ícones a direita',
          formLabel: 'Formulário',
          fieldTextLabel: 'Campo de texto',
          fieldText: 'Nome',
          fieldSelectLabel: 'Campo de seleção',
          fieldSelect: 'Nome',
          fieldCheckboxLabel: 'Campo checkbox',
          fieldCheckbox: 'Ativo',
          fieldSwitchLabel: 'Campo de alternância',
          fieldSwitch: 'Ativo'
        }
      },
      tabSettings: {
        title: 'Configuração',
        form: {
          inputNotification: 'Ativar notificação de inadimplência'
        }
      },
      tabManagement: {
        title: 'Gestão de contas',
        form: {
          inputAccount: 'Gestão de contas'
        },
        table: {
          columns: {
            name: 'Nome',
            actions: 'Ações'
          }
        },
        messages: {
          successMessage: 'Registro atualizado com sucesso',
          errorMessage: 'Erro ao atualizar registro'
        }
      }
    },
    'agents-ccee': {
      title: 'Agentes CCEE'
    },
    alarms: {
      title: 'Alarmes',
      btnNew: 'Novo Alarme',
      table: {
        search: {
          fieldStatus: 'Status',
          fieldQuery: 'Pesquisar'
        },
        columns: {
          status: 'Status',
          name: 'Nome',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir alarme',
          textInfo: 'Tem certeza de que deseja excluir o alarme:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          deleteSuccessMessage: 'Alarme removido com sucesso',
          deleteErrorMessage: 'Erro ao remover a alarme'
        },
        updateSuccessMessage: 'Alarme atualizado com sucesso',
        updateErrorMessage: 'Erro ao atualizar alarme'
      }
    },
    alarmsId: {
      title: 'Alarme',
      titleNew: 'Cadastro de alarme',
      titleEdit: 'Edição de alarme',
      tabs: {
        data: 'Cadastro',
        rules: 'Regras',
        monitoring: 'Monitoramento',
        notification: 'Notificação',
        history: 'Histórico'
      },
      tabData: {
        title: 'Cadastro',
        subtitle: 'Altere e/ou atualize os dados do cadastro',
        form: {
          input: {
            status: 'Status',
            name: 'Nome',
            description: 'Descrição',
            category: 'Categoria',
            account: 'Conta'
          },
          messages: {
            createSuccessMessage: 'Alarme criado com sucesso',
            createErrorMessage: 'Erro ao criar alarme',
            updateSuccessMessage: 'Alarme atualizado com sucesso',
            updateErrorMessage: 'Erro ao atualizar alarme'
          }
        }
      },
      tabRules: {
        title: 'Regras',
        form: {
          target: {
            title: 'Alvo',
            input: {
              targetType: 'Tipo',
              target: 'Alvo',
              company: 'Empresa',
              placeholderCompany: 'selecione uma empresa',
              equipment: 'Equipamento',
              placeholderEquipment: 'Selecione um equipamento',
              template: 'Template',
              placeholderTemplate: 'Selecione um template'
            }
          },
          shot: {
            title: 'Disparo',
            registered: 'Disparos cadastrados'
          },
          normalized: {
            title: 'Normalizados',
            registered: 'Normalizados cadastrados'
          }
        }
      },
      tabMonitoring: {
        title: 'Frequência de monitoramento',
        form: {
          daysWeek: {
            all: 'Todos',
            monday: 'Segunda',
            tuesday: 'Terça',
            wednesday: 'Quarta',
            thursday: 'Quinta',
            friday: 'Sexta',
            saturday: 'Sábado',
            sunday: 'Domingo'
          },
          input: {
            days: 'Todos',
            initialHour: 'Horário de início',
            fimHour: 'Horário de fim',
            preActivation: 'Pré acionamento',
            daysRetention: 'Dias de retenção do log',
            daysWeekWarning: 'Selecione um dia da semana no mínimo.'
          }
          /** formMessages: IFormMessages */
        }
      },
      tabNotification: {
        title: 'Notificação',
        form: {
          input: {
            channel: 'Canal',
            frequency: 'Frequência',
            user: 'Usuário',
            email: 'E-mail'
          },
          messages: {
            createSuccessMessage: 'Notificação criado com sucesso',
            createErrorMessage: 'Erro ao criar notificação',
            updateSuccessMessage: 'Notificação atualizado com sucesso',
            updateErrorMessage: 'Erro ao atualizar notificação'
          }
        },
        table: {
          columns: {
            channel: 'Canal',
            frequency: 'Frequência',
            user: 'Usuário',
            actions: 'Ações'
          },
          modalDelete: {
            title: 'Excluir notificação',
            textInfo: 'Tem certeza de que deseja excluir a notificação:',
            textConfirm: 'Excluir',
            textCancel: 'Cancelar',
            messages: {
              successMessage: 'Notificação removida com sucesso',
              errorMessage: 'Erro ao remover a notificação'
            }
          }
        },
        btnCreateNewAlarm: 'Criar novo alarme'
      },
      tabHistorical: {
        title: 'Histórico de alarmes',
        form: {
          input: {
            status: 'Status',
            equipment: 'Equipamento'
          },
          formMessages: {
            createSuccessMessage: 'Alarme normalizado com sucesso',
            createErrorMessage: 'Erro ao normalizar o alarme'
          }
        },
        table: {
          columns: {
            status: 'Status',
            company: 'Empresa',
            equipment: 'Equipamento',
            triggered: 'Acionado em:',
            normalized: 'Normalizado em:',
            actions: 'Ações'
          }
        },
        modalNormalizeAlarm: {
          title: 'Reconhecimento do alarme',
          textInfo:
            'Esta ação irá normalizar este acionamento de alarme, tenha certeza desta ação antes de prosseguir.',
          buttonConfirm: 'Sim, normalizar alarme',
          buttonCancel: 'Cancelar'
        }
      }
    },
    apportionments: {
      title: 'Rateios',
      btnNew: 'Novo Rateio',
      table: {
        search: {
          fieldQuery: 'Pesquisar'
        },
        columns: {
          name: 'Nome',
          company: 'Empresa',
          type: 'Tipo',
          tariff: 'Tarifa',
          cdcQuantity: 'Qtde. CDC',
          cdcValue: 'Valor CDC',
          actions: 'Ações'
        },
        modalDelete: {
          title: 'Excluir rateio',
          textInfo: 'Tem certeza de que deseja excluir o rateio:',
          messages: {
            successMessage: 'Rateio removido com sucesso',
            errorMessage: 'Erro ao remover o rateio'
          }
        }
      }
    },
    apportionmentsId: {
      title: 'Rateio',
      dropdownItems: [
        'Editar informações',
        'Criar centro de custo',
        'Criar grupo',
        'Criar valor de composição',
        'Criar múltiplos centros de custoso',
        'Calcular rateio',
        'Exportar dados',
        'Excluir'
      ],
      sectionApportionment: {
        title: 'Informações gerais',
        table: {
          columns: {
            company: 'Empresa',
            apportionmentType: 'Tipo',
            tariff: 'Tarifa',
            costCenterLength: 'Qtde CDC',
            totalValueCurrent: 'Valor CDC'
          }
        }
      },
      sectionGroups: {
        title: 'Grupos',
        table: {
          columns: {
            name: 'Nome',
            equipments: 'Qnt equipamento',
            actions: 'Ações'
          },
          modalDelete: {
            title: 'grupo',
            textInfo: 'Tem certeza de que deseja excluir o grupo:',
            textConfirm: 'Excluir',
            textCancel: 'Cancelar',
            messages: {
              successMessage: 'Grupo removido com sucesso',
              errorMessage: 'Erro ao remover a grupo'
            }
          }
        }
      },
      sectionCostCenter: {
        title: 'Centro de Custo',
        table: {
          columns: {
            name: 'Nome',
            tariffs: 'Tarifas',
            equipments: 'Qnt equipamento',
            actions: 'Ações'
          },
          modalDelete: {
            title: 'centro de custo',
            textInfo: 'Tem certeza de que deseja excluir o centro de custo:',
            textConfirm: 'Excluir',
            textCancel: 'Cancelar',
            messages: {
              successMessage: 'Centro de custo removido com sucesso',
              errorMessage: 'Erro ao remover a centro de custo'
            }
          }
        }
      },
      sectionResultsHistory: {
        title: 'Histórico de resultados',
        table: {
          columns: {
            date: 'Data',
            constCenter: 'Centro de custo',
            tariffType: 'Tipo de tarifa',
            tariff: 'Tarifa',
            amountConsumer: 'Consumo total'
          }
        },
        graphic: {
          title: 'Composição de custos'
        },
        withoutData: 'Nenhum dado encontrado'
      }
    },
    companies: {
      title: 'Empresas',
      btnNew: 'Nova Empresa',
      table: {
        search: {
          fieldAccounts: 'Conta',
          fieldType: 'Tipo',
          fieldQuery: 'Pesquisar'
        },
        columns: {
          id: 'Id',
          name: 'Nome',
          contact: 'Contato',
          account: 'conta',
          parent: 'Empresa pai',
          state: 'Estado',
          type: 'Tipo',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir empresa',
          textInfo: 'Tem certeza de que deseja excluir a empresa:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          deleteSuccessMessage: 'Empresa removida com sucesso',
          deleteErrorMessage: 'Erro ao remover a empresa'
        }
      }
    },
    companyId: {
      title: 'Empresa',
      titleNew: 'Nova Empresa',
      titleEdit: 'Editar Empresa',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro',
        contacts: 'Contatos',
        salesForce: 'Salesforce'
      },
      tabData: {
        title: 'Dados',
        form: {
          inputAccount: 'Conta',
          inputSocialName: 'Nome Social',
          inputName: 'Nome Fantasia',
          inputCnpj: 'CNPJ',
          inputCnae: 'CNAE',
          inputConsumerUnitCode: 'Código unidade consumidora',
          inputCompany: 'Empresa',
          inputZipCode: 'CEP',
          inputState: 'Estado',
          inputCity: 'Cidade',
          inputNeighborhood: 'Bairro',
          inputStreet: 'Rua/Avenida',
          inputNumber: 'Número',
          inputComplement: 'Complemento',
          inputTimezone: 'Timezone',
          inputType: 'Tipo'
        },
        formMessages: {
          createSuccessMessage: 'Empresa criada com sucesso',
          createErrorMessage: 'Erro ao criar empresa',
          updateSuccessMessage: 'Empresa atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar empresa',
          errorWhenSearchingZipCode: 'Erro ao buscar por CEP'
        }
      },
      tabContacts: {
        title: 'Contatos',
        form: {
          inputName: 'Nome',
          inputEmail: 'Email',
          inputCellphone: 'Celular',
          inputCategory: 'Categoria'
        },
        formMessages: {
          createSuccessMessage: 'Contato criado com sucesso',
          createErrorMessage: 'Erro ao criar contato',
          updateSuccessMessage: 'Contato atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar contato'
        },
        table: {
          columns: {
            name: 'Nome',
            email: 'Email',
            cellphone: 'Celular',
            category: 'Categoria',
            actions: 'Ações'
          },
          tableWithoutData: 'Nenhum dado encontrado'
        },
        modalDelete: {
          title: 'Excluir contato',
          textInfo: 'Tem certeza de que deseja excluir o contato:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          messages: {
            successMessage: 'Contato removido com sucesso',
            errorMessage: 'Erro ao remover o contato'
          }
        }
      },
      tabSalesforce: {
        title: 'Salesforce',
        form: {
          inputExternalId: 'Código loja',
          inputSource: 'Fonte'
        },
        formMessages: {
          createSuccessMessage: 'Salesforce criado com sucesso',
          createErrorMessage: 'Erro ao criar salesforce',
          updateSuccessMessage: 'Salesforce atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar salesforce'
        },
        table: {
          columns: {
            externalId: 'Código loja',
            source: 'Fonte',
            actions: 'Ações'
          },
          tableWithoutData: 'Nenhum dado encontrado'
        },
        modalDelete: {
          title: 'Excluir salesforce',
          textInfo: 'Tem certeza de que deseja excluir salesforce:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          messages: {
            successMessage: 'Salesforce removido com sucesso',
            errorMessage: 'Erro ao remover o salesforce'
          }
        }
      }
    },
    contracts: {
      title: 'Contrato'
    },
    dashboard: {
      title: 'Dashboard',
      createDashboardModal: {
        title: 'Criar dashboard',
        inputReplace: 'Substituir',
        inputReplaceInfo:
          'Caso esta opção seja selecionada, as dashboards antigas serão substituídas. Caso contrário, a dashboard atual será combinada com as dashboards já existentes.',
        inputEntity: 'Criar dashboard para:',
        inputAccount: 'Conta',
        inputTemplate: 'Template',
        inputSearch: 'Pesquisar',
        submitMessages: {
          successMessage: 'Dashboard criada com sucesso',
          errorMessage: 'Erro ao criar dashboard'
        }
      },
      deleteDashboardModal: {
        title: 'Excluir dashboard',
        inputAccount: 'Conta',
        inputSearch: 'Pesquisar',
        submitMessages: {
          successMessage: 'Dashboard deletado com sucesso',
          errorMessage: 'Erro ao deletar dashboard'
        }
      },
      createAlarmModal: {
        title: 'Criar Alarme',
        form: {
          input: {
            name: 'Nome',
            frequency: 'Frequencia',
            email: 'E-mail',
            placeholderEmail: 'Digite e pressione Enter para adicionar'
          },
          messages: {
            successMessage: 'Alarme criado com sucesso',
            errorMessage: 'Erro ao criar alarme'
          }
        }
      }
    },
    devices: {
      title: 'Dispositivos',
      btnNew: 'Novo Dispositivo',
      formSearch: {
        fieldCompany: 'Empresa',
        fieldQuery: 'Pesquisar'
      },
      table: {
        search: {
          fieldCompany: 'Empresa',
          fieldQuery: 'Pesquisar'
        },
        columns: {
          type: 'Tipo',
          status: 'Status',
          code: 'Número de série',
          company: 'Empresa',
          equipment: 'Equipmento',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir dispositivo',
          textInfo:
            'Tem certeza de que deseja excluir o dispositivo de código:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          deleteSuccessMessage: 'Dispositivo removido com sucesso',
          deleteErrorMessage: 'Erro ao remover o dispositivo'
        }
      }
    },
    devicesId: {
      title: 'Dispositivo',
      titleNew: 'Cadastro de Dispositivo',
      titleEdit: 'Edição de Dispositivo',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro',
        constant: 'Constantes',
        installation: 'Instalações',
        billings: 'Faturamento'
      },
      tabData: {
        title: 'Cadastro',
        form: {
          inputCode: 'Número de série',
          inputMaster: 'Master',
          inputEquipment: 'Equipmento',
          inputType: 'Tipo',
          inputModel: 'Modelo',
          inputHourlyFrequency: 'Frequência horária',
          inputAlertSeconds: 'Alerta em Segundos',
          inputConnection: 'Conexão',
          inputUrlFirmware: 'URL Firmware',
          inputVersion: 'Versão',
          inputIccid: 'ICCID',
          inputSsidOp: 'SSID OP'
        },
        formMessages: {
          createSuccessMessage: 'Dispositivo criada com sucesso',
          createErrorMessage: 'Erro ao criar dispositivo',
          updateSuccessMessage: 'Dispositivo atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar dispositivo'
        },
        actionObservation: {
          add: 'Colocar em observação',
          remove: 'Remover de observação'
        },
        modalObservationData: {
          title: 'Observação'
        },
        modalObservation: {
          title: 'Colocar dispositivo em observação',
          textInfo: 'Descreva o motivo de colocar o dispositivo em observação'
        },
        modalRemoveObservation: {
          title: 'Remover dispositivo do status de observação',
          textInfo:
            'Tem certeza que deseja remover este dispositivo do status de Observação?',
          textConfirm: 'Sim, remover o dispositivo de observação'
        },
        requestStatusObservation: {
          successMessage: 'Status do dispositivo alterado com sucesso',
          errorMessage: 'Erro ao alterar status do dispositivo'
        }
      },
      tabConstants: {
        title: 'Constantes',
        form: {
          inputInitialDate: 'Data inícial',
          inputCurrentRelation: 'Relação corrente TC',
          inputPotentialRelation: 'Relação potencial TP',
          inputMeterConstant: 'Medidor KE',
          inputLossFactor: 'Perda'
        },
        formMessages: {
          createSuccessMessage: 'Constante criada com sucesso',
          createErrorMessage: 'Erro ao criar constante',
          updateSuccessMessage: 'Constante atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar constante'
        },
        table: {
          columns: {
            initialDate: 'Data inícial',
            currentRelation: 'Relação corrente TC',
            potentialRelation: 'Relação potencial TP',
            meterConstant: 'Medidor KE',
            lossFactor: 'Perda',
            actions: 'Ações'
          },
          tableWithoutData: 'Não foram encontrados dados'
        },
        modalDelete: {
          title: 'Deletar Constante',
          textInfo: 'Tem certeza que deseja deletar a constante',
          textConfirm: 'Sim, deletar constante',
          textCancel: 'Cancelar',
          messages: {
            successMessage: 'Dispositivo deletado sucesso',
            errorMessage: 'Erro ao deletar dispositivo'
          },
          textwarning:
            'Após deletar a constante, você não poderá recuperar os dados.',
          values: {
            initialDate: 'Data inícial',
            currentRelation: 'Relação corrente TC',
            potentialRelation: 'Relação potencial TP',
            meterConstant: 'Medidor KE',
            lossFactor: 'Perda'
          }
        }
      },
      tabInstallations: {
        title: 'Instalação',
        form: {
          key: 'Chave',
          value: 'Valor'
        },
        formMessages: {
          createSuccessMessage: 'Configuração de instalação criada com sucesso',
          createErrorMessage: 'Erro ao criar configuração de instalação',
          updateSuccessMessage:
            'Configuração de instalação atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar configuração de instalação'
        },
        table: {
          columns: {
            keyName: 'Nome da chave',
            value: 'Valor',
            actions: 'Ações'
          },
          tableWithoutData: 'Não foram encontrados dados'
        },
        modalDelete: {
          title: 'Deletar configuração de instalação',
          textInfo:
            'Tem certeza que deseja deletar a configuração de instalação.',
          textConfirm: 'Sim, deletar configuração de instalação',
          textCancel: 'Cancelar',
          messages: {
            successMessage: 'Configuração de instalação deletada sucesso',
            errorMessage: 'Erro ao deletar configuração de instalação'
          },
          textwarning:
            'Após deletar a configuração de instalação, você não poderá recuperar os dados.',
          values: {
            keyName: 'Nome da configuração'
          }
        }
      },
      tabBillings: {
        title: 'Faturamento',
        form: {
          openDate: 'Abertura',
          openComment: 'Comentário de abertura',
          closeDate: 'Fechamento',
          closeComment: 'Comentário de fechamento'
        },
        formMessages: {
          createSuccessMessage: 'Faturamento criado com sucesso',
          createErrorMessage: 'Erro ao criar faturamento',
          updateSuccessMessage: 'Faturamento atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar faturamento'
        },
        table: {
          columns: {
            openDate: 'Abertura',
            openComment: 'Comentário de abertura',
            closeDate: 'Fechamento',
            closeComment: 'Comentário de fechamento'
          }
        }
      }
    },
    distributors: {
      title: 'Distribuidoras',
      btnNew: 'Nova Distribuidora',
      table: {
        search: {
          fieldType: 'Tipo',
          fieldQuery: 'Pesquisar',
          buttonModalUpdateTariffs: 'Atualizar tarifas'
        },
        columns: {
          name: 'Nome',
          type: 'Tipo',
          state: 'Estado',
          submarket: 'Submercado',
          actions: 'Ações'
        },
        tableWithoutData: 'Nenhum dado encontrado',
        modalDelete: {
          title: 'Deletar Distribuidora',
          textInfo: 'Desejar excluir',
          textwarning:
            'Após deletar a distribuidora, você nao poderá recuperar os dados.',
          textConfirm: 'Sim, excluir distribuidora',
          messages: {
            successMessage: 'Distribuidora excluida com sucesso',
            errorMessage: 'Erro ao excluir distribuidora'
          }
        }
      },
      modalTariffs: {
        title: 'Atualizar tarifas',
        fieldDate: 'Data'
      }
    },
    distributorId: {
      title: 'Distribuidora',
      titleNew: 'New Distribuidora',
      titleEdit: 'Edit Distribuidora',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro',
        tariffs: 'Tarifa'
      },
      tabData: {
        title: 'Dados',
        form: {
          inputName: 'Nome',
          inputCnpj: 'CNPJ',
          inputExternalId: 'Código Loja',
          inputType: 'Tipo',
          inputSubmarket: 'Submercado',
          inputState: 'Estado',
          inputPeakStartTime: 'Início do horário ponta',
          inputPeakEndTime: 'Término do horário ponta',
          inputReactiveStartTime: 'Inicio de horário reativo',
          inputReactiveEndTime: 'Término do Horário Reativo',
          inputIntermediateStartTime: 'Inicio do horário intermediário',
          inputIntermediateEndTime: 'Término do horário intermediário'
        },
        formMessages: {
          createSuccessMessage: 'Distribuidora criado com sucesso',
          createErrorMessage: 'Error ao criar distribuidora',
          updateSuccessMessage: 'Distribuidora atualizado com sucesso',
          updateErrorMessage: 'Error ao atualizar distribuidora'
        }
      },
      tabTariffs: {
        title: 'Tarifas',
        table: {
          columns: {
            tariffModalityName: 'Modalidade',
            voltageClassName: 'Classe',
            type: 'Tipo',
            vigencyStart: 'Início vigência',
            vigencyEnd: 'Fim vigência',
            tusdMwhCaptive: 'TUSD cativo MWh',
            tusdKwCaptive: 'TUSD cativo kW',
            tusdMwh: 'TUSD MWh',
            tusdKw: 'TUSD kW',
            teMwh: 'TE MWh'
          },
          tableWithoutData: 'Nenhum dado encontrado'
        }
      }
    },
    'energetic-statement': {
      title: 'Balanço energético'
    },
    equipments: {
      title: 'Equipamentos',
      btnNew: 'Novo equipamento',
      table: {
        search: {
          fieldCompany: 'Empresa',
          fieldQuery: 'Pesquisar',
          fieldAccount: 'Conta'
        },
        columns: {
          name: 'Nome',
          companyName: 'Empresa',
          ruleOperationalTitle: 'Regra operacional',
          accountName: 'Conta',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir equipamento',
          textInfo: 'Tem certeza que deseja deletar a equipamento',
          textwarning:
            'Após deletar o equipamento, você não poderá recuperar os dados.',
          deleteSuccessMessage: 'Equipamento removida com sucesso',
          deleteErrorMessage: 'Erro ao remover equipamento'
        },
        modalReprocessEquipment: {
          title: 'Reprocessar equipamentos',
          table: {
            name: 'Nome',
            company: 'Empresa',
            MustContainLeastText: 'equipamento'
          },
          search: {
            title: 'Filtro',
            inputCompany: 'Empresa',
            inputQuery: 'Pesquisar'
          },
          form: {
            title: 'Formulário',
            inputInitialDate: 'Data início',
            inputFinalDate: 'Data final',
            messages: {
              errorMessage: 'Erro ao disparar reprocessamento',
              successMessage:
                'Reprocessamento de profundidade disparado com sucesso'
            }
          }
        }
      }
    },
    equipmentsId: {
      title: 'Equipamento',
      titleNew: 'New Equipamento',
      titleEdit: 'Edit Equipamento',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro',
        telemetry: 'Telemetria',
        property: 'Propriedade',
        integration: 'Integração'
      },
      tabData: {
        title: 'Dados',
        form: {
          inputName: 'Nome',
          inputCompany: 'Empresa',
          inputRuleOperational: 'Regra operacional',
          inputDistributor: 'DIstribuidora',
          inputSCDEKey: 'Chave SCDE'
        },
        formMessages: {
          createSuccessMessage: 'Equipamento criado com sucesso',
          createErrorMessage: 'Erro ao criar equipamento',
          updateSuccessMessage: 'Equipamento atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar equipamento'
        }
      },
      tabTelemetry: {
        title: 'Telemetry',
        switchTelemetryProcess: 'Habilitar processo de telemetria',
        switchAutofillMissingTelemetry:
          'Habilitar preenchimento automático de telemetria faltante',
        switchConsumptionProjectionProcessing:
          'Habilitar processamento de projeção de consumo',
        switchModulationProcess: 'Habilitar processo de modulação',
        formMessages: {
          createSuccessMessage: 'Telemetria criado com sucesso',
          createErrorMessage: 'Erro ao criar telemetria',
          updateSuccessMessage: 'Telemetria atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar telemetria'
        },
        modal: {
          createDashboard: {
            button: 'Criar Dashboard',
            title: 'Criar Dashboard',
            inputReplace: 'Substituir',
            inputReplaceInfo:
              'Caso esta opção seja selecionada, as dashboards antigas serão substituídas. Caso contrário, a dashboard atual será combinada com as dashboards já existentes.',
            inputTemplate: 'Template',
            messages: {
              successMessage: 'Dashboard criada com sucesso',
              errorMessage: 'Erro ao criar Dashboard'
            }
          },
          reprocessData: {
            button: 'Reprocessar dados',
            title: 'Reprocessar',
            inputInitialDate: 'Data iniício',
            inputFinalDate: 'Data final',
            inputProperty: 'Propriedade',
            checkInfo: 'Reprocessar a partir da variável acumulada',
            messages: {
              successMessage:
                'Reprocessamento de medições disparado com sucesso',
              errorMessage: 'Erro ao disparar reprocessamento'
            }
          },
          checkMissingTelemetry: {
            button: 'Verificar telemetria faltante',
            title: 'Verificar telemetria faltante',
            alertText:
              'O período máximo de análise é de 60 dias entre início e fim.',
            inputInitialDate: 'Data iniício',
            inputFinalDate: 'Data final',
            inputCheck: 'Preencher pontos com valor zero',
            messages: {
              successMessage:
                'Processo de preenchimento de pontos faltantes disparado com sucesso.',
              errorMessage:
                'Erro no processo de preenchimento de pontos faltantes.'
            }
          },
          consumptionProjection: {
            button: 'Projeção de consumo',
            title: 'Projeção de consumo',
            inputInitialDate: 'Data iniício',
            inputFinalDate: 'Data final',
            messages: {
              successMessage:
                'Processo de projeção de consumo iniciado com sucesso.',
              errorMessage: 'Erro no processo de projeção de consumo.'
            }
          },
          clearMeasurementdata: {
            button: 'Limpar dados de medição',
            title: 'Limpar dados de medição',
            step1: {
              inputProcessed: 'Reprocessar período excluído',
              inputReprocess: 'Dados processados',
              inputInitialDate: 'Data início da medição',
              inputFinalDate: 'Data fim da medição',
              inputProperties: 'Propriedades',
              inputPropertiesWarning:
                'Selecione dados processados ou propriedade.'
            },
            step2: {
              columnData: 'Dados',
              columnAffectedRows: 'Linhas afetadas'
            },
            step3: {
              info1: 'Esta ação é irreversível,',
              info2:
                'caso deseja continuar, por segurança pedimos que confirme o PIN para completar a ação!',
              buttonConfirm: 'Sim, limpar dados de medição'
            },
            messages: {
              successMessage:
                'Processo de remoção de medições disparado com sucesso.',
              errorMessage: 'Erro no rocesso de remoção de medições.'
            }
          },
          revertTelemetryFilling: {
            button: 'Reverter preenchimento de telemetria',
            title: 'Reverter preenchimento de telemetria',
            info1:
              'O período máximo para reverter o preenchimento de telemetria é de 30 dias entre início e fim.',
            inputInitialDate: 'Data iniício',
            inputFinalDate: 'Data final',
            messages: {
              successMessage:
                'Processo de rollback de pontos faltantes disparado com sucesso.',
              errorMessage: 'Erro no processo de Reverter preenchimento.'
            }
          }
        },
        equipmentVirtual: {
          title: 'Composição',
          form: {
            inputEquipment: 'Equipamento',
            inputOperationalRule: 'Regra operacional'
          },
          formMessages: {
            createSuccessMessage: 'Composição criada com sucesso',
            createErrorMessage: 'Erro ao criar composição',
            updateSuccessMessage: 'Composição atualizada com sucesso',
            updateErrorMessage: 'Erro ao atualizar composição'
          },
          table: {
            columns: {
              equipment: 'Equipamento de composição',
              operationalRule: 'Regra operacional',
              actions: 'Ações'
            },
            tableWithoutData: 'Nenhum dado encontrado',
            totalRegisters: 'registros'
          },
          modalDelete: {
            title: 'Excluir composição',
            textInfo: 'Tem certeza de que deseja excluir a composição:',
            textConfirm: 'Excluir',
            textCancel: 'Cancelar',
            messages: {
              successMessage: 'Composição removida com sucesso',
              errorMessage: 'Erro ao remover o composição'
            }
          }
        }
      },
      tabProperty: {
        title: 'Propriedade',
        form: {
          inputProperty: 'Propriedade',
          inputOffset: 'Offset',
          inputConversionFactor: 'Fator de conversão'
        },
        formMessages: {
          createSuccessMessage: 'Propriedade criada com sucesso',
          createErrorMessage: 'Erro ao criar propriedade',
          updateSuccessMessage: 'Propriedade atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar propriedade'
        },
        table: {
          columns: {
            name: 'Nome',
            offset: 'Offset',
            conversionFactor: 'Fator de conversão',
            actions: 'Ações'
          }
        }
      },
      tabIntegration: {
        title: 'Integração',
        form: {
          inputIntegration: 'Integração',
          inputRoute: 'Rota',
          inputExternalId: 'Identificador'
        },
        formMessages: {
          createSuccessMessage: 'Integração criada com sucesso',
          createErrorMessage: 'Erro ao criar integração',
          updateSuccessMessage: 'Integração atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar integração'
        },
        table: {
          columns: {
            integration: 'Integração',
            route: 'Rota',
            externalId: 'Identificador',
            actions: 'Ações'
          }
        }
      }
    },
    'financial-statement': {
      title: 'Balanço financeiro'
    },
    monitoring: {
      title: 'Monitoramento',
      table: {
        search: {
          fieldStatus: 'Status',
          fieldType: 'Tipo',
          fieldQuery: 'Pesquisar'
        },
        columns: {
          equipmentId: 'Equip.ID',
          equipmentName: 'Equipamento',
          serialNumber: 'Nº de Serie',
          status: 'Status',
          version: 'Versão',
          lastReading: 'Última leitura',
          lastAlarm: 'Último alarme',
          template: 'Template',
          company: 'Empresa',
          distributor: 'Distribuidora',
          location: 'Localização',
          connection: 'Conexão',
          actions: 'Ações'
        },
        modalMeasurementCount: {
          title: 'Reprocessar contagem de medição',
          table: {
            name: 'Equip. Nome',
            company: 'Empresa',
            MustContainLeastText: 'Equipamento'
          },
          search: {
            title: 'Filtros',
            inputStatus: 'Status',
            inputType: 'Tipo',
            inputQuery: 'Pesquisar'
          },
          form: {
            title: 'Período de reprocessamento',
            inputInitialDate: 'Data início',
            inputFinalDate: 'Data fim',
            messages: {
              errorMessage: 'Erro ao criar contagem de medição',
              successMessage: 'Contagem de medições criada com sucesso'
            }
          }
        },
        modalMonitoringDownload: {
          title: 'Download',
          form: {
            inputProbeList: 'Lista de probes',
            inputCountList: 'Lista de contagem',
            messages: {
              successMessage: 'Arquivo enviado por e-mail',
              errorMessage: 'Erro ao fazer download'
            }
          }
        }
      }
    },
    installationDocument: {
      title: 'Documento de instalação',
      btnNew: 'Nova instalação',
      table: {
        search: {
          fieldStatus: 'Periodo',
          fieldCompany: 'Empresa'
        },
        title: 'Lista de Relatórios',
        inputQuery: 'Pesquisar',
        buttonDownload: 'Enviar',
        columns: {
          equipment: 'Equipamento',
          actions: 'Ações',
          identification: 'Identificação',
          date: 'Periodo',
          serialNumber: 'NS Telemetria',
          status: 'Status'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir documento',
          textInfo:
            'Tem certeza que deseja excluir o documento do dispositivo:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          deleteSuccessMessage: 'Documento removido com sucesso',
          deleteErrorMessage: 'Erro ao remover documento'
        },
        errorDownloadingReport: 'Erro ao baixar o relatório'
      },
      reportSubmissionModal: {
        title: 'Enviar relatório por e-mail',
        inputEmail: 'Digite um e-mail',
        inputCopyEmail: 'Digite e-mail em cópia(opcional)',
        inputMessage: 'Digite sua mensagem(opcional)',
        formMessages: {
          createSuccessMessage: 'E-mail enviado com sucesso',
          createErrorMessage: 'Erro ao enviar e-mail',
          updateSuccessMessage: '',
          updateErrorMessage: ''
        }
      }
    },
    installationDocumentId: {
      title: 'Documento de instalação',
      titleNew: 'Novo documento',
      titleEdit: 'Edição de documento',
      tabs: {
        monitoring: 'Informações',
        productivity: 'Validação'
      },
      tabMonitoring: {
        title: 'Informações do relatório',
        titleFormCustomerInformation: 'Informações do cliente',
        titleFormMeterConfiguration: 'Configuração do Medidor',
        form: {
          inputCompany: 'Empresa',
          inputEquipment: 'Equipamento',
          inputLuc: 'Luc',
          inputDate: 'Data',
          inputNSTelemetry: 'NS Telemetria',
          inputNSTelemetryInfo:
            'O equipamento selecionado não possui um dispositivo associado',
          inputNOC: 'NOC',
          inputInstaller: 'Instalador',
          inputModel: 'Modelo',
          inputProtocol: 'Protocolo',
          inputAddress: 'Endereço',
          inputBaudRate: 'Baud Rate',
          inputParity: 'Paridade',
          inputCurrentRelation: 'Relação corrente TC',
          inputPotentialRelation: 'Relação potencial TP',
          inputMeterConstant: 'Medidor KE',
          inputPowerSystem: 'Sistema de Potência'
        },
        formMessages: {
          createSuccessMessage: 'Relatório criado com sucesso',
          createErrorMessage: 'Erro ao criar relatório',
          updateSuccessMessage: 'Relatório atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar relatório'
        }
      },
      tabProductivity: {
        title: 'Informações de validação',
        columns: {
          variable: 'Variável',
          information: 'Informações',
          meter: 'Medidor',
          pliersAmmeter: 'Alicate Amperímetro',
          telemetry: 'Telemetria',
          document: 'Fotos'
        },
        formMessages: {
          createSuccessMessage: 'Documento criado com sucesso',
          createErrorMessage: 'Erro ao criar documento',
          updateSuccessMessage: 'Documento atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar o documento',
          deleteSuccessMessage: 'Variável excluída com sucesso',
          deleteErrorMessage: 'Erro ao excluir variável'
        },
        reportModal: {
          title: 'Prévia do relatório',
          dateOfIssue: 'Data de emissão',
          referenceMonth: 'Mês de referência',
          customerInformation: {
            titleClient: 'Informação do Cliente',
            titleMeter: 'Configurações do medidor',
            labelCompany: 'Empresa',
            labelLuc: 'Identificação',
            labelDate: 'Data',
            labelNsTelemetry: 'NS Telemetria',
            labelMeterModel: 'Modelo Medidor',
            labelProtocol: 'Protocolo',
            labelAddress: 'Endereço',
            labelBaudRate: 'Baud Rate',
            labelParity: 'Paridade',
            labelCurrentRelation: 'Relação TC',
            labelPotentialRelation: 'Relação TP',
            labelMeterConstant: 'Constante Medidor',
            labelPowerSystem: 'Sistema de Potência'
          },
          titleValidation: 'Validação',
          Representatives: {
            titleRepresentatives: 'Representantes',
            noc: 'NOC',
            installer: 'Instalador'
          },
          backButton: 'Voltar',
          saveReportButton: 'Salvar relatório',
          sendingButton: 'Enviando'
        },
        imageDisplayModal: {
          imageDescription: 'Imagem do equipamento',
          closeModalButton: 'Fechar'
        },
        documentUploadModal: {
          title: 'Upload de arquivo'
        },
        modalDelete: {
          title: 'Excluir variável',
          textInfo: 'Tem certeza que deseja excluir a variável?',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar'
        },
        previewButton: 'visualizar prévia',
        updateErrorMessage: 'Erro ao atualizar documento',
        deleteSuccessMessage: 'Documento removido com sucesso',
        deleteErrorMessage: 'Erro ao remover documento'
      }
    },
    integrations: {
      title: 'Integrações',
      btnNew: 'Nova Integração',
      table: {
        columns: {
          name: 'Nome',
          integration: 'Integração',
          account: 'Conta',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir integração',
          text1: 'Tem certeza de que deseja excluir a integração',
          text2:
            'Após deletar a integração, você não poderá recuperar os dados.',
          deleteSuccessMessage: 'Integração removida com sucesso',
          deleteErrorMessage: 'Erro ao remover integração'
        }
      }
    },
    integrationId: {
      title: 'Integração',
      titleNew: 'Nova Integração',
      titleEdit: 'Editar Integração',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro'
      },
      tabData: {
        title: 'Cadastro',
        form: {
          account: 'Conta',
          name: 'Nome',
          integration: 'Integração',
          token: 'Token',
          username: 'Username',
          password: 'Senha',
          frequency: 'Frequencia',
          delay: 'Delay'
        },
        formMessages: {
          createSuccessMessage: 'Integração criada com sucesso',
          createErrorMessage: 'Erro ao criar integração',
          updateSuccessMessage: 'Integração atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar integração'
        }
      }
    },
    'monitor-alarms': {
      title: 'Monitor alarms'
    },
    'physical-assets': {
      title: 'Unidades'
    },
    'probes-map': {
      title: 'Mapa de Probes'
    },
    properties: {
      title: 'Propriedades',
      btnNew: 'Nova propriedade',
      table: {
        search: {
          fieldQuery: 'Pesquisar'
        },
        columns: {
          name: 'Nome',
          displayName: 'Nome de Exibição',
          type: 'Tipo',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir propriedade',
          textInfo: 'Tem certeza de que deseja excluir a propriedade?',
          deletionConfirmationText:
            'Por segurança, pedimos que você confirme o PIN para concluir a ação de exclusão:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          messages: {
            successMessage: 'Propriedade removida com sucesso',
            errorMessage: 'Erro ao remover propriedade'
          }
        }
      }
    },
    propertyId: {
      title: 'Propriedade',
      titleNew: 'Cadastro de Propriedade',
      titleEdit: 'Edição de Propriedade',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro'
      },
      tabData: {
        title: 'Cadastro',
        form: {
          name: 'Nome',
          displayName: 'Nome de Exibição',
          description: 'Descrição',
          subTypes: 'Subtipos',
          storeable: 'Armazenar',
          type: 'Formato',
          processable: 'Processar'
        },
        formMessages: {
          createSuccessMessage: 'Propriedade criada com sucesso',
          createErrorMessage: 'Erro ao criar propriedade',
          updateSuccessMessage: 'Propriedade atualizada com sucesso',
          updateErrorMessage: 'Erro ao atualizar propriedade'
        }
      }
    },
    reports: {
      title: 'Relatórios',
      tabs: {
        reports: 'Relatórios',
        generate: 'Gerar relatório',
        historical: 'Histórico'
      },
      tabReports: {
        panel: {
          title: 'Gerar relatório',
          subTitle: 'Qual tipo de relatório vamos gerar?',
          buttonGenerateReport: 'Gerar relatório em branco'
        },
        latest: {
          title: 'Mais recente',
          seeMore: 'Ver mais'
        },
        errorRequest: 'Erro ao gerar o relatóio'
      },
      tabHistorical: {
        title: 'Meus relatórios',
        table: {
          search: {
            fieldQuery: 'Pesquisar'
          },
          columns: {
            name: 'Nome',
            update: 'Atualizar',
            entity: 'Entidade',
            typeMeasurements: 'Tipo de medição',
            actions: 'ações'
          },
          modalDelete: {
            title: 'Excluir relatório',
            textInfo: 'Tem certeza de que deseja excluir o relatório?',
            messages: {
              successMessage: 'Relatório excluído com sucesso',
              errorMessage: 'Erro ao remover relatório'
            }
          }
        }
      },
      tabGenerate: {
        form: {
          title: 'Filtro',
          entity_fields: 'Grupo',
          entity_type_fields: 'Tipo de registro',
          entity_data_fields: 'Dados da entidade',
          period_fields: 'Período',
          aggregate_fields: 'Agregações',
          consumption: 'Consumo',
          demand: 'Demanda',
          power_factor: 'Fator de potência',
          consumption_account: 'Log de acesso',
          input: {
            'entity_fields.entity': 'Entidade',
            'entity_type_fields.typeMeasurement': 'Tipo de medições',
            'entity_type_fields.account_type': 'Tipo de registro',
            'entity_data_fields.entity_data': 'Unidade',
            'entity_data_fields.entity_data_group': 'Agrupar dados por:',
            'entity_data_fields.entity_data_account': 'Contas',
            'period_fields.initial_date': 'Data de início',
            'period_fields.final_date': 'Data de término',
            'period_fields.syntax_date': 'Período relativo',
            'aggregate_fields.date_interval': 'Data intervalo',
            'aggregate_fields.date_interval_number':
              'Quantidade do intervalo de data',
            'aggregate_fields.type': 'Agregar pelo(a)',
            'type_data.consumption.tariff_post': 'Posto Tarifário',
            'type_data.consumption.capacity': 'Capacidade',
            'type_data.consumption.consumption': 'Consumo',
            'type_data.consumption.greatness': 'Grandeza',
            'type_data.demand.tariff_post': 'Posto Tarifário',
            'type_data.demand.capacity': 'Capacidade',
            'type_data.demand.consumption': 'Demanda',
            'type_data.power_factor.tariff_post': 'Posto Tarifário',
            'type_data.power_factor.capacity': 'Capacidade'
          },
          generateReport: 'Gerar Relatório',
          requiredFinalDate:
            'O campo data final é obrigatório quando data inicial está presente.',
          requiredTypeData: 'O campo tipo dos dados é obrigatório',
          downloadCompact: 'Excel - compacto',
          downloadDetailed: 'Excel - detalhado'
        },
        table: {
          titleInfo: 'Informações',
          titleTable: 'Relatório',
          infoGenerateReport: 'Use os filtros ao lado para gerar o relatório',
          infoOrderDate: 'Data do pedido',
          infoRequestedPeriod: 'Período solicitado'
        },
        modalSave: {
          form: {
            input: {
              name: 'Nome'
            },
            messages: {
              createSuccessMessage: 'Relatório criado com sucesso',
              createErrorMessage: 'Erro ao criar relatório',
              updateSuccessMessage: 'Relatório atualizado com sucesso',
              updateErrorMessage: 'Erro ao atualizar relatório'
            }
          }
        }
      }
    },
    statistics: {
      title: "Estatísticas e KPI's",
      btnDownload: 'Download',
      indicatorNames: {
        'disconnections-by-period': 'Desconexão por periodo',
        'devices-disconnected-by-company':
          'Desconexão de dispositivos por empresa',
        'reconnections-by-period': 'Reconexão por periodo',
        'disconnections-by-operator': 'Desconexão por operador',
        'new-devices': 'Novos',
        'excluded-devices': 'Excluidos',
        'new-devices-by-type': 'Novos por tipo',
        'new-devices-by-subtype': 'Novos por subtipo',
        'new-devices-energy-frontier': 'Novos energia fronteira',
        'new-devices-energy-sectorized': 'Novos energia setorizada',
        'disconnections-by-distributor': 'Desconexão por distribuidor',
        'disconnections-by-location': 'Desconexão por localização',
        'alarms-by-category': 'Alarmes por categoria',
        'type-energy': 'Energia',
        'type-water': 'Água',
        'subtype-energy-frontier': 'Energia fronteira',
        'subtype-energy-sectorized': 'Energia setorizada',
        'subtype-energy-sensorized': 'Energia sensorizada',
        'subtype-water-frontier': 'Água fronteira',
        'subtype-water-sectorized': 'Água setorizada'
      },
      tabs: {
        productivity: 'Produtividade',
        monitoring: 'Monitoramento'
      },
      tabProductivity: {},
      tabMonitoring: {}
    },
    statisticsProductivityIndicator: {
      title: 'Instalações',
      table: {
        columns: {
          name: 'Nome'
        },
        tableWithoutData: 'Não foram encontrados dados'
      }
    },
    users: {
      title: 'Usuários',
      btnNew: 'Novo usuário',
      table: {
        search: {
          fieldAccounts: 'Conta',
          fieldQuery: 'Pesquisar'
        },
        columns: {
          name: 'Nome',
          email: 'E-mail',
          account: 'Conta',
          lastAcess: 'Último acesso',
          actions: 'Ações'
        },
        tableWithoutData: 'Não foram encontrados dados',
        modalDelete: {
          title: 'Excluir usuário',
          textInfo: 'Tem certeza de que deseja excluir o usuário:',
          textConfirm: 'Excluir',
          textCancel: 'Cancelar',
          deleteSuccessMessage: 'Usuário removido com sucesso',
          deleteErrorMessage: 'Erro ao remover a usuário'
        }
      }
    },
    userId: {
      title: 'Usuário',
      titleNew: 'Cadastro de usuário',
      titleEdit: 'Edição de usuário',
      tabs: {
        data: 'Cadastro',
        subtitleData: 'Altere e/ou atualize os dados do cadastro',
        permissions: 'Permissões',
        entities: 'Entidades'
      },
      tabData: {
        title: 'Cadastro',
        form: {
          adminField: 'Administrador',
          activeField: 'Ativo',
          inputFirstName: 'Nome',
          inputLastName: 'Sobrenome',
          inputEmail: 'E-mail',
          inputPincode: 'PIN',
          inputCellPhone: 'Celular',
          inputPassword: 'Senha',
          inputPasswordConfirmation: 'Confirmar Senha',
          inputAccount: 'Conta',
          fieldValidation: {
            inputCellPhone: 'Por favor, insira um número de telefone válido',
            inputPasswordConfirmation: 'As senhas não conferem!'
          },
          passwordValidation: {
            passwordValidationTitle:
              'A senha deve ter entre 8 e 24 caracteres, contendo pelo menos um número, uma letra maiúscula, uma letra minúscula e um caractere especial (@$!%*?&#).',
            minimumCharacters: 'A senha deve ter pelo menos 8 caracteres',
            maximumCharacters: 'A senha deve ter no máximo 24 caracteres',
            number: 'A senha deve conter pelo menos um número',
            capitalLetter: 'A senha deve conter pelo menos uma letra maiúscula',
            lowercaseLetter:
              'A senha deve conter pelo menos uma letra minúscula',
            specialCharacter:
              'A senha deve conter pelo menos um caractere especial (@$!%*?&#)',
            differentPasswords: 'As senhas não correspondem'
          }
        },
        formMessages: {
          createSuccessMessage: 'Usuário criado com sucesso',
          createErrorMessage: 'Erro ao criar usuário',
          updateSuccessMessage: 'Usuário atualizado com sucesso',
          updateErrorMessage: 'Erro ao atualizar usuário'
        }
      },
      tabPermissions: {
        title: 'Permissões',
        columns: {
          permission: 'permissão',
          list: 'listar',
          create: 'criar',
          delete: 'deletar'
        },
        formMessages: {
          createPermissionSuccessMessage: 'Permissão atualizada com sucesso',
          createPermissionErrorMessage: 'Erro ao atualizar permissão'
        }
      },
      tabEntities: {
        title: 'Entidade',
        informationMessage:
          'Nenhuma permissão de unidade é necessária para o seu nível de usuário.',
        formSearch: {
          fieldAccount: 'Escolha uma conta',
          fieldQuery: 'Pesquisar'
        },
        formMessages: {
          createEntitySuccessMessage: 'Entidade atualizada com sucesso',
          createEntityErrorMessage: 'Erro ao atualizar entidade'
        },
        entitiesNotFound:
          'Sua conta ainda não possui unidades habilitados, por favor entre em contato com nosso suporte.',
        entitiesEmpty: 'Não foram encontrado unidades com termo da busca.'
      }
    }
  },
  layout: {
    header: {
      menu: {
        menuItemHello: 'Olá',
        menuItemEditProfile: 'Editar perfil',
        menuItemLogout: 'Sair'
      }
    },
    modal: {
      progressDeleteAccount: {
        title: 'Deletar conta',
        step1: {
          text1: 'Deseja excluir',
          text2: 'Após deletar a conta, você nao poderá recuperar os dados.',
          btn: 'Sim, excluir conta'
        },
        step2: {
          text1:
            'Por segurança pedimos que confirme o PIN para completar a ação de excluir conta.',
          btn: 'Continuar'
        }
      }
    }
  },
  pwa: {
    install: 'Instalar APP',
    installed: 'APP já instalado',
    installUnavailable: 'instalação de APP indisponível'
  },
  chart: {
    noData: 'Sem informações no momento'
  },
  notifications: {
    title: 'Notificação',
    defaulterClient:
      'Você possui pendências de pagamento. Por favor entrar em contato com o Financeiro ou Gestor.'
  }
}

export default tree
