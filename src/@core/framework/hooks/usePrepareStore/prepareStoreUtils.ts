import { NextRouter } from 'next/router'

export function incrementErrorCount(countKey: string): number {
  const count = Number(localStorage.getItem(countKey))
  const newCount = count + 1
  localStorage.setItem(countKey, String(newCount))
  return newCount
}

export function handleAccessDenied(
  setIsPending: (pending: boolean) => void,
  router: NextRouter,
  errorMessage: string
) {
  setIsPending(false)
  router.push(`/access-denied?error=${errorMessage}`)
}
