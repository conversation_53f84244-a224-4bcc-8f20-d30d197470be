import { AxiosError } from 'axios'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'

import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { authApiV3, menuApiV3, userActionsApiV3 } from '@/@core/infra/api'
import { httpServiceCognito } from '@/@core/infra/api-cognito'
import { authorizationCodeExchangeApi } from '@/@core/infra/api/CognitoAuthorizationCodeApi/AuthorizationCodeExchangeApi'
import { http } from '@/@core/infra/http'
import { memory, memoryApp } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { groupsUserActionsToPermissions } from '@/@core/utils/groupsUserActionsToPermissions'
import { redirectLoginCognito } from '@/@core/utils/redirectLoginCognito'

import { handleAccessDenied, incrementErrorCount } from './prepareStoreUtils'

export const usePrepareStore = () => {
  const router = useRouter()
  const log = useLog()

  const [isPending, setIsPending] = useState<boolean>(true)

  const authStore = useAuthStore()
  const systemStore = useSystemStore()
  const languageStore = useSystemLanguageStore()

  const hasHandlerRun = useRef(false)

  const countKey = 'prepareStoreHandlerCount'

  /** STEPS */
  const stepMemory = async () => {
    await new Promise<void>((resolve) => {
      memoryApp.init()
      languageStore.setLanguage(memory.local.get().system.language)

      resolve()
    })
  }
  const stepToken = async () => {
    await new Promise<void>((resolve) => {
      const auth = memory.cookie.get().auth

      if (auth?.token) {
        http.setToken(auth.token)
      }

      resolve()
    })
  }
  const stepExchangeCode = async () => {
    const hasToken = !!memory.cookie.get().auth.token

    if (hasToken) return

    const params = new URL(`${window.location.origin}${router.asPath}`)
      .searchParams

    const code = params.get('code')

    if (!code) {
      throw new Error('Não foi encontrado o parâmetro code')
    }

    const { data } = await authorizationCodeExchangeApi(
      httpServiceCognito
    ).login({
      code: String(code),
      clientId: process.env.NEXT_PUBLIC_NEXT_CLIENT_ID_COGNITO!,
      redirectUri: process.env.NEXT_PUBLIC_COGNITO_CALLBACK_URL!
    })

    memory.cookie.set({
      auth: {
        token: data.accessToken,
        code: data.code
      }
    })
    http.setToken(data.accessToken)
  }
  const stepUserData = async () => {
    try {
      const resultMe = await authApiV3(http).me()

      const resultMenu = await menuApiV3(http).get()
      const resultUserActions = await userActionsApiV3(http).get({
        accountUserId: resultMe.data.accountUserId!
      })

      authStore.setMe(resultMe.data)
      systemStore.setMenuItems(resultMenu.data)
      systemStore.setPermissions(
        groupsUserActionsToPermissions(resultUserActions.data.items)
      )

      // changeColorsThemeByElement(memory.local.get().system.theme, 'html')
    } catch (error) {
      const { response } = error as AxiosError
      if (response?.status === 428) {
        throw new Error('userNotPartOfPlatform')
      }
      if (response?.status === 401) {
        throw new Error('isAuthError')
      }
    }
  }

  const stepLogout = async () => {
    const auth = memory.cookie.get().auth

    if (!auth?.token) return

    http.setToken(auth.token)

    try {
      await authApiV3(http).logout()
      memoryApp.down()
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'stepLogout - @core/framework/hooks/usePrepareStore/hook.ts'
      })
    }
  }

  async function handler() {
    try {
      await stepMemory()
      await stepToken()
      await stepExchangeCode()
      await stepUserData()

      if (router.pathname === '/login') router.push('/dashboard')

      setIsPending(false)
    } catch (error: unknown) {
      const count = incrementErrorCount(countKey)

      log.send(loggerRequest, {
        error,
        title: 'usePrepareStore - @core/framework/hooks/usePrepareStore/hook.ts'
      })

      const errorMessage = error instanceof Error ? error.message : ''

      if (errorMessage === 'userNotPartOfPlatform') {
        handleAccessDenied(setIsPending, router, errorMessage)
        return
      }

      if (count > 3) {
        localStorage.setItem(countKey, '0')
        handleAccessDenied(setIsPending, router, 'isAuthError')
        return
      }

      await stepLogout()
      window.location.href = redirectLoginCognito()
    }
  }

  useEffect(() => {
    if (!authStore.state.me?.user?.id && hasHandlerRun.current === false) {
      hasHandlerRun.current = true
      handler()
    }
  }, [authStore.state.me?.user?.id])

  return { isPending }
}
