import type { NextRouter } from 'next/router'
import { handleAccessDenied, incrementErrorCount } from './prepareStoreUtils'

describe('incrementErrorCount', () => {
  const key = 'test-error-count'

  beforeEach(() => {
    localStorage.clear()
  })

  it('should increment count from 0', () => {
    expect(incrementErrorCount(key)).toBe(1)
    expect(localStorage.getItem(key)).toBe('1')
  })

  it('should increment count from existing value', () => {
    localStorage.setItem(key, '2')
    expect(incrementErrorCount(key)).toBe(3)
    expect(localStorage.getItem(key)).toBe('3')
  })
})

describe('handleAccessDenied', () => {
  it('should call setIsPending and router.push with correct params', () => {
    const setIsPending = jest.fn()
    const push = jest.fn()
    const router = { push } as unknown as NextRouter
    const errorMessage = 'unauthorized'

    handleAccessDenied(setIsPending, router, errorMessage)

    expect(setIsPending).toHaveBeenCalledWith(false)
    expect(push).toHaveBeenCalledWith(`/access-denied?error=${errorMessage}`)
  })
})
