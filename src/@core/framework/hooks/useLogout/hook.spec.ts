import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { renderHook } from '@testing-library/react'
import { act } from 'react'
import { useLogout } from './hook'

jest.mock('@/@core/infra/api/AuthApiV3/AuthApiV3')
const spyAuthApiV3 = jest.spyOn(
  require('@/@core/infra/api/AuthApiV3/AuthApiV3'),
  'authApiV3'
)

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

describe('src/@core/framework/hooks/useLogout/hook', () => {
  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      push: jest.fn()
    }))
  })

  it('should call logout API, reset cookies, and redirect to login', async () => {
    spyAuthApiV3.mockImplementation(() => ({
      logout: jest.fn().mockRejectedValue({
        status: 200,
        data: { message: 'Logged out' }
      })
    }))

    const { result } = renderHook(() => ({ logout: useLogout() }), {
      wrapper: AppStoreProvider
    })

    await act(async () => {
      await result.current.logout()
    })
  })

  it('should handle error during logout and log it', async () => {
    spyAuthApiV3.mockImplementation(() => ({
      logout: jest.fn().mockResolvedValue({
        status: 200,
        data: { message: 'Logged out' }
      })
    }))

    const { result } = renderHook(() => ({ logout: useLogout() }), {
      wrapper: AppStoreProvider
    })

    await act(async () => {
      await result.current.logout()
    })
  })
})
