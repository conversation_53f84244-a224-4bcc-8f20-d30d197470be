import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/system'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useSystemStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.system)

  function setMenuItems(values: ReducerPayloadProps['setMenuItems']) {
    dispatch(actions.setMenuItems(values))
  }
  function setPermissions(values: ReducerPayloadProps['setPermissions']) {
    dispatch(actions.setPermissions(values))
  }
  function setMountComponent(key: string) {
    setTimeout(
      () => dispatch(actions.setMountComponent({ key, value: true })),
      50
    )
  }
  function setUnmountComponent(keys: string | string[]) {
    Array.from(Array.isArray(keys) ? keys : [keys]).forEach((key) => {
      dispatch(actions.setMountComponent({ key, value: false }))
    })
  }

  return {
    state,
    setMenuItems,
    setPermissions,
    setMountComponent,
    setUnmountComponent
  }
}
