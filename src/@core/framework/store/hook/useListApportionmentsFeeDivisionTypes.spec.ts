import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  apportionmentsFeeDivisionTypesMock1,
  apportionmentsFeeDivisionTypesMock2
} from '@/__mock__/content/api-apportionments-fee-division-types.content'
import useListApportionmentsFeeDivisionTypes from './useListApportionmentsFeeDivisionTypes'

describe('src/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes', () => {
  it('should call method set', () => {
    const { result } = renderHook(
      () => useListApportionmentsFeeDivisionTypes(),
      {
        wrapper: AppStoreProvider
      }
    )

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [
          apportionmentsFeeDivisionTypesMock1,
          apportionmentsFeeDivisionTypesMock2
        ]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
