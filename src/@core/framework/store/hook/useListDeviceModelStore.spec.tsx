import { act, renderHook } from '@testing-library/react'

import {
  deviceModelMock1,
  deviceModelMock2
} from '@/__mock__/content/api-device-model.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListDeviceModelStore from './useListDeviceModelStore'

describe('src/@core/framework/store/hook/useListDeviceModelStore', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListDeviceModelStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [deviceModelMock1, deviceModelMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
