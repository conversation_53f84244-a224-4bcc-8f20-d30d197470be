import { act, renderHook } from '@testing-library/react'

import {
  listCItyMock1,
  listCItyMock2
} from '@/__mock__/content/api-list-cities.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListCityStore from './useListCityStore'

describe('src/@core/framework/store/hook/useListCityStore', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListCityStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [listCItyMock1, listCItyMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
