import { act, renderHook } from '@testing-library/react'

import {
  alarmsOperatorLogicMock1,
  alarmsOperatorLogicMock2
} from '@/__mock__/content/api-alarms-operator-logic.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListAlarmsOperatorLogic from './useListAlarmsOperatorLogic'

describe('src/@core/framework/store/hook/useListAlarmsOperatorLogic', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListAlarmsOperatorLogic(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [alarmsOperatorLogicMock1, alarmsOperatorLogicMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
