import { renderHook } from '@testing-library/react'
import { act } from 'react'

import {
  operationalRuleMock1,
  operationalRuleMock2
} from '@/__mock__/content/api-operational-rules.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListOperationalRuleStore from './useListOperationalRuleStore'

describe('src/@core/framework/store/hook/useListOperationalRuleStore', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListOperationalRuleStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [operationalRuleMock1, operationalRuleMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
