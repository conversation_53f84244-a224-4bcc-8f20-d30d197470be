import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listAlarmsTargets'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListAlarmsTargets() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listAlarmsTargets)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
