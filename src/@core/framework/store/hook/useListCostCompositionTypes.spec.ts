import { act, renderHook } from '@testing-library/react'

import {
  costCompositionTypesMock1,
  costCompositionTypesMock2
} from '@/__mock__/content/api-cost-composition-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListCostCompositionTypes from './useListCostCompositionTypes'

describe('src/@core/framework/store/hook/useListCostCompositionTypes', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListCostCompositionTypes(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [costCompositionTypesMock1, costCompositionTypesMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
