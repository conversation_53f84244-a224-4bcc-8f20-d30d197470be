import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listAlarmsOperatorLogic'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListAlarmsOperatorLogic() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listAlarmsOperatorLogic)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
