import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  alarmsTargetsMock1,
  alarmsTargetsMock2
} from '@/__mock__/content/api-alarms-targets.content'
import useListAlarmsTargets from './useListAlarmsTargets'

describe('src/@core/framework/store/hook/useListAlarmsTargets', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListAlarmsTargets(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [alarmsTargetsMock1, alarmsTargetsMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
