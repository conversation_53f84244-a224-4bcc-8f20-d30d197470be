import { act, renderHook } from '@testing-library/react'

import {
  companiesTypesMock1,
  companiesTypesMock2
} from '@/__mock__/content/api-companies-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListCompaniesTypesStore from './useListCompaniesTypesStore'

describe('src/@core/framework/store/hook/useListCompaniesTypesStore', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListCompaniesTypesStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [companiesTypesMock1, companiesTypesMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
