import { act, renderHook } from '@testing-library/react'

import {
  alarmsRulesMock1,
  alarmsRulesMock2
} from '@/__mock__/content/api-alarms-rules.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListAlarmsRules from './useListAlarmsRules'

describe('src/@core/framework/store/hook/useListAlarmsRules', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListAlarmsRules(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [alarmsRulesMock1, alarmsRulesMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
