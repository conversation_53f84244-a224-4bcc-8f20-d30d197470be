import { act, renderHook } from '@testing-library/react'

import {
  companyContactMock1,
  companyContactMock2
} from '@/__mock__/content/api-company-contacts.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListCompanyCategoryStore from './useListCompanyCategoryStore'

describe('src/@core/framework/store/hook/useListCompanyCategoryStore', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListCompanyCategoryStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [companyContactMock1, companyContactMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
