import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listAlarmsRules'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListAlarmsRules() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listAlarmsRules)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
