import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listAlarmsNotificationsChannels'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListAlarmsNotificationsChannelsStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listAlarmsNotificationsChannel)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
