import { act, renderHook } from '@testing-library/react'

import {
  apportionmentTypeMock1,
  apportionmentTypeMock2
} from '@/__mock__/content/api-apportionment-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListApportionmentTypes from './useListApportionmentTypes'

describe('src/@core/framework/store/hook/useListApportionmentTypes', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListApportionmentTypes(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [apportionmentTypeMock1, apportionmentTypeMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
