import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  apportionmentMeasuresUnitMock1,
  apportionmentMeasuresUnitMock2
} from '@/__mock__/content/api-apportionment-measures-units.content'
import useListApportionmentMeasuresUnits from './useListApportionmentMeasuresUnits'

describe('src/@core/framework/store/hook/useListApportionmentMeasuresUnits', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListApportionmentMeasuresUnits(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [apportionmentMeasuresUnitMock1, apportionmentMeasuresUnitMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
