import { renderHook, waitFor } from '@testing-library/react'

import { menuItem1 } from '@/__mock__/content/menu.layout.contents'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useSystemStore from './useSystemStore'

describe('src/@core/framework/store/hook/useSystemStore', () => {
  beforeAll(() => {
    jest.useFakeTimers()
  })

  afterAll(() => {
    jest.runAllTimers()
  })

  it('should call method setMenuItems', async () => {
    const { result } = renderHook(() => useSystemStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.menuItems).toHaveLength(0)

    await waitFor(() => {
      result.current.setMenuItems([menuItem1])
    })

    expect(result.current.state.menuItems).toHaveLength(1)
  })

  it('should call method setPermissions', async () => {
    const { result } = renderHook(() => useSystemStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.permissions?.accounts).toBeUndefined()

    await waitFor(() => {
      result.current.setPermissions({
        accounts: { create: false, delete: false, list: true }
      })
    })

    expect(result.current.state.permissions?.accounts?.list).toBeTruthy()
  })

  it('should call method setMountComponent and setUnmountComponent', async () => {
    const { result } = renderHook(() => useSystemStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.mountComponent?.dashboard).toBeUndefined()

    await waitFor(async () => {
      result.current.setMountComponent('dashboard')
    })

    expect(result.current.state.mountComponent?.dashboard).not.toBeUndefined()
  })
})
