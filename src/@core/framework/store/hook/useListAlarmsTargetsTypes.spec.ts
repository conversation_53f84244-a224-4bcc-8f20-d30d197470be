import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  alarmsTargetsTypesMock1,
  alarmsTargetsTypesMock2
} from '@/__mock__/content/api-alarms-targets-types.content'
import useListAlarmsTargetsTypes from './useListAlarmsTargetsTypes'

describe('src/@core/framework/store/hook/useListAlarmsTargetsTypes', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListAlarmsTargetsTypes(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [alarmsTargetsTypesMock1, alarmsTargetsTypesMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
