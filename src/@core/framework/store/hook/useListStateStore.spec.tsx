import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  listStateMock1,
  listStateMock2
} from '@/__mock__/content/api-list-states.content'
import useListStateStore from './useListStateStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListStateStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [listStateMock1, listStateMock2]
    })
  })

  expect(result.current.state.list).toHaveLength(2)
})
