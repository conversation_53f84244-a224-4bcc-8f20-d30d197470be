import { act, renderHook } from '@testing-library/react'

import {
  apportionmentTariffTypesMock1,
  apportionmentTariffTypesMock2
} from '@/__mock__/content/api-apportionment-tariff-types'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListApportionmentTariffTypes from './useListApportionmentTariffTypes'

describe('src/@core/framework/store/hook/useListApportionmentTariffTypes', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListApportionmentTariffTypes(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [apportionmentTariffTypesMock1, apportionmentTariffTypesMock2]
      })
    })

    expect(result.current.state.list).toHaveLength(2)
  })
})
