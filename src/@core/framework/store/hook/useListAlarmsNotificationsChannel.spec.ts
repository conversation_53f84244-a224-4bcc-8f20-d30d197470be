import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListAlarmsNotificationsChannel from './useListAlarmsNotificationsChannel'

it('should call method set', () => {
  const { result } = renderHook(() => useListAlarmsNotificationsChannel(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: 'E-mail'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
