import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListDeviceInstallationFieldStore from './useListDeviceInstallationFieldStore'

describe('src/@core/framework/store/hook/useListDeviceInstallationFieldStore', () => {
  it('should call method set', () => {
    const { result } = renderHook(() => useListDeviceInstallationFieldStore(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.state.list).toHaveLength(0)

    act(() => {
      result.current.set({
        list: [
          {
            key: 'key 1',
            name: 'name 1'
          }
        ]
      })
    })

    expect(result.current.state.list).toHaveLength(1)
  })
})
