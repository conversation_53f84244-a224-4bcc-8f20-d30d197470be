import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listAlarmsTargetsTypes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListAlarmsTargetsTypes() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listAlarmsTargetsTypes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
