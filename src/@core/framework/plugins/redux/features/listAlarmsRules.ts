import { createSlice } from '@reduxjs/toolkit'

import { IAlarmsRules } from '@/@core/domain/AlarmsRules'

export type ISliceState = {
  list: IAlarmsRules[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listAlarmsRulesSlice = createSlice({
  name: 'listAlarmsRules',
  initialState,
  reducers
})

export const actions = listAlarmsRulesSlice.actions
