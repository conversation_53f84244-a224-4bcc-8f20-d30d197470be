import { createSlice } from '@reduxjs/toolkit'

import { IAlarmsOperatorLogic } from '@/@core/domain/AlarmsOperatorLogic'

export type ISliceState = {
  list: IAlarmsOperatorLogic[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listAlarmsOperatorLogicSlice = createSlice({
  name: 'listAlarmsOperatorLogic',
  initialState,
  reducers
})

export const actions = listAlarmsOperatorLogicSlice.actions
