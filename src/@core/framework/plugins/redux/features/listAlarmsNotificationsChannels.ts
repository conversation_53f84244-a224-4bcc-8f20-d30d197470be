import { createSlice } from '@reduxjs/toolkit'

import { IAlarmsNotificationsChannels } from '@/@core/domain/AlarmsNotificationsChannels'

export type ISliceState = {
  list: IAlarmsNotificationsChannels[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listAlarmsNotificationsChannelSlice = createSlice({
  name: 'listAlarmsNotificationsChannel',
  initialState,
  reducers
})

export const actions = listAlarmsNotificationsChannelSlice.actions
