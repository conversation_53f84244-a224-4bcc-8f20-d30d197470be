import { IMe } from '@/@core/domain'
import { createSlice } from '@reduxjs/toolkit'

export type ISliceState = {
  me: IMe
}
export type ReducerPayloadProps = {
  setMe: ISliceState['me']
}

const initialState: ISliceState = {
  me: {
    user: {
      id: null,
      name: '',
      avatar: '',
      cellphone: '',
      email: '',
      active: false,
      admin: false
    },
    account: null,
    accountId: null,
    accountName: '',
    accountUserId: null,
    accountUserAdmin: false,
    notification: false,
    cognitoUserId: null,
    groups: []
  }
}

const reducers = {
  setMe(state: ISliceState, action: { payload: ReducerPayloadProps['setMe'] }) {
    state.me = action.payload
  }
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers
})

export const actions = authSlice.actions
