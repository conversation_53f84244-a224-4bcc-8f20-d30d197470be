import { createSlice } from '@reduxjs/toolkit'

import { IAlarmsTargetsTypes } from '@/@core/domain/AlarmsTargetsTypes'

export type ISliceState = {
  list: IAlarmsTargetsTypes[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listAlarmsTargetsTypesSlice = createSlice({
  name: 'listAlarmsTargetsTypes',
  initialState,
  reducers
})

export const actions = listAlarmsTargetsTypesSlice.actions
