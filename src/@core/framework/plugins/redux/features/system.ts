import { IUserActionGrouped } from '@/@core/domain/UserAction'
import { IMenuItem, StatusMenuDesktop } from '@/types/system/layout'
import { createSlice } from '@reduxjs/toolkit'

export type ISliceState = {
  menuItems: IMenuItem[]
  statusMenuDesktop: StatusMenuDesktop
  permissions: Partial<IUserActionGrouped>
  mountComponent: Record<string, boolean>
}
export type ReducerPayloadProps = {
  setMenuItems: ISliceState['menuItems']
  setPermissions: ISliceState['permissions']
  setMountComponent: { key: string; value: boolean }
}

const initialState: ISliceState = {
  menuItems: [],
  statusMenuDesktop: 'close',
  permissions: {},
  mountComponent: {}
}

const reducers = {
  setMenuItems(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setMenuItems'] }
  ) {
    state.menuItems = action.payload
  },
  setPermissions(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setPermissions'] }
  ) {
    state.permissions = action.payload
  },
  setMountComponent(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setMountComponent'] }
  ) {
    state.mountComponent[action.payload.key] = action.payload.value
  }
}

export const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers
})

export const actions = systemSlice.actions
