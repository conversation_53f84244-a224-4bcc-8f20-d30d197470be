import { createSlice } from '@reduxjs/toolkit'

import { IAlarmsTargets } from '@/@core/domain/AlarmsTargets'

export type ISliceState = {
  list: IAlarmsTargets[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listAlarmsTargetsSlice = createSlice({
  name: 'listAlarmsTargets',
  initialState,
  reducers
})

export const actions = listAlarmsTargetsSlice.actions
