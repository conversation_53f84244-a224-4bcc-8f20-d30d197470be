import { useRouter } from 'next/router'
import React, { useState } from 'react'

import { usePrepareStore } from '@/@core/framework/hooks/usePrepareStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { LayoutType } from '@/types/system/layout'

import { LayoutPrivateHeader } from '@/@core/presentation/layouts/Private/Header'
import { LayoutPrivateMain } from '@/@core/presentation/layouts/Private/Main'
import { LayoutPrivateSidebarDesktop } from '@/@core/presentation/layouts/Private/Menu/SidebarDesktop'
import {
  LayoutPrivateModal,
  LayoutPrivateModalDefaulterClients
} from '@/@core/presentation/layouts/Private/Modal'
import {
  LayoutPrivateNotificationDefaulterClientsDesktop,
  LayoutPrivateNotificationDefaulterClientsMobile
} from '@/@core/presentation/layouts/Private/Notification'
import { LayoutPrivateToasts } from '@/@core/presentation/layouts/Private/Toasts'
import { LayoutPrivateFooter } from './Private/Footer'

import { LayoutPublicMain } from '@/@core/presentation/layouts/Public/Main'

import { redirectLoginCognito } from '@/@core/utils/redirectLoginCognito'
import AccessDenied from '@/pages/access-denied'
import { LayoutLoading } from './LayoutLoading'

type LayoutProps = {
  children: React.ReactNode
  layout?: LayoutType
}

function onLogoutEvent(event: StorageEvent) {
  if (event.key === 'logout-event') {
    window.location.href = redirectLoginCognito()
  }
}

export const Layout = ({ children, layout }: LayoutProps) => {
  const router = useRouter()
  const { isPending } = usePrepareStore()
  const systemLoading = useSystemLoadingStore()

  const [menuState, setMenuState] = useState<'open' | 'close'>('close')

  const handleStart = () => systemLoading.setData({ pageLoading: true })
  const handleComplete = () => systemLoading.setData({ pageLoading: false })

  React.useEffect(() => {
    router.events.on('routeChangeStart', handleStart)
    router.events.on('routeChangeComplete', handleComplete)
    window.addEventListener('storage', onLogoutEvent)

    return () => {
      router.events.off('routeChangeStart', handleStart)
      router.events.off('routeChangeComplete', handleComplete)
      window.removeEventListener('storage', onLogoutEvent)
    }
  }, [])

  if (isPending) {
    return <LayoutLoading />
  }

  if (layout === 'accessDenied') {
    return (
      <>
        <LayoutPrivateHeader />
        <AccessDenied />
      </>
    )
  }

  if (!layout) {
    return (
      <>
        {systemLoading.state.pageLoading && (
          <LayoutLoading className="animation-fadeIn absolute top-0 bg-white/30" />
        )}
        <LayoutPrivateHeader />

        <LayoutPrivateSidebarDesktop
          menuState={menuState}
          setMenuState={setMenuState}
        />

        <LayoutPrivateModal />
        {process.env.NODE_ENV === 'production' && (
          <>
            <LayoutPrivateModalDefaulterClients />
            <LayoutPrivateNotificationDefaulterClientsDesktop />
          </>
        )}

        <LayoutPrivateMain menuState={menuState}>
          <LayoutPrivateToasts />
          {process.env.NODE_ENV === 'production' && (
            <LayoutPrivateNotificationDefaulterClientsMobile />
          )}
          {children}
          <LayoutPrivateFooter />
        </LayoutPrivateMain>
      </>
    )
  }

  return (
    <>
      {systemLoading.state.pageLoading && (
        <LayoutLoading className="animation-fadeIn absolute top-0 bg-white/30" />
      )}
      <LayoutPublicMain>{children}</LayoutPublicMain>
    </>
  )
}
