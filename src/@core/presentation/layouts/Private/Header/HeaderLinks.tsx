import Link from 'next/link'
import { useRouter } from 'next/router'
import React, { FC, forwardRef } from 'react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger
} from '@/@core/presentation/shared/ui/dropdown-menu'
import { Icon } from '@/@core/presentation/shared/ui/icons'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { defineHeaderDropdown, toggleHeaderDropdown } from '../utils'
import { HeaderDropdownMyServices } from './HeaderDropdownMyServices'

export const HeaderLinks = () => {
  const router = useRouter()

  return (
    <div className="hidden font-acuminPro-Semibold text-comerc-grayLight-600 lg:flex gap-[12px] pl-[18px] ml-[2.5rem] wei">
      <ButtonHome
        className={cn(router.pathname === '/access-denied' && 'hidden')}
      />
      <ButtonMyServices />
      <ButtonSolutionForYou />
    </div>
  )
}

interface IButtonBaseProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {}
const ButtonBase = forwardRef<HTMLButtonElement, IButtonBaseProps>(
  ({ onClick, children, className, ...rest }, ref) => {
    return (
      <button
        ref={ref}
        onClick={onClick}
        className={cn(
          'flex items-center',
          'h-[44px] p-[18px] gap-[6px]',
          'cursor-pointer hover-menu-primary rounded-[8px]',
          className
        )}
        header-dropdown-btn=""
        {...rest}
      >
        <span className="text-comerc-grayLight-600 text-[16px] leading-[24px]">
          {children}
        </span>
        <div className="duration-150 ease-linear" header-dropdown-btn-icon="">
          <Icon
            icon="chevronDown"
            width="24"
            height="24"
            className="icon-menu-primary"
            strokeWidth="1.66667"
          />
        </div>
      </button>
    )
  }
)

interface ButtonHomeProps {
  className?: string
}

const ButtonHome: FC<ButtonHomeProps> = ({ className }) => {
  return (
    <Link
      href="/dashboard"
      className={cn(
        'flex items-center',
        'h-[44px] p-[18px] gap-[6px]',
        'cursor-pointer hover-menu-primary rounded-[8px]',
        'text-comerc-grayLight-600 text-[16px] leading-[24px]',
        className
      )}
    >
      Página Inicial
    </Link>
  )
}
const ButtonMyServices: FC = () => {
  const handleOpen = () => {
    defineHeaderDropdown([
      {
        selector: '[header-dropdown-content]',
        attribute: 'header-dropdown-content',
        value: 'false'
      },
      {
        selector: '[header-dropdown-btn]',
        attribute: 'header-dropdown-btn',
        value: 'false'
      },
      {
        selector: '.header-dropdown-btn-myServices',
        attribute: 'header-dropdown-btn',
        value: 'true'
      }
    ])
  }
  const handleClose = () => {
    defineHeaderDropdown([
      {
        selector: '[header-dropdown-content]',
        attribute: 'header-dropdown-content',
        value: 'false'
      },
      {
        selector: '[header-dropdown-btn]',
        attribute: 'header-dropdown-btn',
        value: 'false'
      }
    ])
  }
  const handleOpenChange = (value: boolean) =>
    value ? handleOpen() : handleClose()

  return (
    <DropdownMenu onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <ButtonBase className="header-dropdown-btn-myServices">
          Meus Serviços
        </ButtonBase>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="min-w-[324px] ml-40">
        <HeaderDropdownMyServices />
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
const ButtonSolutionForYou: FC = () => {
  const handleClickSolutionForYou = () => {
    toggleHeaderDropdown(
      [
        {
          selector: '.header-dropdown-content-solutionForYou',
          attribute: 'header-dropdown-content'
        },
        {
          selector: '.header-dropdown-btn-solutionForYou',
          attribute: 'header-dropdown-btn'
        }
      ],
      [
        {
          selector: '[header-dropdown-content]',
          attribute: 'header-dropdown-content',
          value: 'false'
        },
        {
          selector: '[header-dropdown-btn]',
          attribute: 'header-dropdown-btn',
          value: 'false'
        }
      ]
    )
  }
  return (
    <ButtonBase
      onClick={handleClickSolutionForYou}
      className="header-dropdown-btn-solutionForYou"
    >
      Soluções para você
    </ButtonBase>
  )
}
