import {
  mapSolutionsFor<PERSON>ou,
  SolutionsForYouItem,
  SolutionsForYouList
} from '@/@core/content/mapSolutionsForYou.content'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import menuItemCard from '@/assets/images/menu-item-card.svg'
import Image from 'next/image'
import { FC, ReactNode } from 'react'

export const HeaderDropdownSolutionForYou: FC = () => {
  return (
    <div
      className={cn(
        'header-dropdown-content-solutionForYou',
        'hidden lg:flex w-full h-full absolute z-[60]',
        'rounded-b-[12px]',
        'bg-primary shadow-[0px_12px_16px_-4px_rgba(16,24,40,0.08),0px_4px_6px_-2px_rgba(16,24,40,0.03)]'
      )}
      header-dropdown-content=""
    >
      <SolutionsItems />
      <SolutionsItemCard />
    </div>
  )
}

const SolutionsItems: FC = () => (
  <div className="grid grid-cols-2 xl:grid-cols-4 gap-[16px] h-[480px] p-[32px] overflow-x-hidden overflow-y-auto">
    {mapSolutionsForYou.map((content, index_nivel) => (
      <SolutionsItemWrapper key={index_nivel}>
        {content.map((data, index_data) => (
          <SolutionsItem
            key={`${index_nivel}-${index_data}`}
            title={data.title}
            content={data.content}
          />
        ))}
      </SolutionsItemWrapper>
    ))}
  </div>
)
interface ISolutionsItemWrapper {
  children: ReactNode
}
const SolutionsItemWrapper: FC<ISolutionsItemWrapper> = ({ children }) => (
  <div className="flex flex-col gap-[12px]">{children}</div>
)
interface ISolutionsItem extends SolutionsForYouList {}
const SolutionsItem: FC<ISolutionsItem> = ({ title, content }) => (
  <div className="flex flex-col items-start gap-[4px] rounded-lg">
    <p
      className={cn(
        'font-acuminPro-Semibold text-[14px] leading-[20px] font-semibold text-[#68826E]',
        'h-[32px]'
      )}
    >
      {title}
    </p>
    {content.map((item, index) => (
      <SolutionsItemContent key={index} {...item} />
    ))}
  </div>
)
interface ISolutionsItemContent extends SolutionsForYouItem {}
const SolutionsItemContent: FC<ISolutionsItemContent> = ({
  icon,
  title,
  description,
  link
}) => (
  <SolutionsItemContentLink {...{ link }}>
    <div
      className={cn(
        'flex items-center p-[3px]',
        'size-[32px] rounded-[6px] border-0 border-solid',
        'shadow-[0px_4px_8px_-2px_rgba(16,24,40,0.10),0px_2px_4px_-2px_rgba(16,24,40,0.06)]'
      )}
    >
      <Icon
        icon={icon}
        width="25"
        height="25"
        viewBox="0 0 25 25"
        className="stroke-comerc-vibra-brandComerc-600 m-auto"
        strokeWidth="1.5"
      />
    </div>
    <div>
      <p className="font-acuminPro-Semibold text-comerc-grayLight-900 text-ellipsis text-[16px] leading-[24px]">
        {title}
      </p>
      <p className="font-acuminPro-Regular text-ellipsis text-comerc-grayLight-600 text-[14px] leading-[20px]">
        {description}
      </p>
    </div>
  </SolutionsItemContentLink>
)
interface ISolutionsItemContentLink {
  children: ReactNode
  link: string
}
const SolutionsItemContentLink: FC<ISolutionsItemContentLink> = ({
  children,
  link
}) => {
  let css = 'flex items-start p-[12px] gap-[16px] rounded-[8px]'

  if (link) {
    css += 'cursor-pointer hover-menu-primary'
  }

  return !!link ? (
    <a href={link} target="_blank" className={css}>
      {children}
    </a>
  ) : (
    <div className={css}>{children}</div>
  )
}

const SolutionsItemCard: FC = () => (
  <div className="flex flex-col items-start gap-[16px] py-[20px] pr-[32px] pl-[20px]  bg-comerc-grayLight-50 dark:bg-comerc-grayLight-100 ml-auto">
    <div className="flex flex-col w-[270px] p-[12px] items-start gap-[24px] rounded-[8px]">
      <div className="flex h-[136px] max-w-[240px] content-center items-center rounded-[6px] ">
        <Image src={menuItemCard} width={194} height={136} alt="menuItemCard" />
      </div>
      <div className="flex flex-col items-start gap-[4px] w-[194px]">
        <p className="font-acuminPro-Semibold text-comerc-grayLight-900 text-ellipsis text-[16px] leading-[24px]">
          Economize até 12% na conta de luz!
        </p>
        <p className="font-acuminPro-Regular text-comerc-grayLight-600 text-ellipsis text-[14px] leading-[20px]">
          Conheça os benefícios da assinatura Comerc Energia.
        </p>
      </div>
      <div className="flex items-start gap-[12px]">
        <p className="font-acuminPro-Semibold text-comerc-vibra-brandComerc-600 text-[14px] font-semibold leading-[20px]">
          Saiba mais
        </p>
      </div>
    </div>
  </div>
)
