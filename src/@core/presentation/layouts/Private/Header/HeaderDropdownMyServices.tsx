import Link from 'next/link'
import { FC, useMemo } from 'react'

import { mapMenuGlobal } from '@/@core/content/mapGroupMenuGlobal.content'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import { memory } from '@/@core/infra/memory'
import { DropdownMenuItem } from '@/@core/presentation/shared/ui/dropdown-menu'
import { Icon } from '@/@core/presentation/shared/ui/icons'

export const HeaderDropdownMyServices: FC = () => {
  const authStore = useAuthStore()

  const links = useMemo(() => {
    const mode = String(process.env.NEXT_PUBLIC_LINK_MY_SERVICES)
    const { code } = memory.cookie.get().auth

    return mapMenuGlobal
      .filter((item) => authStore.state.me.groups.includes(item.group))
      .map((item) => ({
        name: item.name,
        link: `${item.links[mode]}?code=${code}`,
        icon: item.icon
      }))
  }, [authStore.state.me.groups])

  return (
    <>
      {links?.map((item) => (
        <DropdownMenuItem
          asChild
          key={item.name}
          className={cn(
            'flex items-center gap-[16px]',
            'cursor-pointer',
            'h-[48px] p-[12px]',
            'font-acuminPro-Semibold text-[16px] text-comerc-grayLight-900'
          )}
        >
          <Link target="_blank" href={item.link}>
            <span
              className={cn(
                'flex items-center p-[6px]',
                'size-[32px] rounded-[6px] border-0 border-solid',
                'shadow-[0px_4px_8px_-2px_rgba(16,24,40,0.10),0px_2px_4px_-2px_rgba(16,24,40,0.06)]'
              )}
            >
              <Icon
                icon={item.icon}
                height="24"
                width="24"
                viewBox="0 0 24 24"
                fill="none"
                className="icon-primary stroke-comerc-vibra-brandComerc-600 m-auto"
              />
            </span>
            {item.name}
          </Link>
        </DropdownMenuItem>
      ))}
    </>
  )
}
