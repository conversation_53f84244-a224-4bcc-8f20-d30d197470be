import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import React, { useState } from 'react'

import { useLogout } from '@/@core/framework/hooks/useLogout'
import useAuthStore from '@/@core/framework/store/hook/useAuthStore'

import { mapHeaderMyAccount } from '@/@core/content/mapHeaderMyAccount.content'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { ProfileAvatar } from '@/@core/presentation/layouts/Private/_parts/ProfileAvatar'
import { HeaderDropdownSolutionForYou } from '@/@core/presentation/layouts/Private/Header/HeaderDropdownSolutionForYou'
import { HeaderLinks } from '@/@core/presentation/layouts/Private/Header/HeaderLinks'
import { SideBarMobile } from '@/@core/presentation/layouts/Private/Menu/SideBarMobile'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/@core/presentation/shared/ui/dropdown-menu'
import { Icon as IconOld, faBars } from '@/@core/presentation/shared/ui/icon'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
  SheetTrigger
} from '@/@core/presentation/shared/ui/sheet'
import logoComercDark from '@/assets/images/logo-comerc-dark.svg'
import logoComerc from '@/assets/images/logo-comerc.svg'

export const LayoutPrivateHeader = () => {
  return (
    <>
      <Wrapper>
        <div className="w-full flex items-center">
          <Brand />
          <Links />
        </div>
        <div className="hidden lg:flex content-center items-center gap-[16px]">
          <Profile />
        </div>
        <div className="flex lg:hidden">
          <ButtonMobile />
        </div>
      </Wrapper>
      <HeaderDropdownSolutionForYou />
    </>
  )
}

const Wrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <div
      className={cn(
        'flex items-center z-50 overflow-hidden w-screen',
        'border-comerc-grayLight-300 bg-primary',
        'h-[64px] lg:h-[80px] py-[18px] px-6 border-b-[1px] rounded-[12px]',
        'dark:border-comerc-grayLight-700'
      )}
    >
      {children}
    </div>
  )
}

const Brand = () => {
  return (
    <>
      <Image
        className="my-[6px] block dark:hidden"
        src={logoComerc.src}
        width={112}
        height={32}
        alt="logo comerc"
      />
      <Image
        className="my-[6px] hidden dark:block"
        src={logoComercDark.src}
        width={112}
        height={32}
        alt="logo comerc"
      />
    </>
  )
}

const Links = () => {
  return <HeaderLinks />
}

const Profile = () => {
  const router = useRouter()

  const authStore = useAuthStore()
  const logout = useLogout()

  const linkMyAccount =
    mapHeaderMyAccount[String(process.env.NEXT_PUBLIC_LINK_MY_ACCOUNT)]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="overflow-hidden size-[40px] border-[0.75px] border-[#********] bg-comerc-grayLight-100 rounded-full p-1 cursor-pointer">
        <ProfileAvatar src={authStore.state.me.user.avatar} />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem className="cursor-pointer" asChild>
          <Link href={linkMyAccount} target="_blank">
            Minha Conta
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem
          className={cn(
            'cursor-pointer',
            router.pathname === '/access-denied' && 'hidden'
          )}
          onClick={() => logout()}
        >
          Sair
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

const ButtonMobile = () => {
  const [openDialog, setOpenDialog] = useState(false)

  const handleClickLink = () => {
    if (window.innerWidth >= 1024) return
    setOpenDialog(false)
  }

  return (
    <Sheet open={openDialog} onOpenChange={setOpenDialog}>
      <SheetTrigger asChild>
        <button
          data-testid="btn-toggleMenu"
          className="w-[20px] h-[20px] flex items-center justify-center text-primary"
        >
          <IconOld
            className="text-base leading-6 w-[20px] h-[20px]"
            icon={faBars}
          />
        </button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0">
        <SheetTitle />
        <SheetDescription />
        <SideBarMobile handleClickLink={handleClickLink} />
      </SheetContent>
    </Sheet>
  )
}
