import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import logoComercDark from '@/assets/images/logo-comerc-dark.svg'
import logoComerc from '@/assets/images/logo-comerc.svg'
import Image from 'next/image'
import { FC } from 'react'
import { MenuItems } from './MenuItems'
import { MenuProfile } from './MenuProfile'

interface SideBarMobileProps {
  handleClickLink?: () => void
}
export const SideBarMobile: FC<SideBarMobileProps> = ({ handleClickLink }) => {
  const authStore = useAuthStore()

  return (
    <div className="flex flex-col h-full bg-primary">
      <div
        data-testid="menu-mobile-wrapper"
        className={cn(
          'p-[16px] flex flex-col gap-[20px] w-full mb-auto overflow-y-auto'
        )}
      >
        <Image
          className="block dark:hidden"
          src={logoComerc.src}
          width={112}
          height={32}
          alt="logo comerc"
        />
        <Image
          className="hidden dark:block"
          src={logoComercDark.src}
          width={112}
          height={32}
          alt="logo comerc"
        />
        <MenuItems handleClickLink={handleClickLink} />
      </div>
      <MenuProfile
        isOpened={true}
        profile={{
          name: authStore.state.me.user.name,
          email: authStore.state.me.user.email,
          avatar: authStore.state.me.user.avatar
        }}
      />
    </div>
  )
}
