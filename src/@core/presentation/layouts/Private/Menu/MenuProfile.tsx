import { useLogout } from '@/@core/framework/hooks/useLogout'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { ProfileAvatar } from '@/@core/presentation/layouts/Private/_parts/ProfileAvatar'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import React, { ReactNode } from 'react'

interface ProfileData {
  name: string
  email: string
  avatar: string
}

interface MenuProfileProps {
  isOpened: boolean
  profile: ProfileData
}

export const MenuProfile: React.FC<MenuProfileProps> = ({
  isOpened,
  profile
}) => {
  const logout = useLogout()

  return (
    <MenuProfileWrapper>
      <Profile isOpened={isOpened} profile={profile} />
      <LogoutButton onLogout={() => logout()} />
    </MenuProfileWrapper>
  )
}

const MenuProfileWrapper: React.FC<{ children: ReactNode }> = ({
  children
}) => (
  <div
    data-testid="container"
    className="flex items-center pt-6 border-t border-primary w-full px-[8px] pb-[24px]"
  >
    {children}
  </div>
)

interface ProfileProps {
  isOpened: boolean
  profile: ProfileData
}

const Profile: React.FC<ProfileProps> = ({ isOpened, profile }) => (
  <div
    className={cn(
      'flex items-center gap-3 transition-all duration-150 w-full',
      isOpened ? 'max-w-full opacity-100' : 'max-w-0 opacity-0'
    )}
  >
    <div className="w-10 h-10 flex items-center">
      <ProfileAvatar src={profile.avatar} />
    </div>
    <div className="flex flex-col items-start max-w-[73%] overflow-hidden">
      <p
        data-testid="profile-name"
        className="text-sm leading-5 text-primary font-semibold truncate"
      >
        {profile.name}
      </p>
      <p
        data-testid="profile-email"
        className="text-sm leading-5 text-primary font-normal truncate"
      >
        {profile.email}
      </p>
    </div>
  </div>
)

interface LogoutButtonProps {
  onLogout: () => void
}

const LogoutButton: React.FC<LogoutButtonProps> = ({ onLogout }) => (
  <button
    onClick={onLogout}
    className={cn(
      'flex items-center justify-center ml-auto overflow-hidden transition duration-150 ease-in-out w-[36px] h-[36px]'
    )}
  >
    <Icon
      icon="logOut"
      width="20"
      height="20"
      fill="none"
      className="icon-menu-primary"
      strokeWidth="1.66667"
    />
  </button>
)
