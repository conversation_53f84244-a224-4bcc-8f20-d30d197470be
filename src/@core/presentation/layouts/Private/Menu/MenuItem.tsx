import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Icon, IconName } from '@/@core/presentation/shared/ui/icons'
import { IMenuItem } from '@/types/system/layout'
import Link from 'next/link'
import { FC } from 'react'
import { MenuItemText } from './MenuItemText'

interface MenuItemProps {
  item: IMenuItem
  toggleSubmodules: (slug: string) => void
  handleClickModule: (slug: string) => void
}
export const MenuItem: FC<MenuItemProps> = ({
  item,
  toggleSubmodules,
  handleClickModule
}) => {
  const hasSubmodules = !!item.subModules.length

  const isActive = !![
    item.active,
    !!item.subModules.filter((el) => el.active).length
  ].filter(Boolean).length

  const mapIcons: Record<string, IconName> = {
    '/dashboard': 'barChartSquare01',
    '/acl': 'lightning',
    '/apportionments': 'pieChart03',
    '/alarms': 'bellPlus',
    '/reports': 'fileAttachment04',
    '/register': 'fileCheck02',
    '/management': 'users01',
    '/operation': 'settings01'
  }

  return (
    <>
      <div
        className={cn(
          'flex items-center gap-2',
          'h-[40px] hover-menu-primary rounded-md',
          'duration-100 ease-in-out',
          'menu-item',
          {
            'text-palette-600 bg-comerc-grayLight-100 dark:bg-comerc-grayLight-800':
              isActive
          },
          { 'hidden lg:flex': item.onlyDesktop }
        )}
        data-testid="container"
      >
        <div className="w-[24px] h-[24px]">
          <Icon
            icon={mapIcons[item.slug]}
            fill="none"
            width="24"
            height="24"
            className="icon-menu-primary"
            strokeWidth="1.5"
          />
        </div>

        <MenuItemText
          data-testid="menu-link-text"
          handleClick={() =>
            hasSubmodules
              ? toggleSubmodules(item.slug)
              : handleClickModule(item.slug)
          }
          item={item}
        />

        <button
          data-testid="menu-list-btn"
          className={cn(
            'flex ml-auto',
            'menu-item-icon',
            hasSubmodules ? '' : 'w-0 overflow-hidden'
          )}
          onClick={() => toggleSubmodules(item.slug)}
        >
          <div
            className={cn(' duration-150 ease-linear', {
              '-rotate-180': item.showSubmodule
            })}
          >
            <div>
              <Icon
                icon="chevronDown"
                width="24"
                height="24"
                fill="none"
                className="icon-menu-primary"
                strokeWidth="1.66667"
              />
            </div>
          </div>
        </button>
      </div>

      <ul
        data-testid="menu-list-item-subModules duration-200"
        className="list-none menu-list-item-subModules m-0 p-0 w-[280px]"
      >
        {item.subModules.map((submodule) => (
          <li
            key={submodule.name}
            className={cn(
              'duration-150 ease-linear overflow-hidden',
              item.showSubmodule ? 'h-8' : 'h-0 opacity-0'
            )}
          >
            <Link
              href={submodule.to}
              onClick={() => handleClickModule(submodule.slug)}
              className={cn(
                'w-full inline-block',
                'font-acuminPro-Medium  text-[16px] leading-[24px] text-menu-primary',
                'mb-1 py-1 px-[12px] ml-[12px] rounded',
                'hover-menu-primary',
                {
                  'bg-comerc-grayLight-50 dark:bg-comerc-grayLight-800':
                    submodule.active
                }
              )}
            >
              {submodule.name}

              {submodule.prefixBeta && (
                <span className="text-xs font-acuminPro-Medium ml-1 text-moduleBeta">
                  {submodule.prefixBeta}
                </span>
              )}
            </Link>
          </li>
        ))}
      </ul>
    </>
  )
}
