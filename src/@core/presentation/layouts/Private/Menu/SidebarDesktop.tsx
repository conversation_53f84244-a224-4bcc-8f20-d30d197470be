import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { MenuItems } from './MenuItems'

interface SidebarProps {
  menuState: 'open' | 'close'
  setMenuState: (value: 'open' | 'close') => void
}

export const LayoutPrivateSidebarDesktop: React.FC<SidebarProps> = ({
  menuState,
  setMenuState
}) => {
  const refElement = useRef<HTMLDivElement>(null)
  const [manualToggle, setManualToggle] = useState(false)
  const [isWideScreen, setIsWideScreen] = useState(
    () => window.innerWidth >= 1920
  )

  const updateMenuState = useCallback(
    (isWide: boolean) => {
      setIsWideScreen(isWide)
      if (isWide) {
        setMenuState('open')
        setManualToggle(true)
        refElement.current?.setAttribute('data-menu-desktop', 'open')
        return
      }

      if (window.innerWidth < 1024) {
        refElement.current?.setAttribute('data-menu-desktop', 'close')
        setMenuState('close')
        setManualToggle(false)
        return
      }

      if (!manualToggle) {
        refElement.current?.setAttribute('data-menu-desktop', 'close')
      }
    },
    [manualToggle, setMenuState]
  )

  useEffect(() => {
    const handleResize = () => {
      updateMenuState(window.innerWidth >= 1920)
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [updateMenuState])

  const handleMouseOver = () => {
    const isNarrow = !isWideScreen && !manualToggle
    if (isNarrow) {
      refElement.current?.setAttribute('data-menu-desktop', 'open')
    }
  }

  const handleMouseLeave = () => {
    const isNarrow = !isWideScreen && !manualToggle
    if (isNarrow) {
      refElement.current?.setAttribute('data-menu-desktop', 'close')
    }
  }

  const toggleMenu = () => {
    const isClosing = menuState === 'close'
    const newState: 'open' | 'close' = isClosing ? 'open' : 'close'

    setMenuState(newState)
    setManualToggle(isClosing)
    refElement.current?.setAttribute('data-menu-desktop', newState)
  }

  const shouldShowOpenButton = menuState === 'close' || isWideScreen
  const shouldShowCloseButton = menuState !== 'close' && !isWideScreen

  return (
    <div
      data-testid="menuDesktop"
      ref={refElement}
      className={cn(
        'lg:border-r-[1px] border-primary',
        'duration-150 overflow-hidden',
        'flex flex-col bg-primary',
        'absolute top-[80px] bottom-0'
      )}
      data-menu-desktop
      onMouseOver={handleMouseOver}
      onMouseLeave={handleMouseLeave}
    >
      <div className="menu-handle-button">
        {shouldShowOpenButton && (
          <button
            className="m-auto"
            disabled={isWideScreen}
            onClick={toggleMenu}
          >
            <Icon
              icon="layoutPushRight"
              width="26"
              height="26"
              fill="none"
              className="icon-menu-primary stroke-comerc-grayLight-500"
              strokeWidth="1.66667"
            />
          </button>
        )}
        {shouldShowCloseButton && (
          <button className="m-auto" onClick={toggleMenu}>
            <Icon
              icon="layoutPushLeft"
              width="26"
              height="26"
              fill="none"
              className="icon-menu-primary stroke-comerc-grayLight-500 m-auto"
              strokeWidth="1.66667"
            />
          </button>
        )}
      </div>
      <MenuItems className="py-[8px]" />
    </div>
  )
}
