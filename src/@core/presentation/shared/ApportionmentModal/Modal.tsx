import { IApportionment } from '@/@core/domain/Apportionment'
import useListApportionmentsFeeDivisionTypes from '@/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Tabs } from '@/@core/presentation/shared/ui/tabs'
import {
  SelectContentOption,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react'
import { TabApportionmentAdditionalFees } from './_components/TabApportionmentAdditionalFees'
import { TabApportionmentCustomTariff } from './_components/TabApportionmentCustomTariff'
import { TabApportionmentPeriod } from './_components/TabApportionmentPeriod'
import { ITabDataRef, TabData } from './_components/TabData'
import { useLanguageApportionmentModal } from './Modal.hooks'
import {
  IApportionmentData,
  IApportionmentModalProps,
  IApportionmentModalRef,
  IModalData
} from './Modal.types'

export type { IApportionmentModalRef }

const tabValues = ['data', 'period', 'additionalFees', 'custonTariff']

export const ApportionmentModal = forwardRef<
  IApportionmentModalRef,
  IApportionmentModalProps
>((props, ref) => {
  const modalRef = useRef<IModalRootRef>(null)

  const tabDataRef = useRef<ITabDataRef>(null)

  const [stateData, setStateValues] = useState<IModalData>({
    tab: 'data',
    apportionment: {}
  })

  const systemStore = useSystemStore()
  const languageModal = useLanguageApportionmentModal()

  const listApportionmentsFeeDivisionTypes =
    useListApportionmentsFeeDivisionTypes()

  const setState = (values: Partial<IModalData>) => {
    setStateValues((prev) => ({ ...prev, ...values }))
  }
  const handleClose = () => {
    systemStore.setUnmountComponent('apportionment-tabData')
    systemStore.setUnmountComponent('apportionment-tabApportionmentPeriod')
    systemStore.setUnmountComponent(
      'apportionment-tabApportionmentAdditionalFees'
    )
    systemStore.setUnmountComponent('apportionment-tabCustomTariffs')

    memory.local.reset('apportionmentModal')

    setStateValues({ tab: 'data', apportionment: {} })

    listApportionmentsFeeDivisionTypes.set({
      list: []
    })
  }
  const handleTabs = ({
    apportionmentId,
    apportionmentTypeId
  }: {
    apportionmentId: number | null
    apportionmentTypeId: number | null
  }) => {
    memory.local.set({
      apportionmentModal: {
        tabPeriod: {
          search: { apportionmentId, page: 1 }
        },
        tabAdditionalFees: {
          search: { apportionmentId, apportionmentTypeId, page: 1 }
        },
        tabCustomTariffs: {
          search: { apportionmentId, page: 1 }
        }
      }
    })
  }

  const handler = (payload: Partial<IApportionmentData>) => {
    handleTabs({
      apportionmentId: payload.id ? Number(payload.id) : null,
      apportionmentTypeId: payload.apportionmentType?.id
        ? Number(payload.apportionmentType?.id)
        : null
    })
    tabDataRef.current?.handler({ ...payload })

    setStateValues({ tab: 'data', apportionment: payload })
  }
  const handleCreated = (apportionment: IApportionment) => {
    setState({ apportionment })

    handleTabs({
      apportionmentId: apportionment.id,
      apportionmentTypeId: apportionment.apportionmentType.id
    })

    props.handleCreatedTabData?.()
  }

  useImperativeHandle(ref, () => ({
    open: () => modalRef.current?.open(),
    close: () => modalRef.current?.close(),
    handler: (p) => setTimeout(() => handler(p), 0)
  }))

  return (
    <Modal.Root ref={modalRef} handleClose={handleClose} size="lg6">
      <Modal.Title>
        {stateData.apportionment?.id
          ? languageModal.titleEdit
          : languageModal.titleNew}
      </Modal.Title>

      <Modal.Content>
        <Tabs.Root
          value={stateData.tab}
          onValueChange={(tab) => setState({ tab })}
          className="lg:flex items-start gap-2"
        >
          <TabControl
            active={stateData.tab}
            apportionment={stateData.apportionment}
            onChangeTab={(tab) => setState({ tab })}
          />
          <Tabs.Content value={tabValues[0]} className="form-container">
            <TabData
              ref={tabDataRef}
              btnCancel={modalRef.current?.close}
              handleCreated={handleCreated}
              apportionment={stateData.apportionment}
            />
          </Tabs.Content>

          <Tabs.Content value={tabValues[1]}>
            <TabApportionmentPeriod apportionment={stateData.apportionment} />
          </Tabs.Content>

          <Tabs.Content value={tabValues[2]}>
            <TabApportionmentAdditionalFees
              apportionment={stateData.apportionment}
            />
          </Tabs.Content>

          <Tabs.Content value={tabValues[3]}>
            <TabApportionmentCustomTariff
              apportionment={stateData.apportionment}
            />
          </Tabs.Content>
        </Tabs.Root>
      </Modal.Content>
    </Modal.Root>
  )
})

const TabControl = ({
  active,
  apportionment,
  onChangeTab
}: {
  active: string
  apportionment: Partial<IApportionmentData>
  onChangeTab: (tab: string) => void
}) => {
  const languageModal = useLanguageApportionmentModal()

  const isEdit = !apportionment.id

  const tabList = useMemo(() => {
    return [...languageModal.tabs].reduce((list, tabName, i) => {
      list.push({
        value: tabValues[i],
        label: tabName
      })
      return list
    }, [] as SelectContentOption[])
  }, [languageModal.tabs])

  return (
    <>
      <Tabs.List className="hidden lg:flex justify-start flex-col pr-2 mr-6 min-h-[350px]">
        {tabList.map((tab, i) => (
          <Tabs.Trigger
            key={tab.value}
            value={tab.value}
            disabled={i > 0 ? isEdit : false}
          >
            {tab.label}
          </Tabs.Trigger>
        ))}
      </Tabs.List>

      <TagInput.Content
        className="lg:hidden"
        value={active}
        onChange={(value) => onChangeTab(value![0].value)}
        optionDisabled={isEdit ? [] : tabValues}
        options={tabList}
      />
    </>
  )
}
