import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsPeriodsApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'
import {
  IStateTabApportionmentPeriod,
  IStateTabApportionmentPeriodData,
  ISubmitPayload,
  ITabApportionmentPeriodModal,
  ITabApportionmentPeriodValues
} from './TabApportionmentPeriod.types'

const stateData: IStateTabApportionmentPeriodData = {
  table: {
    items: [],
    lastPage: 0,
    total: 0
  }
}

export const useStateTabApportionmentPeriodModal =
  create<IStateTabApportionmentPeriod>((set) => ({
    ...stateData,
    set: (initialData) => set((state) => ({ ...state, ...initialData })),
    reset: () => set((state) => ({ ...state, ...stateData }))
  }))

export const useMethodsTabApportionmentPeriodModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const stateModal = useStateTabApportionmentPeriodModal()
  const languageModal = useLanguageTabApportionmentPeriodModal()

  const fetchData = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      const { search } = memory.local.get().apportionmentModal.tabPeriod

      const { data } = await apportionmentsPeriodsApiV3(http).get({
        ...search,
        apportionmentId: Number(search.apportionmentId)
      })

      stateModal.set({
        table: {
          items: data.items,
          total: data.total,
          lastPage: data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'ApportionmentModal/_components/TabApportionmentPeriod/fetchData'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleSubmit = async (payload: ISubmitPayload) => {
    const { messages } = languageModal.form

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = payload.id
        ? await apportionmentsPeriodsApiV3(http).update(payload.id, payload)
        : await apportionmentsPeriodsApiV3(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentModal/_components/TabApportionmentPeriod/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })

      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleDelete = async (apportionmentPeriodId: number) => {
    const { messages } = languageModal.table.modalDelete

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = await apportionmentsPeriodsApiV3(http).delete(
        apportionmentPeriodId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentModal/_components/TabApportionmentPeriod/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return { fetchData, handleSubmit, handleDelete }
}
export const useLanguageTabApportionmentPeriodModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, modal } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields
  const { form, table } = modal.apportionment.tabPeriod

  return {
    form: {
      input: {
        period: form.input.period,
        periodStart: form.input.periodStart,
        periodEnd: form.input.periodEnd
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    },
    table: {
      columns: table.columns,
      modalDelete: {
        ...table.modalDelete,
        btn: { cancel, confirm }
      }
    }
  }
}

/** UTILS */
export const formDataInput = (
  data: ITabApportionmentPeriodModal
): ITabApportionmentPeriodValues => {
  return {
    id: data.id,
    period: data.period,
    periodStart: data.periodStart,
    periodEnd: data.periodEnd
  }
}
export const formDataOutput = (
  data: ITabApportionmentPeriodValues & {
    apportionmentId: number
  }
): ISubmitPayload => {
  return {
    id: data.id,
    period: data.period,
    periodStart: data.periodStart,
    periodEnd: data.periodEnd,
    apportionmentId: data.apportionmentId
  }
}
