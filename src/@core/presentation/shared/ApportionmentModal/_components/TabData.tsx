import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { forwardRef, useEffect, useImperativeHandle } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'


import useListApportionmentMeasuresUnits from '@/@core/framework/store/hook/useListApportionmentMeasuresUnits'
import useListApportionmentTariffTypes from '@/@core/framework/store/hook/useListApportionmentTariffTypes'
import useListApportionmentTypes from '@/@core/framework/store/hook/useListApportionmentTypes'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Input } from '@/@core/presentation/shared/ui/input'
import {
  formatInputValue,
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { ListApportionmentMeasuresUnitsService } from '@/@core/services/listApportionmentMeasuresUnitsService'
import { ListApportionmentTariffTypesService } from '@/@core/services/listApportionmentTariffTypesService'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import { memory } from '@/@core/infra/memory'
import { IApportionmentData } from '../Modal.types'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabDataModal,
  useMethodsTabDataModal
} from './TabData.hooks'
import { ITabDataProps, ITabDataRef, ITabDataValues } from './TabData.types'
export type { ITabDataRef } from './TabData.types'

const hideApportionmentTypeIds = [4, 5, 6]

export const TabData = forwardRef<ITabDataRef, ITabDataProps>((props, ref) => {
  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const languageTabModal = useLanguageTabDataModal()
  const methodsTabModal = useMethodsTabDataModal()

  const formFields = useTabDataFormFields()

  const listApportionmentMeasuresUnits = useListApportionmentMeasuresUnits()
  const listApportionmentTariffTypes = useListApportionmentTariffTypes()
  const listApportionmentTypes = useListApportionmentTypes()

  const listApportionmentMeasuresUnitsService =
    ListApportionmentMeasuresUnitsService()
  const listApportionmentTariffTypesService =
    ListApportionmentTariffTypesService()

  const handler = async (values: Partial<IApportionmentData>) => {
    defineValuesFormFields(
      formFields.setValue,
      formDataInput({ ...values }),
      formValuesTabDataInitial
    )

    if (systemStore.state.mountComponent?.['apportionment-tabData']) return

    await methodsTabModal.getDependencies({
      apportionmentTypeId: Number(values.apportionmentType?.id)
    })

    systemStore.setMountComponent('apportionment-tabData')
  }

  useImperativeHandle(ref, () => ({
    handler: handler
  }))

  useEffect(() => {
    if (!!props.apportionment.id) handler(props.apportionment)
  }, [])

  const onSubmit = async () => {
    const payload = formDataOutput(formFields.values)

    const { status, data } = await methodsTabModal.handleSubmit(payload)

    console.log({ status, data })

    /** error */
    if (!status) return

    if (!formFields.values.id) {
      data?.id && formFields.setValues({ id: data.id })

      data && props?.handleCreated?.(data)
    }
  }

  const handleType = (apportionmentTypeId: number) => {
    try {
      systemLoading.setData({ modalLoading: true })

      formFields.setValue('apportionmentType', apportionmentTypeId.toString())
      formFields.setValue('apportionmentTariffType', '')
      formFields.setValue('apportionmentMeasureUnit', '')

      /** reset */
      memory.local.reset(['apportionmentsTariffTypesList'])
      listApportionmentMeasuresUnits.set({ list: [] })

      /** update */
      listApportionmentTariffTypesService.handler()
      listApportionmentMeasuresUnitsService.handler(apportionmentTypeId)
    } catch (error) {
      console.log(error)
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return (
    <>
      <form
        onSubmit={formFields.handleSubmit(onSubmit)}
        className="grid gap-3"
        id="form-tabData"
      >
        <Input.Root>
          <Input.Label htmlFor="name">{languageTabModal.form.input.name}</Input.Label>
          <Input.Content
            id="name"
            name="name"
            value={formFields.values.name}
            onChange={({ target }) => formFields.setValue('name', target.value)}
            disabled={systemLoading.state.modalLoading}
            helperText={formFields.errors.name?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="description">{languageTabModal.form.input.description}</Input.Label>
          <Input.Content
            id="description"
            name="description"
            value={formFields.values.description}
            onChange={({ target }) =>
              formFields.setValue('description', target.value)
            }
            disabled={systemLoading.state.modalLoading}
            helperText={formFields.errors.description?.message}
          />
        </Input.Root>

        <TagInput.Root>
          <TagInput.Label htmlFor="companies">{languageTabModal.form.input.company}</TagInput.Label>
          <TagInput.ContentApi
            name="companies"
            value={formatInputValues(formFields.values.companies)}
            onChange={(values) =>
              formFields.setValue('companies', formatOutputValues(values))
            }
            featchData={(p) => companiesApiV4(http).get({ ...p, sort: 'nome' })}
            helperText={formFields.errors?.companies?.message}
            disabled={systemLoading.state.modalLoading}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Label>{languageTabModal.form.input.consumption}</Input.Label>
          <Checkbox.Root className="min-h-[40px]">
            <Checkbox.Content
              id="mixedConsumption"
              checked={formFields.values.mixedConsumption}
              onCheckedChange={() =>
                formFields.setValue(
                  'mixedConsumption',
                  !formFields.values.mixedConsumption
                )
              }
              disabled={systemLoading.state.modalLoading}
            />
            <Checkbox.Label
              htmlFor="mixedConsumption"
              className="cursor-pointer"
            >
              <span className="text-[16px]">
                {languageTabModal.form.input.consumptionInfo}
              </span>
            </Checkbox.Label>
          </Checkbox.Root>
        </Input.Root>

        <div className="grid gap-[6px]">
          <Input.Label>{languageTabModal.form.input.type}</Input.Label>
          <Checkbox.List className="min-h-4">
            {listApportionmentTypes.state.list
              .filter((apportionmentType) => {
                return !hideApportionmentTypeIds.includes(apportionmentType.id)
              })
              .map((apportionmentType) => (
                <Checkbox.Root key={`apportionmentType-${apportionmentType.id}`}>
                  <Checkbox.Content
                    id={`apportionmentType-${apportionmentType.id}`}
                    checked={
                      formFields.values.apportionmentType ===
                      apportionmentType.id.toString()
                    }
                    onCheckedChange={() => handleType(apportionmentType.id)}
                    disabled={
                      !!formFields.values.id || systemLoading.state.modalLoading
                    }
                  />
                  <Checkbox.Label
                    htmlFor={`apportionmentType-${apportionmentType.id}`}
                    className="cursor-pointer"
                  >
                    <span className="text-[16px]">
                      {apportionmentType.name}
                    </span>
                  </Checkbox.Label>
                </Checkbox.Root>
              ))}
          </Checkbox.List>
        </div>

        <TagInput.Root>
          <TagInput.Label htmlFor="apportionmentMeasureUnit">
            {languageTabModal.form.input.measurementUnit}
          </TagInput.Label>
          <TagInput.Content
            name="apportionmentMeasureUnit"
            value={formFields.values.apportionmentMeasureUnit}
            onChange={(values) =>
              formFields.setValue('apportionmentMeasureUnit', values![0].value)
            }
            options={listApportionmentMeasuresUnits.state.list.map(
              formatInputValue
            )}
            helperText={
              !listApportionmentMeasuresUnits.state.list.length
                ? languageTabModal.form.input.unitMeasurementNotAvailable
                : formFields.errors?.apportionmentMeasureUnit?.message
            }
            disabled={
              systemLoading.state.modalLoading ||
              !listApportionmentMeasuresUnits.state.list.length
            }
          />
        </TagInput.Root>

        <div className="grid gap-[6px]">
          <Input.Label>{languageTabModal.form.input.tariffType}</Input.Label>

          <Checkbox.List className="min-h-4">
            {listApportionmentTariffTypes.state.list.map(
              (apportionmentTariffType) => (
                <Checkbox.Root
                  key={`apportionmentTariffType-${apportionmentTariffType.id}`}
                >
                  <Checkbox.Content
                    id={`apportionmentTariffType-${apportionmentTariffType.id}`}
                    checked={
                      formFields.values.apportionmentTariffType ===
                      apportionmentTariffType.id.toString()
                    }
                    onCheckedChange={() =>
                      formFields.setValue(
                        'apportionmentTariffType',
                        apportionmentTariffType.id.toString()
                      )
                    }
                    disabled={systemLoading.state.modalLoading}
                  />
                  <Checkbox.Label
                    htmlFor={`apportionmentTariffType-${apportionmentTariffType.id}`}
                    className="cursor-pointer"
                  >
                    <span className="text-[16px]">
                      {apportionmentTariffType.name}
                    </span>
                  </Checkbox.Label>
                </Checkbox.Root>
              )
            )}
          </Checkbox.List>
        </div>
      </form>

      <div className="footer-form">
        <Button type="button" onClick={() => props?.btnCancel?.()}>
          {formFields.values.id
            ? languageTabModal.form.btn.cancel
            : languageTabModal.form.btn.clean}
        </Button>
        <Button type="submit" variant="primary" form="form-tabData">
          {formFields.values.id
            ? languageTabModal.form.btn.save
            : languageTabModal.form.btn.add}
        </Button>
      </div>
    </>
  )
})

const formValuesTabDataInitial: ITabDataValues = {
  id: null,
  name: '',
  description: '',
  companies: [],
  apportionmentType: '0',
  apportionmentTariffType: '',
  apportionmentMeasureUnit: '',
  mixedConsumption: false
}
const useTabDataFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabDataModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z
        .object({
          id: z.number().nullable(),
          name: z.string().min(1, { message }),
          description: z.string(),
          companies: z.array(
            z.object({
              id: z.number(),
              name: z.string()
            })
          ),
          apportionmentType: z.string(),
          apportionmentTariffType: z.string(),
          mixedConsumption: z.boolean(),
          apportionmentMeasureUnit: z.string()
        })
        .superRefine((values, ctx) => {
          if (!values.companies.length)
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message,
              path: ['companies']
            })
        })
    ),
    defaultValues: { ...formValuesTabDataInitial }
  })
  const values = form.watch()

  const setValues = (payload: Partial<ITabDataValues>) => {
    defineValuesFormFields(form.setValue, payload, formValuesTabDataInitial)
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
