import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsTariffsApiV3 } from '@/@core/infra/api/ApportionmentsTariffsApiV3'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import { memory } from '@/@core/infra/memory'
import {
  IStateTabApportionmentCustomTariffs,
  IStateTabApportionmentCustomTariffsData,
  ISubmitPayload,
  ITabApportionmentCustomTariffsValues
} from './TabApportionmentCustomTariff.types'

const stateData: IStateTabApportionmentCustomTariffsData = {
  table: {
    items: [],
    lastPage: 0,
    total: 0
  }
}
export const useStateTabApportionmentCustomTariffsModal =
  create<IStateTabApportionmentCustomTariffs>((set) => ({
    ...stateData,
    set: (initialData) => set((state) => ({ ...state, ...initialData })),
    reset: () => set((state) => ({ ...state, ...stateData }))
  }))

export const useMethodsTabApportionmentCustomTariffsModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const stateModal = useStateTabApportionmentCustomTariffsModal()
  const languageModal = useLanguageTabApportionmnetCustomTariffsModal()

  const fetchData = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      const { search } = memory.local.get().apportionmentModal.tabCustomTariffs

      const { data } = await apportionmentsTariffsApiV3(http).get({
        ...search,
        apportionmentId: Number(search.apportionmentId)
      })

      stateModal.set({
        table: {
          items: data.items,
          total: data.total,
          lastPage: data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'TabApportionmentCustomTariff.hooks/_components/TabCustomTariffs/fetchData'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleSubmit = async (payload: ISubmitPayload) => {
    const { messages } = languageModal.form

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = payload.id
        ? await apportionmentsTariffsApiV3(http).update(payload.id, payload)
        : await apportionmentsTariffsApiV3(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'TabApportionmentCustomTariff.hooks/_components/TabCustomTariffs/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })

      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleDelete = async (customTariffId: number) => {
    const { messages } = languageModal.table.modalDelete

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = await apportionmentsTariffsApiV3(http).delete(
        customTariffId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'TabApportionmentCustomTariff.hooks/_components/TabCustomTariffs/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return {
    fetchData,
    handleSubmit,
    handleDelete
  }
}
export const useLanguageTabApportionmnetCustomTariffsModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, modal } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields
  const { form, table } = modal.apportionment.tabCustomTariffs

  return {
    form: {
      input: {
        vigencyStart: form.input.vigencyStart,
        vigencyEnd: form.input.vigencyEnd,
        value: form.input.value
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    },
    table: {
      columns: table.columns,
      modalDelete: {
        ...table.modalDelete,
        btn: { cancel, confirm }
      }
    }
  }
}
/** UTILS */
export const formDataInput = (
  data: any
): ITabApportionmentCustomTariffsValues => {
  return {
    id: data.id,
    vigencyStart: data.vigencyStart,
    vigencyEnd: data.vigencyEnd,
    value: data.value
  }
}
export const formDataOutput = (
  data: Omit<ITabApportionmentCustomTariffsValues, 'value'> & {
    value: string
    apportionmentId: number
  }
): ISubmitPayload => {
  return {
    id: data.id,
    vigencyStart: data.vigencyStart,
    vigencyEnd: data.vigencyEnd,
    value: +data.value,
    apportionmentId: data.apportionmentId
  }
}
