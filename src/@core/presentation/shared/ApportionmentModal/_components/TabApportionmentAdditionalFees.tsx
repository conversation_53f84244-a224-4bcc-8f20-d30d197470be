import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { IApportionmentAdditionalFees } from '@/@core/domain/ApportionmentAdditionalFees'
import { useNumberFormatter } from '@/@core/framework/hooks/useNumberFormatter/hook'
import useListApportionmentsFeeDivisionTypes from '@/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import {
  TagInput,
  formatInputValue
} from '@/@core/presentation/shared/ui/tagInput'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import { IApportionmentData } from '../Modal.types'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabApportionmentAdditionalFeesModal,
  useMethodsTabApportionmentAdditionalFeesModal,
  useStateTabApportionmentAdditionalFeesModal
} from './TabApportionmentAdditionalFees.hooks'
import { ITabApportionmentAdditionalFeesValues } from './TabApportionmentAdditionalFees.types'

export const TabApportionmentAdditionalFees = (props: {
  apportionment: Partial<IApportionmentData>
}) => {
  const isMounted = useRef(false)

  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const stateTabModal = useStateTabApportionmentAdditionalFeesModal()
  const methodsTabModal = useMethodsTabApportionmentAdditionalFeesModal()
  const languageTabModal = useLanguageTabApportionmentAdditionalFeesModal()
  const { currencyFormat } = useNumberFormatter()

  const listApportionmentsFeeDivisionTypes =
    useListApportionmentsFeeDivisionTypes()

  const searchFields =
    memory.local.get().apportionmentModal.tabAdditionalFees.search

  const formFields = useTabApportionmentAdditionalFeesFormFields()

  const handler = async () => {
    if (
      systemStore.state.mountComponent?.[
        'apportionment-tabApportionmentAdditionalFees'
      ]
    )
      return

    await methodsTabModal.fetchData()

    await methodsTabModal.getDependencies()

    systemStore.setMountComponent(
      'apportionment-tabApportionmentAdditionalFees'
    )
  }

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      handler()
      return
    }
    return () => {
      isMounted.current = false
    }
  }, [])

  const onSubmit = async () => {
    const apportionmentId = Number(props.apportionment.id)

    const { status } = await methodsTabModal.handleSubmit(
      formDataOutput({ ...formFields.values, apportionmentId })
    )
    if (!status) return

    await methodsTabModal.fetchData()

    formFields.reset()
  }

  return (
    <>
      <div className="form-container my-4">
        <form
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-2"
          id="form-tabCustomTariffs"
          onSubmit={formFields?.handleSubmit?.(onSubmit)}
        >
          <Input.Root>
            <Input.Label>{languageTabModal.form.input.period}</Input.Label>
            <Input.ContentDateMonth
              value={
                formFields.values.period
                  ? dayjs(formFields.values.period).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'period',
                  dayjs(value).startOf('month').format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.period?.message}
              disabled={systemLoading.state.modalLoading}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label>{languageTabModal.form.input.name}</Input.Label>
            <Input.Content
              value={formFields.values.name}
              onChange={({ target }) =>
                formFields.setValue('name', target.value)
              }
              helperText={formFields.errors.name?.message}
              disabled={systemLoading.state.modalLoading}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label>{languageTabModal.form.input.value}</Input.Label>
            <Input.Content
              value={formFields.values.value}
              onChange={({ target }) =>
                formFields.setValue('value', target.value)
              }
              type="number"
              helperText={formFields.errors.value?.message}
              disabled={systemLoading.state.modalLoading}
            />
          </Input.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type">
              {languageTabModal.form.input.type}
            </TagInput.Label>
            <TagInput.Content
              name="type"
              value={formFields.values.divisionTypeId}
              onChange={(items) => {
                formFields.setValue('divisionTypeId', String(items?.[0].value))
              }}
              options={listApportionmentsFeeDivisionTypes.state.list.map(
                formatInputValue
              )}
              helperText={formFields.errors.divisionTypeId?.message}
            />
          </TagInput.Root>
        </form>

        <div className="footer-form">
          <Button type="button" onClick={() => formFields?.reset?.()}>
            {formFields.values.id
              ? languageTabModal.form.btn.cancel
              : languageTabModal.form.btn.clean}
          </Button>

          <Button type="submit" variant="primary" form="form-tabCustomTariffs">
            {formFields.values.id
              ? languageTabModal.form.btn.save
              : languageTabModal.form.btn.add}
          </Button>
        </div>
      </div>

      <Table.Root classNameWrapper="block">
        <Table.Header>
          <Table.Row>
            <Table.Head>{languageTabModal.table.columns.period}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.name}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.value}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.type}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {stateTabModal.table.items.map((apportionmentAdditionalFees) => (
            <Table.Row key={apportionmentAdditionalFees.id}>
              <Table.Cell>
                {dayjs(apportionmentAdditionalFees.period).format('MM/YYYY')}
              </Table.Cell>
              <Table.Cell>{apportionmentAdditionalFees.name}</Table.Cell>
              <Table.Cell>
                {apportionmentAdditionalFees.value
                  ? currencyFormat(apportionmentAdditionalFees.value, {
                      locale: 'pt-BR',
                      currency: 'BRL'
                    })
                  : ''}
              </Table.Cell>
              <Table.Cell>
                {apportionmentAdditionalFees.divisionType.name}
              </Table.Cell>
              <Table.Cell width={80} role="td-actions">
                <div className="flex items-center gap-2">
                  <ButtonDelete
                    apportionmentPeriodId={apportionmentAdditionalFees.id}
                    resetForm={formFields.reset}
                  />
                  <ButtonEdit
                    data={apportionmentAdditionalFees}
                    onClick={(p) => {
                      console.log(p)
                      formFields.setValues(p)
                    }}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>

        <Table.Paginate
          currentPage={searchFields.page}
          lastPage={stateTabModal.table.lastPage}
          handleChangePage={(page) => {
            memory.local.set({
              costCenterModal: {
                tabCustomTariffs: { search: { page } }
              }
            })
            methodsTabModal.fetchData()
          }}
        />
      </Table.Root>
    </>
  )
}
const ButtonDelete = ({
  apportionmentPeriodId,
  resetForm
}: {
  apportionmentPeriodId: number
  resetForm: Function
}) => {
  const modalRef = useRef<IModalRootRef>(null)

  const methodsTabModal = useMethodsTabApportionmentAdditionalFeesModal()
  const languageTabModal = useLanguageTabApportionmentAdditionalFeesModal()

  const handleClickConfirm = async () => {
    const { status } = await methodsTabModal.handleDelete(apportionmentPeriodId)

    if (!status) return

    await methodsTabModal.fetchData()

    modalRef.current?.close()

    resetForm()
  }

  return (
    <>
      <button
        className="table-td-action hover:cursor-pointer"
        onClick={() => modalRef.current?.open()}
      >
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>

      <Modal.Root ref={modalRef} size="lg2">
        <Modal.Title>{languageTabModal.table.modalDelete.title}</Modal.Title>
        <Modal.Content>
          {languageTabModal.table.modalDelete.textInfo}
        </Modal.Content>
        <Modal.Footer>
          <Button type="button" onClick={() => modalRef.current?.close()}>
            {languageTabModal.table.modalDelete.btn.cancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languageTabModal.table.modalDelete.btn.confirm}
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
const ButtonEdit = ({
  data,
  onClick
}: {
  data: IApportionmentAdditionalFees
  onClick: (payload: Partial<ITabApportionmentAdditionalFeesValues>) => void
}) => {
  return (
    <button
      className="table-td-action hover:cursor-pointer"
      onClick={() => {
        onClick(formDataInput(data))
      }}
    >
      <Icon
        icon="edit"
        className="icon-menu-primary"
        height="20"
        width="20"
        viewBox="0 0 20 20"
      />
    </button>
  )
}

const formValuesTabApportionmentPeriodInitial: ITabApportionmentAdditionalFeesValues =
  {
    id: null,
    name: '',
    period: '',
    value: '',
    divisionTypeId: ''
  }
const useTabApportionmentAdditionalFeesFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabApportionmentAdditionalFeesModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z.object({
        id: z.number().nullable(),
        period: z.string().min(1, { message }),
        name: z.string().min(1, { message }),
        value: z.string().min(1, { message }),
        divisionTypeId: z.string().min(1, { message })
      })
    ),
    defaultValues: { ...formValuesTabApportionmentPeriodInitial }
  })

  const values = form.watch()

  const setValues = (
    payload: Partial<ITabApportionmentAdditionalFeesValues>
  ) => {
    defineValuesFormFields(
      form.setValue,
      payload,
      formValuesTabApportionmentPeriodInitial
    )
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
