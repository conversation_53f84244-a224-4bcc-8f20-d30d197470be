import { create } from 'zustand'

import { IApportionmentAdditionalFees } from '@/@core/domain/ApportionmentAdditionalFees'
import useListApportionmentsFeeDivisionTypes from '@/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import {
  apportionmentsAdditionalFeesApiV3,
  apportionmentsFeeDivisionTypesApiV3
} from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import {
  IStateTabApportionmentAdditionalFees,
  IStateTabApportionmentAdditionalFeesData,
  ISubmitPayload,
  ITabApportionmentAdditionalFeesValues
} from './TabApportionmentAdditionalFees.types'

const stateData: IStateTabApportionmentAdditionalFeesData = {
  table: {
    items: [],
    lastPage: 0,
    total: 0
  }
}

export const useStateTabApportionmentAdditionalFeesModal =
  create<IStateTabApportionmentAdditionalFees>((set) => ({
    ...stateData,
    set: (initialData) => set((state) => ({ ...state, ...initialData })),
    reset: () => set((state) => ({ ...state, ...stateData }))
  }))
export const useMethodsTabApportionmentAdditionalFeesModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const stateModal = useStateTabApportionmentAdditionalFeesModal()
  const languageModal = useLanguageTabApportionmentAdditionalFeesModal()

  const listApportionmentsFeeDivisionTypes =
    useListApportionmentsFeeDivisionTypes()

  const fetchData = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      const { search } = memory.local.get().apportionmentModal.tabAdditionalFees

      const { data } = await apportionmentsAdditionalFeesApiV3(http).get({
        ...search,
        apportionmentId: Number(search.apportionmentId)
      })

      stateModal.set({
        table: {
          items: data.items,
          total: data.total,
          lastPage: data.lastPage
        }
      })
    } catch (error) {
      console.log(error)

      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentModal/_components/TabApportionmentAdditionalFee/fetchData'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleSubmit = async (payload: ISubmitPayload) => {
    const { messages } = languageModal.form

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = payload.id
        ? await apportionmentsAdditionalFeesApiV3(http).update(
            payload.id,
            payload
          )
        : await apportionmentsAdditionalFeesApiV3(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentModal/_components/TabApportionmentAdditionalFee/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })

      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleDelete = async (apportionmentAdditionalFeesId: number) => {
    const { messages } = languageModal.table.modalDelete

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = await apportionmentsAdditionalFeesApiV3(http).delete(
        apportionmentAdditionalFeesId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentModal/_components/TabApportionmentAdditionalFee/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  const getDependencies = async () => {
    const { search } = memory.local.get().apportionmentModal.tabAdditionalFees

    /** update */
    listApportionmentsFeeDivisionTypes.set({
      list: await apportionmentsFeeDivisionTypesApiV3(http)
        .get({
          typeId: Number(search.apportionmentTypeId)
        })
        .then(({ data }) => data)
    })
  }

  return { fetchData, handleSubmit, handleDelete, getDependencies }
}
export const useLanguageTabApportionmentAdditionalFeesModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, modal } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields
  const { form, table } = modal.apportionment.tabAdditionalFees

  return {
    form: {
      input: {
        period: form.input.period,
        name: form.input.name,
        value: form.input.value,
        type: form.input.type
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    },
    table: {
      columns: table.columns,
      modalDelete: {
        ...table.modalDelete,
        btn: { cancel, confirm }
      }
    }
  }
}

/** UTILS */
export const formDataInput = (
  data: IApportionmentAdditionalFees
): ITabApportionmentAdditionalFeesValues => {
  return {
    id: data.id,
    name: data.name,
    period: data.period,
    value: data.value.toString(),
    divisionTypeId: data.divisionType.id.toString()
  }
}
export const formDataOutput = (
  data: ITabApportionmentAdditionalFeesValues & { apportionmentId: number }
): ISubmitPayload => {
  return {
    id: data.id,
    name: data.name,
    period: data.period,
    value: Number(data.value),
    divisionTypeId: Number(data.divisionTypeId),
    apportionmentId: data.apportionmentId
  }
}
