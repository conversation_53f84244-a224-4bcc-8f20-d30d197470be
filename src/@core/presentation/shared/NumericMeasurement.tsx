import { useMemo } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'

interface INumericMeasurementProps {
  className?: string
  classNameValue?: string
  value: number | string | null
  unit: string
  decimal: number | undefined
}
const NumericMeasurement = ({
  className,
  classNameValue,
  ...props
}: INumericMeasurementProps) => {
  const currentValue = useMemo(() => {
    return (props.value ?? 0).toLocaleString('pt-BR', {
      minimumFractionDigits: props.decimal,
      maximumFractionDigits: props.decimal
    })
  }, [props.value])

  return (
    <span className={cn('', className)}>
      <span
        className={cn(
          'text-2xl font-acuminPro-Semibold leading-[44px] text-comerc-gray-900 dark:text-comerc-grayLight-400',
          classNameValue
        )}
      >
        {currentValue}
      </span>
      &nbsp;
      <span className="text-comerc-gray-700 text-[16px] font-thin leading-[30px] dark:text-comerc-grayLight-600">
        {props.unit}
      </span>
    </span>
  )
}

export default NumericMeasurement
