import '@/__mock__/logging/logger'

import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { useLanguageModal, useMethodsModal } from './Modal.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsExportExcelApiV3')
const spyApportionmentsExportExcelApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsExportExcelApiV3'),
  'apportionmentsExportExcelApiV3'
)

describe('src/@core/presentation/shared/ApportionmentExportDataModal/Modal | useLanguageModal', () => {
  it('check de modal title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageModal()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.title).toBe('Exportar rateio (excel)')
  })
})

describe('src/@core/presentation/shared/ApportionmentExportDataModal/Modal | useMethodsModal', () => {
  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsModal()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload = {
      apportionmentId: 120,
      date: '2025-01-01',
      sendCostCenters: false
    }

    /* request error **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsExportExcelApiV3.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao exportar rateio'
    )

    /* request success **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsExportExcelApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 204,
        data: [
          'Processo iniciado com sucesso, ao concluir enviaremos via email!'
        ]
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Processo iniciado com sucesso, ao concluir enviaremos via email!'
    )
  })
})
