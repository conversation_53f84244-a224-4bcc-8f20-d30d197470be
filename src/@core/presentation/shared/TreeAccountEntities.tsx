import { ReactNode } from 'react'

import { IAccountsEntitiesGrouped } from '@/@core/domain/AccountsEntities'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Icon, faSpinner } from '@/@core/presentation/shared/ui/icon'
import { Icon as IconOld } from '@/@core/presentation/shared/ui/icons'

export const TreeAccountEntitiesWrapper = ({
  children
}: {
  children: ReactNode
}) => {
  const loadingStore = useSystemLoadingStore()
  return (
    <div
      className={cn(
        'rounded-md px-2 relative',
        'h-[300px] lg:h-[350px] mb-2 overflow-y-auto border-2 border-comerc-grayLight-200 py-2',
        loadingStore.state.loading ? 'overflow-hidden' : 'overflow-x-hidden'
      )}
    >
      <div
        className={cn(
          'bg-white flex [&>*]:m-auto',
          'absolute top-0 left-0 right-0 bottom-0 duration-150',
          {
            'z-40 opacity-100': loadingStore.state.loading,
            '-z-10 opacity-0': !loadingStore.state.loading
          }
        )}
      >
        <Icon icon={faSpinner} className="animate-spin text-2xl" />
      </div>

      <div
        className={cn('duration-150', {
          'opacity-0': loadingStore.state.loading
        })}
      >
        {children}
      </div>
    </div>
  )
}
export const TreeAccountEntitiesItem = ({
  data,
  className,
  handleRowUpdate
}: {
  data: IAccountsEntitiesGrouped
  className?: string
  handleRowUpdate: (value: IAccountsEntitiesGrouped) => void
}) => {
  return (
    <div
      data-description="entity"
      className={cn('animate-fadeIn z-30', className)}
    >
      <div className="flex items-center p-1 min-h-8">
        <Checkbox.Root>
          <Checkbox.Content
            id={`checkbox-${data._uniqueKey}`}
            checked={data.checked}
            onCheckedChange={() => {
              handleRowUpdate({ ...data, checked: !data.checked })
            }}
          />
          <Checkbox.Label
            htmlFor={`checkbox-${data._uniqueKey}`}
            className="cursor-pointer"
          >
            <span className="text-[16px]">{data.entityName}</span>
          </Checkbox.Label>
        </Checkbox.Root>

        <button
          type="button"
          className={cn('ml-auto h-6 w-6 flex [&>*]:m-auto', {
            hidden: !data.subEntities.length,
            '[&>*]:rotate-180': data.showSubEntities
          })}
          onClick={() => {
            handleRowUpdate({ ...data, showSubEntities: !data.showSubEntities })
          }}
        >
          <IconOld
            icon="chevronDown"
            width="18"
            height="18"
            viewBox="0 0 20 20"
            className="icon-menu-primary duration-200"
          />
        </button>
      </div>

      <div
        data-description="entity-subentities"
        className={cn('duration-150', {
          'max-h-0 overflow-y-hidden opacity-0': data.showSubEntities
        })}
      >
        {data.subEntities.map((item) => (
          <TreeAccountEntitiesItem
            key={item._uniqueKey}
            data={item}
            className="ml-4"
            handleRowUpdate={handleRowUpdate}
          />
        ))}
      </div>
    </div>
  )
}
