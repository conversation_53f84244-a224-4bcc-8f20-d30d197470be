import { act, renderHook } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  formDataOutput,
  useLanguageModal,
  useMethodsModal
} from './CostCenterMultipleModal.hook'

jest.mock('@/@core/infra/api/CostCentersMultipleApiV3')

const spyDevicesApiV4 = jest.spyOn(
  require('@/@core/infra/api/CostCentersMultipleApiV3'),
  'costCentersMultipleApiV3'
)

describe('src/@core/presentation/shared/CostCenterMultipleModal | useMethodsModal', () => {
  it('should check return the function onSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsModal(),
        systemToast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request error **/
    await act(() => {
      result.current.systemToast.reset()
    })

    spyDevicesApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    const payload = {
      apportionmentId: 1,
      equipmentsAll: false,
      equipmentsAllCompanyGroup: false,
      equipmentsIds: [11],
      usageLimit: 100
    }

    let resultRequest: boolean | undefined

    await act(async () => {
      resultRequest = await result.current.method.onSubmit(payload)
    })

    expect(resultRequest).toBeFalsy()
    expect(result.current.systemToast.state.toasts).toHaveLength(1)
    expect(result.current.systemToast.state.toasts[0].message).toBe(
      'Ocorreu um erro, tente novamente mais tarde'
    )

    /* request success **/
    await act(() => {
      result.current.systemToast.reset()
    })

    spyDevicesApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: [
          'Os centros de custos estão sendo criados e logo estarão disponíveis'
        ]
      })
    }))

    await act(async () => {
      resultRequest = await result.current.method.onSubmit(payload)
    })

    expect(resultRequest).toBeTruthy()
    expect(result.current.systemToast.state.toasts).toHaveLength(1)
    expect(result.current.systemToast.state.toasts[0].message).toBe(
      'Os centros de custos estão sendo criados e logo estarão disponíveis'
    )
  })
})

describe('src/@core/presentation/shared/CostCenterMultipleModal | useLanguageModal', () => {
  it('check the title', () => {
    const { result } = renderHook(
      () => ({
        languageModal: useLanguageModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    expect(result.current.languageModal.title).toBe(
      'Criar múltiplos centros de custos'
    )
  })
})

describe('src/@core/presentation/shared/CostCenterMultipleModal | formDataOutput', () => {
  it('should check return the function', () => {
    const inputData = {
      apportionmentId: 1,
      equipmentsAll: false,
      equipmentsAllCompanyGroup: false,
      equipmentsIds: [{ id: 1, name: 'string' }],
      usageLimit: 100
    }
    const outData = {
      apportionmentId: 1,
      equipmentsAll: false,
      equipmentsAllCompanyGroup: false,
      equipmentsIds: [1],
      usageLimit: 100
    }

    const result = formDataOutput(inputData)

    expect(result).toEqual(outData)
  })
})
