import React, {
  forwardRef,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'

import {
  Dialog,
  IDialodContentSize
} from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'

export interface IModalRootRef {
  open: () => void
  close: () => void
}
export interface IModalRootProps {
  children: ReactNode
  handleOpen?: () => void
  handleClose?: () => void
  setOpen?: (p: boolean) => void
  open?: boolean
  closeOnClickOutside?: boolean
  disabled?: boolean
  size?: IDialodContentSize
  contentProps?: {
    style?: React.CSSProperties
  }
}

const ModalRoot = forwardRef<IModalRootRef, IModalRootProps>((props, ref) => {
  const isMounted = useRef(false)
  const {
    children,
    handleOpen,
    handleClose,
    size = 'sm',
    closeOnClickOutside = false,
    contentProps
  } = props

  let _title = null
  let _content = null
  let _footer = null

  React.Children.forEach(children, (element) => {
    if (!React.isValidElement(element)) return

    const { displayName } = element.type as unknown as { displayName: string }

    if (displayName === 'ModalTitle') _title = element
    if (displayName === 'ModalContent') _content = element
    if (displayName === 'ModalFooter') _footer = element
  })

  const [open, setOpen] = useState<boolean>(false)

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false)
  }))

  useEffect(() => {
    if (isMounted.current === false) {
      isMounted.current = true
      return
    }

    open && handleOpen?.()
    !open && handleClose?.()
  }, [open])

  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Content
        {...{ size, closeOnClickOutside, style: contentProps?.style }}
      >
        <Dialog.Header className="flex-row items-center space-y-0 mb-0">
          {_title && (
            <Dialog.Title className="text-lg text-comerc-grayLight-900">
              {_title}
            </Dialog.Title>
          )}

          <Dialog.Close
            className="ml-auto size-[44px] flex *:m-auto"
            disabled={props.disabled}
          >
            <Icon
              icon="xClose"
              width="24"
              height="24"
              fill="none"
              className="icon-menu-primary"
              strokeWidth="1.66667"
            />
          </Dialog.Close>
        </Dialog.Header>
        <Dialog.Description hidden />

        {_content}

        {_footer && <Dialog.Footer className="gap-2">{_footer}</Dialog.Footer>}
      </Dialog.Content>
    </Dialog.Root>
  )
})

const ModalTitle = ({ children }: { children: ReactNode }) => {
  return <>{children}</>
}
ModalTitle.displayName = 'ModalTitle'

const ModalContent = ({ children }: { children: ReactNode }) => {
  return (
    <section className="flex-1 overflow-y-scroll pr-2 -mr-2">
      {children}
    </section>
  )
}
ModalContent.displayName = 'ModalContent'

const ModalFooter = ({ children }: { children: ReactNode }) => {
  return <>{children}</>
}
ModalFooter.displayName = 'ModalFooter'

export const Modal = {
  Root: ModalRoot,
  Title: ModalTitle,
  Content: ModalContent,
  Footer: ModalFooter
}
