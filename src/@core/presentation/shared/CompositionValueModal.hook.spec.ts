import { renderHookWithRedux } from '@/utils/setupTest'
import { act } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { costCenterEquipmentResponseMock } from '@/__mock__/content/api-cost-center-equipment'

import {
  formDataOutput,
  useLanguageModal,
  useMethodsModal
} from './CompositionValueModal.hook'

jest.mock('@/@core/infra/api/CostCentersEquipmentsApiV3')

const spyCostCentersEquipmentsApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCentersEquipmentsApiV3'),
  'costCentersEquipmentsApiV3'
)

describe('src/@core/presentation/shared/CompositionValueModal | useMethodsModal', () => {
  it('should check return the function onSubmit', async () => {
    const { result } = renderHookWithRedux(() => ({
      method: useMethodsModal(),
      systemToast: useSystemToastStore()
    }))

    const payload = {
      costCenterId: 1,
      equipmentId: 1,
      usageLimit: 10,
      apportionmentGroupId: 1,
      costCompositionTypeId: 1
    }

    /* request error **/
    act(() => result.current.systemToast.reset())

    spyCostCentersEquipmentsApiV3.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    let resultRequest: boolean | undefined

    await act(async () => {
      resultRequest = await result.current.method.onSubmit(payload)
    })

    expect(resultRequest).toBeFalsy()
    expect(result.current.systemToast.state.toasts).toHaveLength(1)
    expect(result.current.systemToast.state.toasts[0].message).toBe(
      'Error creating composition value'
    )

    /* request success **/
    act(() => result.current.systemToast.reset())

    spyCostCentersEquipmentsApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: costCenterEquipmentResponseMock
      })
    }))

    await act(async () => {
      resultRequest = await result.current.method.onSubmit(payload)
    })

    expect(resultRequest).toBeTruthy()
    expect(result.current.systemToast.state.toasts).toHaveLength(1)
    expect(result.current.systemToast.state.toasts[0].message).toBe(
      'Composition value created successfully'
    )
  })
})

describe('src/@core/presentation/shared/CompositionValueModal | useLanguageModal', () => {
  it('check the title', () => {
    const { result } = renderHookWithRedux(() => ({
      languageModal: useLanguageModal()
    }))

    expect(result.current.languageModal.title).toBe('New Composition Value')
  })
})

describe('src/@core/presentation/shared/CompositionValueModal | formDataOutput', () => {
  it('should check return the function', () => {
    const inputData = {
      costCenters: [{ id: 101, name: 'costCenter' }],
      equipments: [{ id: 10, name: 'equipment' }],
      usageLimit: '50',
      apportionmentGroups: [{ id: 5, name: 'group' }],
      costCompositionType: '2'
    }
    const outData = {
      costCenterId: 101,
      equipmentId: 10,
      usageLimit: 50,
      apportionmentGroupId: 5,
      costCompositionTypeId: 2
    }

    const result = formDataOutput(inputData)

    expect(result).toEqual(outData)
  })
})

describe('src/@core/presentation/shared/CompositionValueModal | formDataOutput', () => {
  it('should check return the function when values null', () => {
    const inputData = {
      costCenters: [{ id: 101, name: 'costCenter' }],
      equipments: [{ id: 10, name: 'equipment' }],
      usageLimit: '',
      apportionmentGroups: [],
      costCompositionType: '2'
    }
    const outData = {
      costCenterId: 101,
      equipmentId: 10,
      usageLimit: 0,
      apportionmentGroupId: null,
      costCompositionTypeId: 2
    }

    const result = formDataOutput(inputData)

    expect(result).toEqual(outData)
  })
})
