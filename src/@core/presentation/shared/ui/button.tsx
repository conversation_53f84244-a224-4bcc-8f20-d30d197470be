import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

const buttonVariants = cva(
  [
    'button-base flex items-center gap-2',
    'border-[1px] rounded-[8px] duration-150',
    'disabled:cursor-not-allowed',
    'focus:shadow-[0_0_0_4px] focus-visible:outline-none outline-none',
    'shadow-sm shadow-[#1018280D]',
    'dark:hover:bg-comerc-grayLight-800'
  ],
  {
    variants: {
      variant: {
        primary: cn([
          'bg-comerc-vibra-brandComerc-700 hover:bg-comerc-vibra-brandComerc-600',
          'border-comerc-vibra-brandComerc-700 hover:border-comerc-vibra-brandComerc-600',
          'text-comerc-neutral-white',
          'focus:bg-comerc-vibra-brandComerc-600 focus:border-comerc-vibra-brandComerc-600 focus:text-comerc-grayLight-950 focus:shadow-comerc-vibra-brandComerc-600/25',
          'disabled:bg-comerc-grayLight-100 disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400'
        ]),
        'secondary-gray': cn([
          'bg-secondary',
          'border-secondary',
          'fg-secondary',
          'focus:bg-secondary focus:border-secondary focus:fg-secondary focus:shadow-comerc-grayLight-300/25',
          'hover:bg-secondary_hover focus:border-secondary_hover focus:fg-secondary_hover focus:shadow-comerc-grayLight-300/25',
          'disabled:bg-primary disabled:border-disabled_subtle disabled:fg-disabled'
        ]),
        'secondary-color': cn([
          'bg-white hover:bg-comerc-brand01-50',
          'border-comerc-brand01-300 hover:border-comerc-brand01-300',
          'text-comerc-brand01-700 hover:text-comerc-brand01-800',
          'focus:bg-white focus:border-comerc-brand01-300 focus:text-comerc-brand01-700 focus:shadow-comerc-brand01-300/25',
          'disabled:bg-white disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400'
        ]),
        'tertiary-gray': cn([
          'bg-transparent hover:bg-transparent',
          'border-transparent hover:border-transparent',
          'text-comerc-grayLight-600 hover:text-comerc-grayLight-700',
          'shadow-transparent',
          'focus:bg-transparent focus:border-transparent focus:text-comerc-grayLight-600 focus:shadow-transparent',
          'disabled:bg-transparent disabled:border-transparent disabled:text-comerc-grayLight-400'
        ]),
        'tertiary-color': cn([
          'bg-transparent hover:bg-comerc-brand01-50',
          'border-transparent hover:border-comerc-brand01-50',
          'text-comerc-brand01-700 hover:text-comerc-brand01-700',
          'shadow-transparent',
          'focus:bg-transparent focus:border-transparent focus:text-comerc-brand01-700 focus:shadow-transparent',
          'disabled:bg-transparent disabled:border-transparent disabled:text-comerc-grayLight-400'
        ]),
        'link-gray': cn([
          'bg-white hover:bg-transparent',
          'border-transparent hover:border-transparent',
          'text-comerc-grayLight-600 hover:text-comerc-grayLight-700',
          'shadow-transparent',
          'focus:bg-white focus:border-transparent focus:text-comerc-grayLight-600 focus:shadow-transparent',
          '[&.disabled]:bg-white [&.disabled]:border-transparent [&.disabled]:text-comerc-grayLight-400 [&.disabled]:cursor-not-allowed'
        ]),
        'link-color': cn([
          'bg-white hover:bg-transparent',
          'border-transparent hover:border-transparent',
          'text-comerc-brand01-700 hover:text-comerc-brand01-800',
          'shadow-transparent',
          'focus:bg-white focus:border-transparent focus:text-comerc-brand01-700 focus:shadow-transparent',
          '[&.disabled]:bg-white [&.disabled]:border-transparent [&.disabled]:text-comerc-grayLight-400 [&.disabled]:cursor-not-allowed'
        ]),
        'gradient-color': cn([
          '[&:not(:disabled)]:bg-gradient-to-l from-comerc-brand01-500 to-comerc-brand02-500 hover:from-comerc-brand01-600 hover:to-comerc-brand02-600',
          'border-white',
          'text-comerc-grayLight-950 hover:text-comerc-grayLight-950',
          'focus:from-comerc-brand02-500 focus:to-comerc-brand01-500  focus:text-comerc-grayLight-950 focus:shadow-comerc-brand01-500/25',
          'disabled:bg-comerc-grayLight-100 disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400'
        ]),
        'gradient-stroke': cn([
          '[&:not(:disabled)]:bg-gradient-to-l hover:from-comerc-brand01-500 hover:to-comerc-brand02-500',
          'border-comerc-brand01-300 hover:border-white',
          'text-comerc-brand01-800',
          'focus:bg-white focus:border-comerc-brand01-300 focus:text-comerc-brand01-700 focus:shadow-comerc-brand01-500/25',
          'disabled:bg-white disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400'
        ]),
        'error-primary': cn([
          'bg-comerc-error-600 hover:bg-comerc-error-700',
          'border-comerc-error-600 hover:border-comerc-error-700',
          'text-white',
          'focus:bg-comerc-error-600 focus:border-comerc-error-600 focus:text-white focus:shadow-comerc-error-500/25',
          'disabled:bg-comerc-grayLight-100 disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400'
        ]),
        'error-secondary': cn([
          'bg-white hover:bg-comerc-error-50',
          'border-comerc-error-300 hover:border-comerc-error-300',
          'text-comerc-error-700 hover:text-comerc-error-800',
          'focus:bg-white focus:border-comerc-error-300 focus:text-comerc-error-700 focus:shadow-comerc-error-300/25',
          'disabled:bg-white disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400'
        ]),
        'error-tertiary': cn([
          'bg-transparent hover:bg-transparent',
          'border-transparent hover:border-transparent',
          'text-comerc-error-700 hover:text-comerc-error-800',
          'shadow-transparent',
          'focus:bg-transparent focus:border-transparent focus:text-comerc-error-700 focus:shadow-transparent',
          'disabled:bg-transparent disabled:border-transparent disabled:text-comerc-grayLight-400'
        ]),
        'error-link': cn([
          'bg-transparent hover:bg-transparent',
          'border-transparent hover:border-transparent',
          'text-comerc-error-700 hover:text-comerc-error-800',
          'shadow-transparent',
          'focus:bg-transparent focus:border-transparent focus:text-comerc-error-700 focus:shadow-transparent',
          'disabled:bg-transparent disabled:border-transparent disabled:text-comerc-grayLight-400'
        ]),
        'icon-only': cn([
          'w-[40px] h-[40px] justify-center',
          'rounded-[8px] border border-comerc-grayLight-300',
          'hover:bg-comerc-grayLight-200',
          'focus:bg-comerc-grayLight-300 focus:shadow-comerc-grayLight-300/25',
          'disabled:bg-comerc-grayLight-100 disabled:border-comerc-grayLight-200 disabled:text-comerc-grayLight-400',
          'dark:border-comerc-vibra-grayLightMode-800'
        ])
      },
      size: {
        '2xs':
          'py-[6.5px] px-[10.5px] text-[10px] leading-[16px] font-acuminPro-Semibold',
        xs: 'py-[8.5px] px-[12.5px] text-[12px] leading-[18px] font-acuminPro-Semibold',
        sm: 'py-[8.63px] px-[14.62px] text-[14px] leading-[20px] font-acuminPro-Semibold',
        md: 'py-[10px] px-[14px] text-[16px] leading-[24px] font-acuminPro-Semibold',
        lg: 'py-[8px] px-[18.75px] text-[18px] leading-[28px] font-acuminPro-Semibold',
        xl: 'py-[9px] px-[20.75px] text-[20px] leading-[30px] font-acuminPro-Semibold',
        '2xl':
          'py-[13px] px-[22.75px] text-[20px] leading-[30px] font-acuminPro-Semibold',
        '3xl':
          'py-[17px] px-[22.75px] text-[20px] leading-[30px] font-acuminPro-Semibold'
      }
    },
    defaultVariants: {
      variant: 'secondary-gray',
      size: 'sm'
    }
  }
)

interface HTMLButtonElementApp extends HTMLButtonElement {
  Icon: React.ReactNode
}

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElementApp>,
    VariantProps<typeof buttonVariants> {}

const ButtonRoot = React.forwardRef<HTMLButtonElementApp, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(
          buttonVariants({ variant, size, className }),
          { [variant!]: true },
          ''
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
ButtonRoot.displayName = 'Button'

export interface ButtonIconProps {
  children: React.ReactNode
  className?: string
}
const ButtonIcon: React.FC<ButtonIconProps> = ({ children, className }) => {
  return (
    <span className={cn('inline-flex [&>*]:m-auto', className)}>
      {children}
    </span>
  )
}

export interface ButtonLinkProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement>,
    VariantProps<typeof buttonVariants> {
  disabled?: boolean
}
{
}
const ButtonLink = React.forwardRef<HTMLAnchorElement, ButtonLinkProps>(
  ({ children, className, variant, size, disabled, ...props }, ref) => {
    return (
      <a
        className={cn(
          buttonVariants({ variant, size, className }),
          'cursor-pointer',
          { disabled }
        )}
        ref={ref}
        {...props}
      >
        {children}
      </a>
    )
  }
)

const Button = Object.assign(ButtonRoot, { Icon: ButtonIcon, Link: ButtonLink })

export { Button, buttonVariants }
