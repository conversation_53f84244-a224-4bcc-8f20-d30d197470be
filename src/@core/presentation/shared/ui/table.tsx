import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'
import * as React from 'react'
import { Badge } from './badge'
import { Button } from './button'
import {
  Icon as IconOld,
  faArrowDown,
  faArrowLeft,
  faArrowRight,
  faArrowUp,
  faPlus,
  faSortUpDown,
  faSpinner
} from './icon'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink
} from './pagination'

const TableRoot = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement> & {
    classNameWrapper?: string
    classNameTable?: string
  }
>(
  (
    { className, classNameWrapper, classNameTable, children, ...props },
    ref
  ) => {
    let _info = null
    let _header = null
    let _body = null
    let _mobile = null
    let _paginate = null

    React.Children.forEach(children, (element) => {
      if (!React.isValidElement(element)) return

      const { displayName } = element.type as unknown as { displayName: string }

      if (displayName === 'TableHeader') {
        _header = element
      }
      if (displayName === 'TableBody') {
        _body = element
      }
      if (displayName === 'TablePaginate') {
        _paginate = element
      }
      if (displayName === 'TableMobile') {
        _mobile = element
      }
      if (displayName === 'TableInfo') {
        _info = element
      }
    })

    return (
      <div
        className={cn(
          'table-root',
          'border-[1px] border-comerc-grayLight-200',
          'rounded-[12px] duration-75',
          'dark:border-comerc-vibra-grayLightMode-800',
          className
        )}
      >
        {_info}

        <div
          className={cn(
            'hidden md:block relative w-full overflow-auto',
            'dark:border-comerc-vibra-grayLightMode-800',
            classNameWrapper
          )}
        >
          <table
            ref={ref}
            className={cn(
              'w-full caption-bottom text-sm bg-primary',
              classNameTable
            )}
            {...props}
          >
            {_header}
            {_body}
          </table>
        </div>

        {_mobile}
        {_paginate}
      </div>
    )
  }
)
TableRoot.displayName = 'TableRoot'

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead
    ref={ref}
    className={cn(
      '[&_tr]:border-b border-b-comerc-vibra-grayLightMode-200',
      '[&_th_.th--text]:bg-comerc-vibra-grayLightMode-50 dark:[&_th_.th--text]:bg-comerc-grayLight-950',
      className
    )}
    {...props}
  />
))
TableHeader.displayName = 'TableHeader'

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn(
      'transition-colors',
      '[&_tr]:border-b [&_tr:last-child]:border-0',
      '[&_tr]:data-[state=selected]:bg-comerc-grayLight-50 dark:[&_tr]:data-[state=selected]:bg-slate-800',
      className
    )}
    {...props}
  />
))
TableBody.displayName = 'TableBody'

const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      'border-t bg-slate-100/50 font-medium [&>tr]:last:border-b-0 dark:bg-slate-800/50',
      className
    )}
    {...props}
  />
))
TableFooter.displayName = 'TableFooter'

type HTMLTableRowElementProps = React.HTMLAttributes<HTMLTableRowElement> & {
  hide?: boolean
}
const TableRow = React.forwardRef<
  HTMLTableRowElement,
  HTMLTableRowElementProps
>(({ className, hide, ...props }, ref) => {
  return hide ? null : (
    <tr
      ref={ref}
      className={cn(
        'border-primary duration-150',
        'hover:bg-comerc-grayLight-50 dark:hover:bg-comerc-grayLight-50/5',
        className,
        { 'h-0 overflow-hidden': hide }
      )}
      {...props}
    />
  )
})
TableRow.displayName = 'TableRow'

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement> & {
    textClassName?: string
  }
>(({ className, textClassName, children, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      ' text-left [&:has([role=checkbox])]:pr-0 dark:text-slate-400  p-0 ',
      className
    )}
    {...props}
  >
    <span
      className={cn(
        'th--text h-[44px] px-4 flex items-center align-middle text-[12px] leading-[18px]',
        'text-comerc-grayLight-400 dark:text-comerc-grayLight-400 font-acuminPro-Regular',
        textClassName
      )}
    >
      {children}
    </span>
  </th>
))
TableHead.displayName = 'TableHead'

const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    height={35}
    className={cn(
      'dark:text-comerc-gray-50',
      'py-1.5 px-6 text-comerc-primary-900 align-middle [&:has([role=checkbox])]:pr-0',
      'text-comerc-grayLight-600 font-normal text-[14px] leading-[20px]',
      className
    )}
    {...props}
  />
))
TableCell.displayName = 'TableCell'

const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn('mt-4 text-sm text-slate-500 dark:text-slate-400', className)}
    {...props}
  />
))
TableCaption.displayName = 'TableCaption'

const TablePaginate = ({
  currentPage,
  lastPage,
  handleChangePage,
  disabled,
  status,
  hide
}: {
  currentPage: number
  lastPage: number
  handleChangePage: (page: number) => void
  disabled?: boolean
  status?: boolean
  hide?: boolean
}) => {
  const { lang } = useSystemLanguageStore().state
  const languageTable = languageByMode(lang).table

  interface IPage {
    value: number | null
    active: boolean
    ellipsis: boolean
  }
  const parsePage = (page: number, ellipsis = false) => ({
    value: page,
    active: false,
    ellipsis
  })

  const incrementPage = (arr: IPage[], start: number, end: number) => {
    for (let i = start; i <= end; i++) {
      if (i > 0) arr.push(parsePage(i))
    }
    return arr
  }

  const pages = React.useMemo<IPage[]>(() => {
    const firstPages = 3

    let data = []

    const start = 1
    const end = lastPage

    /** only first pages */
    data = incrementPage([], start, end)

    /** only first pages and last pages */
    if (lastPage === 4) {
      data = [
        ...data.slice(0, firstPages),
        parsePage(999999, true),
        ...data.slice(lastPage - 1, lastPage)
      ]
    }
    if (lastPage === 5) {
      data = [
        ...data.slice(0, firstPages),
        parsePage(999999, true),
        ...data.slice(lastPage - 2, lastPage)
      ]
    }
    if (lastPage === 6) {
      data = [
        ...data.slice(0, firstPages),
        parsePage(999999, true),
        ...data.slice(lastPage - firstPages, lastPage)
      ]
    }
    if (lastPage > 6) {
      let dataInitial =
        currentPage >= firstPages
          ? [...data].slice(currentPage - 2, currentPage + 1)
          : [...data].slice(0, firstPages)

      if (currentPage > lastPage - firstPages) {
        dataInitial = [...data].slice(lastPage - 6, lastPage - 3)
      }

      data = [
        ...dataInitial,
        parsePage(999999, true),
        ...[...data].slice(lastPage - 3, lastPage)
      ]
    }

    return data.map((page) => ({ ...page, active: page.value === currentPage }))
  }, [currentPage, lastPage])

  return (
    <Pagination
      className={cn(
        'px-6 bg-primary rounded-b-[12px] duration-150',
        status
          ? 'opacity-0 h-0 overflow-hidden'
          : 'opacity-100 h-min pt-[14px] pb-[18px]',
        { 'h-0 overflow-hidden py-0': hide }
      )}
    >
      <PaginationContent className="w-full flex items-center">
        <PaginationItem className="mr-auto">
          <PaginationLink
            className={cn(
              'font-semibold h-[36px] capitalize border-comerc-grayLight-300',
              currentPage === 1 || disabled
                ? 'opacity-50 hover:bg-transparent'
                : 'cursor-pointer'
            )}
            onClick={() => {
              if (!disabled && currentPage > 1)
                handleChangePage(currentPage - 1)
            }}
          >
            <IconOld icon={faArrowLeft} />
            <span className="hidden md:block">{languageTable.previous}</span>
          </PaginationLink>
        </PaginationItem>

        <PaginationItem className={cn('block md:hidden', 'cursor-normal')}>
          {currentPage} {languageTable.of} {lastPage}
        </PaginationItem>

        {pages.map((page) => (
          <PaginationItem
            key={`${currentPage}-${page.value}`}
            className={cn(
              'hidden md:block',
              page.ellipsis ? 'cursor-normal' : 'cursor-pointer'
            )}
          >
            <PaginationLink
              className={cn(
                'flex justify-center size-[40px] border-transparent rounded-[8px] bg-transparent',
                page.active
                  ? 'bg-comerc-vibra-grayLightMode-100 dark:bg-comerc-grayLight-800 border'
                  : 'font-thin',
                page.ellipsis ? 'px-2 w-[38px] hover:bg-transparent' : ''
              )}
              onClick={() => !page.ellipsis && handleChangePage(page.value!)}
            >
              {page.ellipsis ? <PaginationEllipsis /> : page.value}
            </PaginationLink>
          </PaginationItem>
        ))}

        <PaginationItem className="ml-auto">
          <PaginationLink
            className={cn(
              'font-semibold h-[36px] capitalize border-comerc-grayLight-300',
              currentPage === lastPage
                ? 'opacity-50 hover:bg-transparent'
                : 'cursor-pointer'
            )}
            onClick={() => {
              if (!disabled && currentPage < lastPage)
                handleChangePage(currentPage + 1)
            }}
          >
            <span className="hidden md:block">{languageTable.next}</span>
            <IconOld icon={faArrowRight} />
          </PaginationLink>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  )
}
TablePaginate.displayName = 'TablePaginate'

const TableRowLoading = ({
  status,
  colSpan,
  className
}: {
  status: boolean
  colSpan?: number
  className?: string
}) => {
  if (!status) return null
  return (
    <TableRow>
      <TableCell colSpan={colSpan}>
        <div
          className={cn(
            'flex items-center justify-center min-h-[300px]',
            className
          )}
        >
          <IconOld icon={faSpinner} className="animate-spin" />
        </div>
      </TableCell>
    </TableRow>
  )
}
TableRowLoading.displayName = 'TableRowLoading'

const TableCellIcon = ({
  sort,
  order,
  field,
  disabled
}: {
  sort?: string
  field?: string
  order?: 'asc' | 'desc'
  disabled?: boolean
}) => {
  const active = Boolean(sort && field && sort === field)

  return (
    <IconOld
      className={cn(
        'ml-2',
        active
          ? 'text-comerc-grayLight-600 dark:text-comerc-grayLight-400'
          : '',
        disabled && !active
          ? 'text-comerc-grayLight-600 dark:text-comerc-grayLight-400'
          : ''
      )}
      icon={!active ? faSortUpDown : order === 'desc' ? faArrowUp : faArrowDown}
    />
  )
}
TableCellIcon.displayName = 'TableCellIcon'

const TableMobile = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'table-mobile flex flex-col items-start md:hidden gap-[40px] py-[20px] bg-primary border-y-[1px] border-primary bg-primary',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})
TableMobile.displayName = 'TableMobile'

const TableInfo = ({
  children,
  className
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn(
        'table-info flex',
        'flex-wrap items-center gap-[12px] px-[24px] py-[20px] min-h-[93px] rounded-t-[12px]',
        'border-b-[1px] border-primary',
        className
      )}
    >
      {children}
    </div>
  )
}
TableInfo.displayName = 'TableInfo'

const TableInfoTitle = ({
  className,
  ...rest
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn(
        'table-info-title text-[18px] text-comerc-grayLight-900 font-acuminPro-Semibold leading-[28px] min-w-max dark:text-comerc-neutral-white',
        className
      )}
      {...rest}
    />
  )
}
const TableInfoBadge = ({
  children,
  className
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <Badge.Root variant="success" size={'sm'} className={cn(className, 'py-0')}>
      <Badge.Content>{children}</Badge.Content>
    </Badge.Root>
  )
}
const TableInfoNewRegister = ({
  children,
  onClick,
  permission
}: {
  children?: React.ReactNode
  onClick: () => void
  permission: boolean | undefined
}) => {
  if (!permission) return null
  return (
    <Button className="h-[40px]" variant="primary" onClick={onClick}>
      <div
        className={cn('flex items-center justify-center', children && 'gap-2')}
      >
        <span className="hidden md:block text-xs truncate">{children}</span>
        <Button.Icon>
          <IconOld icon={faPlus} className="text-[16px]" />
        </Button.Icon>
      </div>
    </Button>
  )
}

const Table = {
  Root: TableRoot,
  Header: TableHeader,
  Body: TableBody,
  Mobile: TableMobile,
  Footer: TableFooter,
  Head: TableHead,
  Row: TableRow,
  Cell: TableCell,
  CellIcon: TableCellIcon,
  RowLoading: TableRowLoading,
  Caption: TableCaption,
  Paginate: TablePaginate,
  Info: TableInfo,
  InfoTitle: TableInfoTitle,
  InfoBadge: TableInfoBadge,
  InfoNewRegister: TableInfoNewRegister
}

export { Table }
