import { FC } from 'react'

import { TimePicker, TimePickerProps } from '@mui/x-date-pickers'
import dayjs, { Dayjs } from 'dayjs'

interface InputContentTimeProps {
  className?: string
  label?: TimePickerProps<Dayjs>['label']
  helperErrorText?: string
  value?: TimePickerProps<Dayjs>['value'] | string
  defaultValue?: TimePickerProps<Dayjs>['defaultValue'] | string
  disabled?: TimePickerProps<Dayjs>['disabled']
  views?: TimePickerProps<Dayjs>['views']
  timeSteps?: TimePickerProps<Dayjs>['timeSteps']
  onChange: (p: Dayjs | null) => void
}

export const InputContentTime: FC<InputContentTimeProps> = ({
  onChange,
  helperErrorText,
  value,
  defaultValue,
  ...rest
}) => {
  const parsedValue = typeof value === 'string' ? dayjs(value, 'HH:mm') : value
  const parsedDefaultValue =
    typeof defaultValue === 'string'
      ? dayjs(defaultValue, 'HH:mm')
      : defaultValue

  return (
    <TimePicker
      slotProps={{
        textField: {
          size: 'small',
          helperText: helperErrorText
        }
      }}
      onChange={(newValue) => onChange(newValue as Dayjs)}
      timeSteps={{ minutes: 1, hours: 1 }}
      views={['hours', 'minutes']}
      value={parsedValue}
      defaultValue={parsedDefaultValue}
      {...rest}
    />
  )
}
