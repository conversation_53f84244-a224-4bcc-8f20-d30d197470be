import { cn } from '@/@core/framework/plugins/shadcn/utils'

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'w-full h-40 animate-pulse rounded-[8px] bg-gradient-to-r from-comerc-grayLight-100 to-comerc-grayLight-400 overflow-hidden',
        'dark:from-comerc.grayLight-800 dark:to-comerc-grayLight-700',
        className
      )}
      {...props}
    />
  )
}

export { Skeleton }
