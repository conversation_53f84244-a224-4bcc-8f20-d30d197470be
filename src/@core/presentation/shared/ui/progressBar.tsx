import { cn } from '@/@core/framework/plugins/shadcn/utils'
import React, { ReactNode } from 'react'

type ProgressBarRootProps = {
  className?: string
  children: ReactNode
}

const ProgressBarRoot: React.FC<ProgressBarRootProps> = ({
  children,
  className
}) => {
  return (
    <div
      className={cn(
        'w-full bg-comerc-grayLight-300 rounded h-[6px] relative',
        'dark:bg-comerc-grayLight-700',
        className
      )}
    >
      {children}
    </div>
  )
}

type ProgressBarMainProps = {
  classNamePercentageUsed?: string
  classNameExceededPercentage?: string
  maxValue: number
  currentValue: number
}

const ProgressBarMain: React.FC<ProgressBarMainProps> = ({
  classNamePercentageUsed,
  classNameExceededPercentage,
  maxValue,
  currentValue
}) => {
  const adjustedCurrentValue = Math.max(currentValue || 0, 0)

  const clampedPercentageUsed = Math.min(Math.max(adjustedCurrentValue, 0), 100)

  // Calcular a porcentagem excedida, caso ultrapasse 100%
  const exceededPercentage =
    adjustedCurrentValue > 100 ? adjustedCurrentValue : 0

  return (
    <div
      className={cn(
        'h-[6px] rounded transition-all duration-300',
        classNamePercentageUsed,
        clampedPercentageUsed > 0 ? 'bg-comerc-primary-400' : 'bg-none'
      )}
      style={{ width: `${clampedPercentageUsed}%` }}
    >
      {exceededPercentage > 0 && (
        <div
          className={cn(
            'h-[6px] bg-comerc-error-500 transition-all duration-300 ml-auto rounded',
            classNameExceededPercentage
          )}
          style={{
            width: `${Math.min(exceededPercentage, 100)}%`
          }}
        />
      )}
    </div>
  )
}

export const ProgressBar = {
  Root: ProgressBarRoot,
  Main: ProgressBarMain
}
