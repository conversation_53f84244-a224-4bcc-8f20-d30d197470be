'use client'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import * as SwitchPrimitives from '@radix-ui/react-switch'
import React, { forwardRef } from 'react'

interface SwitchContentProps {
  'data-testid'?: string
  className?: string
  label?: string
  labelPosition?: 'top' | 'bottom' | 'start' | 'end'
  classNameLabel?: string
  disabled?: boolean
  checked?: boolean
  defaultChecked?: boolean
  onChange?: (val: boolean) => void
  size?: 'sm' | 'md'
}

const switchSizes = {
  sm: 'w-[36px] h-[20px]',
  md: 'w-[44px] h-[24px]'
}

const thumbSizes = {
  sm: 'size-[16px] data-[state=checked]:translate-x-3',
  md: 'size-[20px] data-[state=checked]:translate-x-4'
}

const Switch = forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> & {
    size?: 'sm' | 'md'
  }
>(({ className, size = 'md', ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      'px-[4px] rounded-full transition-colors focus-visible:outline-none focus-visible:shadow-[0px_0px_0px_4px_rgba(69,212,46,0.24)] disabled:cursor-not-allowed disabled:opacity-50',
      'data-[state=checked]:bg-comerc-vibra-brandComerc-500',
      'data-[state=unchecked]:bg-comerc-grayLight-100 data-[state=unchecked]:dark:bg-comerc-grayLight-800',
      switchSizes[size],
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        'pointer-events-none block rounded-full',
        'transition-transform data-[state=unchecked]:translate-x-0',
        'shadow-[0px_1px_3px_0px_rgba(16,24,40,0.10),0px_1px_2px_0px_rgba(16,24,40,0.06)]',
        'bg-comerc-neutral-white',
        thumbSizes[size]
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export const SwitchContent = forwardRef<HTMLButtonElement, SwitchContentProps>(
  (
    {
      className,
      label,
      labelPosition,
      classNameLabel,
      onChange,
      size = 'md',
      ...rest
    },
    ref
  ) => {
    const handleChange = (checked: boolean) => {
      if (onChange) onChange(checked)
    }

    return (
      <div className={cn('flex items-center', className)}>
        {label && labelPosition === 'start' && (
          <span className={cn('mr-2 text-secondary', classNameLabel)}>
            {label}
          </span>
        )}
        <Switch
          ref={ref}
          {...rest}
          size={size}
          onCheckedChange={handleChange}
        />
        {label && labelPosition === 'end' && (
          <span className="ml-2 text-secondary">{label}</span>
        )}
      </div>
    )
  }
)
SwitchContent.displayName = 'SwitchContent'
