import { Highcharts } from '@/@core/framework/plugins/highcharts'
import { HighchartsReact } from '@/@core/framework/plugins/highchartsReact'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import 'highcharts/modules/no-data-to-display'
import { FC, useEffect } from 'react'
import { Skeleton } from '../skeleton'
import { optionsLang } from './ChartContent.content'

type HighchartOptions = Highcharts.Options & {
  series: Highcharts.SeriesOptionsType[]
}
interface IChartContentProps extends HighchartsReact.Props {
  chartOption: HighchartOptions
  skeletonOption?: {
    height: string
  }
  isLoading?: boolean
}

const ChartContent: FC<IChartContentProps> = ({
  chartOption,
  skeletonOption,
  isLoading,
  ...props
}) => {
  const languageStore = useSystemLanguageStore()

  useEffect(() => {
    Highcharts.setOptions({
      lang: optionsLang[languageStore.state.lang],
      credits: {
        enabled: false
      },
      title: {
        text: undefined
      }
    })
  }, [languageStore.state.lang])

  if (!!skeletonOption && isLoading) {
    return <Skeleton />
  }

  const { chart, tooltip, yAxis, xAxis, plotOptions } = chartOption

  const chartDefault = {
    zoomType: 'x',
    ...chart
  } as Highcharts.ChartOptions

  const tooltipDefault: Highcharts.TooltipOptions = {
    shared: true,
    ...tooltip
  }
  const yAxisDefault: Highcharts.YAxisOptions = {
    ...yAxis,
    title: {
      text: undefined,
      ...(yAxis as Highcharts.YAxisOptions)?.title
    }
  }
  const xAxisDefault: Highcharts.XAxisOptions = {
    ...xAxis,
    title: {
      text: undefined,
      ...(xAxis as Highcharts.XAxisOptions)?.title
    }
  }
  const plotOptionsDefault: Highcharts.PlotOptions = {
    ...plotOptions,
    column: {
      pointWidth: 12,
      borderWidth: 1,
      borderRadius: 10,
      ...plotOptions?.column
    },
    bar: {
      pointWidth: 12,
      borderRadius: 10,
      showInLegend: false,
      ...plotOptions?.bar
    }
  }
  const baseOptions: HighchartOptions = {
    noData: {
      style: {
        fontWeight: 'lighter',
        fontSize: '14px'
      }
    },
    legend: { enabled: false },
    ...chartOption,
    chart: chartDefault,
    yAxis: yAxisDefault,
    xAxis: xAxisDefault,
    tooltip: tooltipDefault,
    plotOptions: plotOptionsDefault,
    accessibility: {
      enabled: false
    }
  }

  return (
    <HighchartsReact {...props} highcharts={Highcharts} options={baseOptions} />
  )
}
export default ChartContent
