import React, { useEffect, useMemo, useState } from 'react'
import { components } from 'react-select'
import ReactSelect from 'react-select/creatable'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import {
  Icon as IconOld,
  fa<PERSON><PERSON><PERSON>,
  fa<PERSON><PERSON><PERSON>,
  faXmarkSolid
} from '@/@core/presentation/shared/ui/icon'

export interface SelectContentOption {
  value: string
  label: string
}
interface SelectContentProps {
  onChange: (p: string[]) => void
  value?: string | string[] | null
  defaultValue?: string | string[] | null
  name?: string
  closeMenuOnSelect?: boolean
  isClearable?: boolean
  placeholder?: string
  disabled?: boolean
  isMulti?: boolean
  onInputFilter?: (p: string) => boolean
  inputFilterMessage?: string
  helperTextProps?: {
    className?: string
  }
  helperText?: string
  childrenOption?: (args: SelectContentOption) => React.JSX.Element
  className?: string
  optionDisabled?: string[]
}
export const TagInputContentCreatable = (props: SelectContentProps) => {
  const {
    onChange,
    name: inputId,
    helperTextProps,
    helperText,
    childrenOption: ChildrenOption,
    value: currentValue,
    defaultValue: currentDefaultvalue,
    optionDisabled,
    disabled: isDisabled,
    isMulti = true,
    onInputFilter,
    inputFilterMessage,
    ...rest
  } = props

  const [options, setOptions] = useState<SelectContentOption[]>([])
  const [inputMessage, setInputMessage] = useState<string>('')

  const handleChange = (payload: unknown) => {
    const value = payload as SelectContentOption | SelectContentOption[]

    const values = (Array.isArray(value) ? value : [value])
      .filter(Boolean)
      .filter(({ value }) => (!!onInputFilter ? onInputFilter(value) : true))
      .map(({ value }) => value)

    onChange(values)
  }

  const renderOptions = (items: string[]): SelectContentOption[] => {
    return items.map((value) => ({
      value: String(value),
      label: String(value)
    }))
  }

  useEffect(() => {
    const value = currentValue ?? currentDefaultvalue

    const textList = (Array.isArray(value) ? value : [value])
      .filter((text) => !!text)
      .map((text) => String(text))

    const options = renderOptions(textList)

    setOptions(options)
  }, [currentValue, currentDefaultvalue])

  const values = useMemo(() => {
    if (!!currentValue)
      return {
        value: options.filter((option) => currentValue.includes(option.value))
      }

    if (!!currentDefaultvalue)
      return {
        defaultValues: options.filter((option) =>
          currentDefaultvalue.includes(option.value)
        )
      }

    return {}
  }, [options])

  return (
    <>
      <ReactSelect
        placeholder=""
        {...rest}
        {...values}
        options={options}
        inputId={inputId}
        onChange={handleChange}
        isDisabled={isDisabled}
        isMulti={isMulti}
        classNamePrefix="reactSelect"
        styles={{
          control: (baseStyles) => ({
            ...baseStyles,
            borderRadius: '8px'
          })
        }}
        isOptionDisabled={(option: SelectContentOption) => {
          return !!optionDisabled?.includes(option.value)
        }}
        onInputChange={(value) => {
          !!inputMessage && setInputMessage('')

          if (!!onInputFilter && inputFilterMessage && onInputFilter?.(value)) {
            setInputMessage(inputFilterMessage)
          }
        }}
        components={{
          NoOptionsMessage: () => null,
          LoadingIndicator: () => (
            <div className="w-[28px] min-h-[20px] flex [&>*]:m-auto text-comerc-grayLight-500 absolute right-[24px]">
              <IconOld icon={faSpinner} className="animate-spin" />
            </div>
          ),
          Option: ({ children, className, ...props }) => (
            <components.Option
              {...props}
              className={cn(className, 'flex items-baseline')}
            >
              {ChildrenOption && <ChildrenOption {...props.data} />}
              {props.label}
              {props.isSelected && (
                <IconOld
                  className="ml-auto text-comerc-success-500 w-[20px] h-[20px]"
                  icon={faCheck}
                />
              )}
            </components.Option>
          ),
          MultiValue: ({ children, ...props }) => {
            return (
              <components.MultiValue {...props} className={props.className}>
                {ChildrenOption && <ChildrenOption {...props.data} />}
                {children}
              </components.MultiValue>
            )
          },
          SingleValue: ({ children, ...props }) => {
            return (
              <components.SingleValue {...props}>
                {ChildrenOption && <ChildrenOption {...props.data} />}
                {children}
              </components.SingleValue>
            )
          },
          IndicatorSeparator: null,
          MultiValueRemove: (props) => (
            <components.MultiValueRemove {...props}>
              <span className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500 dark:text-comerc-grayLight-400">
                <IconOld
                  icon={faXmarkSolid}
                  className="text-[16px] font-bold"
                />
              </span>
            </components.MultiValueRemove>
          ),
          MultiValueLabel: (props) => (
            <span className="text-comerc-grayLight-500 dark:text-comerc-grayLight-400">
              <components.MultiValueLabel {...props} />
            </span>
          ),
          DropdownIndicator: (props) => (
            <components.DropdownIndicator
              className="w-[20px] min-h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500"
              {...props}
            />
          ),
          ClearIndicator: (props) => (
            <components.ClearIndicator {...props}>
              <span className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500">
                <IconOld icon={faXmarkSolid} className="text-lg font-bold" />
              </span>
            </components.ClearIndicator>
          )
        }}
      />
      {helperText && (
        <span
          data-inputtext
          className={cn(
            'text-[13px] leading-6 font-normal text-comerc-error-400',
            helperTextProps?.className
          )}
        >
          {helperText}
        </span>
      )}
      {inputMessage && (
        <span
          data-inputtext
          className={cn(
            'text-[13px] leading-6 font-normal text-comerc-error-400'
          )}
        >
          {inputMessage}
        </span>
      )}
    </>
  )
}
