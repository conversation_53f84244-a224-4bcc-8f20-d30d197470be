import {
  formatInputValue,
  formatInputValues,
  formatOutputValue,
  formatOutputValues,
  reduceOnlyValues,
  reduceOptionsByOnlyValues
} from './TagInput.utils'

describe('src/@core/presentation/shared/ui/tagInput/TagInput.utils', () => {
  it('should compare result data from function formatInputValues', () => {
    const valueString = { id: 'company', name: 'Name empresa' }
    const valueStringCommon = { value: 'company', label: 'Name empresa' }

    const valueNumber = { id: '1', name: 'Name empresa' }
    const valueNumberCommon = { value: '1', label: 'Name empresa' }

    expect(formatInputValue(valueString)).toEqual(valueStringCommon)

    expect(formatInputValue(valueNumber)).toEqual(valueNumberCommon)

    expect(formatInputValues(null)).toHaveLength(0)

    expect(formatInputValues([valueNumber, valueString])).toHaveLength(2)
  })

  it('should compare result data from function formatOutputValue', () => {
    const valueString = { value: 'company', label: 'Name empresa' }
    const valueStringCommon = { id: 'company', name: 'Name empresa' }

    const valueNumber = { value: '1', label: 'Name empresa' }
    const valueNumberCommon = { id: 1, name: 'Name empresa' }

    expect(formatOutputValue(valueNumber)).toEqual(valueNumberCommon)

    expect(formatOutputValue(valueString)).toEqual(valueStringCommon)

    expect(formatOutputValues(null)).toHaveLength(0)

    expect(formatOutputValues([valueNumber, valueString])).toHaveLength(2)
  })

  it('should compare result from function reduceOnlyValues', () => {
    const op1 = { label: 'label 1', value: '1' }
    const op2 = {
      label: 'label 2',
      value: '2',
      options: [
        {
          label: 'sub-label 1',
          value: 'sub-1'
        }
      ]
    }
    const op3 = '1'
    const op4 = 'sub-1'

    expect(reduceOnlyValues([op1, op2])).toHaveLength(3)

    expect(reduceOptionsByOnlyValues([op1, op2], [op3, op4])).toHaveLength(2)
  })
})
