import { TagInputContent } from './TagInputContent'
import { TagInputContentApi } from './TagInputContentApi'
import { TagInputContentCreatable } from './TagInputContentCreatable'
import { TagInputLabel } from './TagInputLabel'
import { TagInputRoot } from './TagInputRoot'

export type { SelectContentOption } from './TagInputContent'

export const TagInput = {
  Root: TagInputRoot,
  Content: TagInputContent,
  ContentApi: TagInputContentApi,
  ContentCreatable: TagInputContentCreatable,
  Label: TagInputLabel
}
