import { cn } from '@/@core/framework/plugins/shadcn/utils'
import {
  faC<PERSON>ck,
  faS<PERSON>ner,
  faXmarkSolid,
  Icon as IconOld
} from '@/@core/presentation/shared/ui/icon'
import { useDebounceFunction } from '@/hooks/useDebouce'
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import ReactSelect, { components, GroupBase } from 'react-select'
import Select from 'react-select/dist/declarations/src/Select'

interface ReactSelectContentFetchParams {
  limit: number
  page: number
  q: string
}
interface SelectContentFetchData extends ReactSelectContentFetchParams {
  lastPage: number
}
interface SelectContentOption {
  value: string
  label: string
}
interface SelectContentProps {
  onChange: (p: SelectContentOption[] | null) => void
  featchData: (p: ReactSelectContentFetchParams) => Promise<{
    status: number
    data: {
      items: unknown[]
      limit?: number
      page?: number
      lastPage?: number
    }
  }>
  featchDataItem?: (p: unknown) => {
    value: string
    name: string
    label: string
  }
  value?: SelectContentOption | SelectContentOption[] | null
  defaultValue?: SelectContentOption | SelectContentOption[] | null
  name?: string
  isMulti?: boolean
  closeMenuOnSelect?: boolean
  isClearable?: boolean
  placeholder?: string
  disabled?: boolean
  helperTextProps?: {
    className?: string
  }
  helperText?: string
  childrenOption?: (args: SelectContentOption) => React.JSX.Element
  isSearchable?: boolean
  menuPlacement?: 'top' | 'bottom' | 'auto'
  optionDisabled?: string[]
}
type StateData = {
  isLoading: boolean
  params: SelectContentFetchData
  options: SelectContentOption[]
}
const stateData: StateData = {
  isLoading: false,
  params: { limit: 15, page: 1, lastPage: 1, q: '' },
  options: []
}
export const TagInputContentApi = forwardRef<any, SelectContentProps>(
  (props, ref) => {
    const {
      onChange,
      featchData,
      featchDataItem,
      name: inputId,
      helperTextProps,
      helperText,
      disabled,
      childrenOption: ChildrenOption,
      menuPlacement = 'top',
      optionDisabled,
      ...rest
    } = props

    const firstRequest = useRef<boolean>(true)
    const inputRef = useRef<Select<
      SelectContentOption,
      boolean,
      GroupBase<SelectContentOption>
    > | null>(null)

    const [state, setStateData] = useState<StateData>({ ...stateData })

    const setState = useCallback((payload: Partial<StateData>) => {
      setStateData((prev) => ({ ...prev, ...payload }))
    }, [])
    const parseItemResultCallback = useCallback((item: unknown) => {
      const { id, name } = item as { id: string; name: string }
      return { value: id, label: name, name }
    }, [])

    const parseItemResult = featchDataItem ?? parseItemResultCallback

    const loadData = async (
      currentParams: Partial<ReactSelectContentFetchParams> = {}
    ) => {
      try {
        const params = {
          ...state.params,
          ...currentParams
        }

        setState({ isLoading: true, params })

        const result = await featchData({
          limit: params.limit,
          page: params.page,
          q: params.q
        })

        const {
          status,
          data: { items, page, lastPage }
        } = result

        const hasData = status === 200

        let newOptions: SelectContentOption[] = [...state.options]

        if (hasData)
          items.map(parseItemResult).forEach((item) => newOptions.push(item))

        if (optionDisabled)
          newOptions = newOptions.filter(
            (item) => !optionDisabled?.includes(item.value)
          )

        setState({
          isLoading: false,
          options: newOptions,
          params: {
            ...params,
            page: hasData && page ? page : stateData.params.page,
            lastPage: hasData && lastPage ? lastPage : stateData.params.lastPage
          }
        })
      } catch (error) {
        setState({ isLoading: false })
      }

      return state.options
    }
    const handleFocus = () => {
      if (!firstRequest.current) {
        return
      }
      firstRequest.current = false
      loadData()
    }
    const handleChange = (payload: unknown) => {
      const value = payload as SelectContentOption | SelectContentOption[]

      if (value === null) {
        firstRequest.current = true

        setStateData((prevState) => ({
          ...prevState,
          params: {
            ...prevState.params,
            q: ''
          }
        }))
      }

      const newValues: SelectContentOption[] = []

      const numberIdsIgnore: number[] = []

      const numberIds: number[] = (Array.isArray(value) ? value : [value])
        .filter(Boolean)
        .map((op) => Number(op.value))

      Array.from([
        ...(Array.isArray(rest?.defaultValue) ? rest.defaultValue : []),
        ...(Array.isArray(rest?.value) ? rest.value : [])
      ]).forEach((op, i) => {
        if (numberIds.includes(Number(op.value))) {
          newValues.push(op)
          numberIdsIgnore.push(Number(op.value))
        }
      })

      state.options
        .filter((op) => {
          const isNotIgnore = !numberIdsIgnore.includes(Number(op.value))
          const add = numberIds.includes(Number(op.value))
          return isNotIgnore && add
        })
        .forEach((op) => newValues.push(op))

      onChange(newValues)
    }
    const handleInput = useDebounceFunction(
      ({ query, action }: { query: string; action: string }) => {
        if (action === 'menu-close') return

        loadData({ q: query, page: 1 })
      },
      250
    )
    const scrollToBottom = useDebounceFunction(
      () => {
        const { page, lastPage } = state.params

        if (page < lastPage) {
          loadData({ page: page + 1 })
        }
      },
      { delay: 50, deps: [state.params] }
    )

    useImperativeHandle(ref, () => ({
      reset: () => {
        firstRequest.current = true
        setStateData({ ...stateData })
      },
      focus: () => {
        handleFocus()
        inputRef.current?.focus()
      }
    }))

    return (
      <>
        <ReactSelect
          ref={inputRef}
          placeholder=""
          {...{ ...rest, menuPlacement }}
          menuPlacement="auto"
          isDisabled={disabled}
          inputId={inputId}
          isLoading={state.isLoading}
          options={state.options}
          onChange={handleChange}
          onFocus={handleFocus}
          onMenuOpen={handleFocus}
          openMenuOnFocus
          onInputChange={(query, { action }) => handleInput({ query, action })}
          onMenuScrollToBottom={scrollToBottom}
          menuPortalTarget={
            typeof window !== 'undefined' ? document.body : undefined
          }
          menuShouldScrollIntoView={false}
          // noOptionsMessage={() => 'SEM DADOS'}
          // loadingMessage={() => 'BUSCANO !!'}
          classNamePrefix="reactSelect"
          styles={{
            menuPortal: (base) => ({
              ...base,
              zIndex: 100,
              pointerEvents: 'auto'
            }),
            control: (baseStyles) => ({
              ...baseStyles,
              borderRadius: '8px'
            })
          }}
          // isClearable={false}
          // isOptionDisabled={(option: SelectContentOption) => {
          //   return !!optionDisabled?.includes(option.value)
          // }}
          components={{
            // DownChevron: () => <span>XXXX</span>
            // DropdownIndicator: () => <IconOld icon={faSpinner} className="animate-spin" />
            // LoadingMessage: () => <span>loading</span>,
            // NoOptionsMessage: () => <span>SEM CONTEÚDO</span>
            LoadingIndicator: () => (
              <div className="w-[28px] min-h-[20px] flex [&>*]:m-auto text-comerc-grayLight-500 absolute right-[24px]">
                <IconOld icon={faSpinner} className="animate-spin" />
              </div>
            ),
            Option: ({ children, className, ...props }) => (
              <components.Option
                {...props}
                className={cn(className, 'flex items-baseline')}
              >
                {ChildrenOption && <ChildrenOption {...props.data} />}
                {props.label}
                {props.isSelected && (
                  <IconOld
                    className="ml-auto text-comerc-success-500 w-[20px] h-[20px]"
                    icon={faCheck}
                  />
                )}
              </components.Option>
            ),
            MultiValue: ({ children, ...props }) => {
              return (
                <components.MultiValue {...props} className={props.className}>
                  {ChildrenOption && <ChildrenOption {...props.data} />}
                  {children}
                </components.MultiValue>
              )
            },
            SingleValue: ({ children, ...props }) => {
              return (
                <components.SingleValue {...props}>
                  {ChildrenOption && <ChildrenOption {...props.data} />}
                  {children}
                </components.SingleValue>
              )
            },
            IndicatorSeparator: null,
            MultiValueRemove: (props) => (
              <components.MultiValueRemove {...props}>
                <span className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500 dark:text-comerc-grayLight-400">
                  <IconOld
                    icon={faXmarkSolid}
                    className="text-[16px] font-bold"
                  />
                </span>
              </components.MultiValueRemove>
            ),
            MultiValueLabel: (props) => (
              <span
                className="text-comerc-grayLight-900"
                title={props.children?.toString().replace(',', '')}
              >
                <components.MultiValueLabel {...props} />
              </span>
            ),
            DropdownIndicator: (props) => (
              <components.DropdownIndicator
                className="w-[28px] min-h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500"
                {...props}
              />
            ),
            ClearIndicator: (props) => (
              <components.ClearIndicator {...props}>
                <span className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500">
                  <IconOld icon={faXmarkSolid} className="text-lg font-bold" />
                </span>
              </components.ClearIndicator>
            ),
            Menu: (props) => <components.Menu {...props} />
          }}
        />
        {helperText && (
          <span
            data-inputtext
            className={cn(
              'text-[13px] leading-6 font-normal text-comerc-error-400',
              helperTextProps?.className
            )}
          >
            {helperText}
          </span>
        )}
      </>
    )
  }
)
