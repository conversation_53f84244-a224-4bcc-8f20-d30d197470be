import { SelectContentOption } from './TagInputContent'

export type IValueInternal = {
  value: string
  label: string
}
export type IValueCommonIn = {
  id: number | string
  name: string
}
export type IValueCommonOut = {
  id: number
  name: string
}

export const formatOutputValue = ({
  value,
  label: name,
  ...rest
}: IValueInternal): IValueCommonOut => {
  const id = (Number.isNaN(Number(value)) ? value : Number(value)) as unknown

  return { ...rest, name, id } as IValueCommonOut
}

export const formatOutputValues = (
  values: IValueInternal[] | null
): IValueCommonOut[] => {
  return values?.map(formatOutputValue) ?? []
}

export const formatInputValue = ({
  id,
  name,
  ...rest
}: IValueCommonIn): IValueInternal => {
  return { ...rest, value: String(id), label: name }
}

export const formatInputValues = (
  values: IValueCommonIn[] | null
): IValueInternal[] => {
  return values?.map(formatInputValue) ?? []
}

export const reduceOnlyValues = (values: SelectContentOption[]): string[] => {
  const reduceValues = (acc: string[], el: SelectContentOption) => {
    acc.push(el.value)
    el.options?.reduce(reduceValues, []).forEach((e) => acc.push(e))
    return acc
  }
  return values.filter(Boolean).reduce(reduceValues, [] as string[])
}

export const reduceOptionsByOnlyValues = (
  options: SelectContentOption[],
  onlyValues: string[]
): SelectContentOption[] => {
  const reduceOptions = (
    acc: SelectContentOption[],
    el: SelectContentOption
  ) => {
    if (onlyValues.includes(el.value)) acc.push(el)
    el.options?.reduce(reduceOptions, []).forEach((e) => acc.push(e))
    return acc
  }
  return options.reduce(reduceOptions, [])
}
