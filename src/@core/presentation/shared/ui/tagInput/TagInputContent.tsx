import React, { forwardRef, useImperative<PERSON><PERSON>le, useMemo } from 'react'
import ReactSelect, { components } from 'react-select'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import {
  Icon as IconOld,
  faC<PERSON><PERSON>,
  fa<PERSON><PERSON><PERSON>,
  faXmarkSolid
} from '@/@core/presentation/shared/ui/icon'
import { reduceOnlyValues, reduceOptionsByOnlyValues } from './TagInput.utils'

export interface SelectContentOption {
  value: string
  label: string
  options?: {
    value: string
    label: string
    options?: SelectContentOption[]
  }[]
}
interface SelectContentProps {
  onChange: (p: SelectContentOption[] | null) => void
  value?: string | string[] | null
  defaultValue?: string | string[] | null
  name?: string
  isMulti?: boolean
  closeMenuOnSelect?: boolean
  isClearable?: boolean
  placeholder?: string
  disabled?: boolean
  helperTextProps?: {
    className?: string
  }
  helperText?: string
  childrenOption?: (args: SelectContentOption) => React.JSX.Element
  options: SelectContentOption[]
  className?: string
  optionDisabled?: string[]
}
export const TagInputContent = forwardRef<any, SelectContentProps>(
  (props, ref) => {
    const {
      onChange,
      name: inputId,
      helperTextProps,
      helperText,
      childrenOption: ChildrenOption,
      value: currentValue,
      defaultValue: currentDefaultvalue,
      optionDisabled,
      disabled: isDisabled,
      ...rest
    } = props

    const handleFocus = () => {}

    const handleChange = (payload: unknown) => {
      const value = payload as SelectContentOption | SelectContentOption[]

      onChange(
        reduceOptionsByOnlyValues(
          rest.options,
          reduceOnlyValues(Array.isArray(value) ? value : [value])
        )
      )
    }

    useImperativeHandle(ref, () => ({}))

    const values = useMemo(
      () => ({
        value: currentValue
          ? reduceOptionsByOnlyValues(
              props.options,
              Array.isArray(currentValue) ? currentValue : [currentValue]
            )
          : undefined,
        defaultValue: currentDefaultvalue
          ? reduceOptionsByOnlyValues(
              props.options,
              Array.isArray(currentDefaultvalue)
                ? currentDefaultvalue
                : [currentDefaultvalue]
            )
          : undefined
      }),
      [props.options, currentValue, currentDefaultvalue]
    )

    return (
      <>
        <ReactSelect
          placeholder=""
          {...rest}
          {...values}
          inputId={inputId}
          onChange={handleChange}
          onFocus={handleFocus}
          onMenuOpen={handleFocus}
          isDisabled={isDisabled}
          // noOptionsMessage={() => 'SEM DADOS'}
          // loadingMessage={() => 'BUSCANO !!'}
          classNamePrefix="reactSelect"
          menuPortalTarget={
            typeof window !== 'undefined' ? document.body : undefined
          }
          menuShouldScrollIntoView={false}
          menuPlacement="auto"
          styles={{
            menuPortal: (base) => ({
              ...base,
              zIndex: 100,
              pointerEvents: 'auto'
            }),
            control: (baseStyles) => ({
              ...baseStyles,
              borderRadius: '8px'
            })
          }}
          isOptionDisabled={(option: SelectContentOption) => {
            return !!optionDisabled?.includes(option.value)
          }}
          components={{
            // DownChevron: () => <span>XXXX</span>
            // DropdownIndicator: () => <IconOld icon={faSpinner} className="animate-spin" />
            // LoadingMessage: () => <span>loading</span>,
            // NoOptionsMessage: () => <span>SEM CONTEÚDO</span>
            LoadingIndicator: () => (
              <div className="w-[28px] min-h-[20px] flex [&>*]:m-auto text-comerc-grayLight-500 absolute right-[24px]">
                <IconOld icon={faSpinner} className="animate-spin" />
              </div>
            ),
            Option: ({ children, className, ...props }) => (
              <components.Option
                {...props}
                className={cn(className, 'flex items-baseline')}
              >
                {ChildrenOption && <ChildrenOption {...props.data} />}
                {props.label}
                {props.isSelected && (
                  <IconOld
                    className="ml-auto text-comerc-success-500 w-[20px] h-[20px]"
                    icon={faCheck}
                  />
                )}
              </components.Option>
            ),
            MultiValue: ({ children, ...props }) => {
              return (
                <components.MultiValue {...props} className={props.className}>
                  {ChildrenOption && <ChildrenOption {...props.data} />}
                  {children}
                </components.MultiValue>
              )
            },
            SingleValue: ({ children, ...props }) => {
              return (
                <components.SingleValue {...props}>
                  {ChildrenOption && <ChildrenOption {...props.data} />}
                  {children}
                </components.SingleValue>
              )
            },
            IndicatorSeparator: null,
            MultiValueRemove: (props) => (
              <components.MultiValueRemove {...props}>
                <span className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500 dark:text-comerc-grayLight-400">
                  <IconOld
                    icon={faXmarkSolid}
                    className="text-[16px] font-bold"
                  />
                </span>
              </components.MultiValueRemove>
            ),
            MultiValueLabel: (props) => (
              <span className="text-comerc-grayLight-500 dark:text-comerc-grayLight-400">
                <components.MultiValueLabel {...props} />
              </span>
            ),
            DropdownIndicator: (props) => (
              <components.DropdownIndicator
                className="w-[20px] min-h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500"
                {...props}
              />
            ),
            ClearIndicator: (props) => (
              <components.ClearIndicator {...props}>
                <span className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500">
                  <IconOld icon={faXmarkSolid} className="text-lg font-bold" />
                </span>
              </components.ClearIndicator>
            )
          }}
        />
        {helperText && (
          <span
            data-inputtext
            className={cn(
              'text-[13px] leading-6 font-normal text-comerc-error-400',
              helperTextProps?.className
            )}
          >
            {helperText}
          </span>
        )}
      </>
    )
  }
)
