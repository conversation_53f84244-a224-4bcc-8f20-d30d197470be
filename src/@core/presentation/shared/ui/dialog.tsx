'use client'

import * as DialogPrimitive from '@radix-ui/react-dialog'
import * as React from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Icon as IconOld, faSpinner } from '@/@core/presentation/shared/ui/icon'
import { Icon } from '@/@core/presentation/shared/ui/icons'

export type IDialodContentSize =
  | 'sm'
  | 'md'
  | 'lg'
  | 'lg2'
  | 'lg3'
  | 'lg4'
  | 'lg5'
  | 'lg6'

const DialogRoot = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-[50] bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
    overlay?: boolean
    closeButton?: boolean
    size?: IDialodContentSize
    closeOnClickOutside?: boolean
  }
>(
  (
    {
      className,
      children,
      overlay = true,
      closeButton,
      size,
      closeOnClickOutside = true,
      ...props
    },
    ref
  ) => {
    const mapWidth: Record<string, string> = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      lg2: 'max-w-2xl',
      lg3: 'max-w-3xl',
      lg4: 'max-w-4xl',
      lg5: 'max-w-5xl',
      lg6: 'max-w-6xl'
    }

    return (
      <DialogPortal>
        {overlay ? <DialogOverlay /> : null}
        <DialogPrimitive.Content
          ref={ref}
          className={cn(
            'fixed left-[50%] top-[50%] z-[50] grid w-[calc(100%_-_2rem)] max-w-lg max-h-screen translate-x-[-50%] translate-y-[-50%] gap-4 p-6 shadow-lg sm:rounded-lg ',
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]',
            'border border-secondary',
            'bg-primary',
            className,
            !!size && mapWidth[size]
          )}
          onInteractOutside={(e) => {
            !closeOnClickOutside && e.preventDefault()
          }}
          {...props}
        >
          {children}

          {closeButton && (
            <DialogPrimitive.Close className="absolute right-5 top-6 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-slate-100 data-[state=open]:text-slate-500 dark:ring-offset-slate-950 dark:focus:ring-slate-300 dark:data-[state=open]:bg-slate-800 dark:data-[state=open]:text-slate-400">
              <Icon
                icon="xClose"
                fill="none"
                width="24"
                height="25"
                className="icon-menu-primary"
                strokeWidth="2"
                viewBox="0 0 24 25"
              />
              <span className="sr-only">Close</span>
            </DialogPrimitive.Close>
          )}
        </DialogPrimitive.Content>
      </DialogPortal>
    )
  }
)
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col space-y-1.5 text-center sm:text-left p-6 -mx-6 -mt-6 mb-5',
      'border-b-[1px] border-comerc-grayLight-200 dark:border-comerc-grayLight-800',
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = 'DialogHeader'

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 -mx-6 -mb-6',
      'border-t-[1px] border-comerc-grayLight-200 dark:border-comerc-grayLight-800',
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = 'DialogFooter'

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      'text-2xl pt-5 pb-2 leading-none tracking-tight p-0',
      'text-primary',
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn('text-sm text-slate-500 dark:text-slate-400', className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

const DialogIconLoading: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <IconOld
      icon={faSpinner}
      className={cn('animate-spin text-lg', className)}
    />
  )
}

const Dialog = {
  Root: DialogRoot,
  Header: DialogHeader,
  Title: DialogTitle,
  Trigger: DialogTrigger,
  Content: DialogContent,
  Footer: DialogFooter,
  Description: DialogDescription,
  Close: DialogClose,
  IconLoading: DialogIconLoading
}

export {
  Dialog
  // DialogPortal,
  // DialogOverlay,
  // DialogClose,
  // DialogTrigger,
  // DialogContent,
  // DialogHeader,
  // DialogFooter,
  // DialogTitle,
  // DialogDescription
}
