import { iconPathMap } from '@/@core/content/iconPathMap.content'
import React from 'react'

export type IconName =
  | 'barChartSquare'
  | 'file'
  | 'clipboard'
  | 'lightning'
  | 'monitor'
  | 'settings'
  | 'usersEdit'
  | 'chevronDown'
  | 'checkCircleBroken'
  | 'bookClosed'
  | 'stars02'
  | 'playCircle'
  | 'flag06'
  | 'messageCircle01'
  | 'users01'
  | 'fileCheck02'
  | 'fileAttachment04'
  | 'bellPlus'
  | 'settings01'
  | 'pieChart03'
  | 'barChartSquare01'
  | 'logOut'
  | 'plus'
  | 'multiplePlus'
  | 'xClose'
  | 'loading2'
  | 'check'
  | 'trendUp01'
  | 'trash01'
  | 'refreshccw01'
  | 'refreshccw05'
  | 'book-closed'
  | 'tash'
  | 'edit'
  | 'searchLg'
  | 'infoCicle'
  | 'building07'
  | 'signal02'
  | 'calendar'
  | 'messageDotsSquare'
  | 'eye'
  | 'wifiOf'
  | 'bell01'
  | 'flash'
  | 'flashOf'
  | 'hourglass'
  | 'user01'
  | 'telemetry'
  | 'freeMarket'
  | 'solarSubscription'
  | 'dataHub'
  | 'renewableEnergyCharging'
  | 'chargingBatteryCable1'
  | 'monitorFlash'
  | 'renewableEnergySolarElectricity'
  | 'renewableEnergySolarMonitor'
  | 'monetizationApprove'
  | 'businessDealCash2'
  | 'recyclingRefresh'
  | 'pollutionCo2'
  | 'layoutPushRight'
  | 'layoutPushLeft'
  | 'arrowRight'
  | 'barLineChart'
  | 'list'
  | 'receiptCheck'
  | 'percent02'
  | 'homeLine'
  | 'chevronRight'
  | 'intersectSquare'
  | 'dots-vertical'
  | 'download-01'
  | 'arrow-up'
  | 'arrow-down'
  | 'minus'
  | 'ellipsisVertical'
  | 'menu'
  | 'pin'
  | 'calendar2'

type IconProps = {
  fill?: string
  width?: string
  height?: string
  viewBox?: string
  stroke?: string
  strokeWidth?: string
  icon: IconName
  className?: string
  strokeLinecap?: 'round' | 'inherit' | 'square' | 'butt'
  strokeLinejoin?: 'round' | 'inherit' | 'miter' | 'bevel'
}

export const Icon: React.FC<IconProps> = ({
  stroke = '#737373',
  strokeWidth = '1.5',
  fill = 'none',
  width = '24',
  height = '24',
  viewBox = '0 0 24 24',
  icon,
  className = '',
  ...rest
}) => {
  return (
    <svg
      width={width}
      height={height}
      fill={fill}
      viewBox={viewBox}
      className={`stroke-[${stroke}] ${className}`}
    >
      <path
        id="Icon"
        d={iconPathMap[icon] || iconPathMap.file}
        strokeWidth={strokeWidth}
        {...rest}
      />
    </svg>
  )
}
