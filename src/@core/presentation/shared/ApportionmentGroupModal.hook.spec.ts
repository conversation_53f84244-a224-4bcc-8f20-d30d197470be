import { waitFor } from '@testing-library/react'

import { apportionmentGroupEquipmentResponseMock1 } from '@/__mock__/content/api-apportionments-groups-equipments.content'
import { renderHookWithRedux } from '@/utils/setupTest'

import {
  formDataOutput,
  useLanguageModal,
  useMethodsModal
} from './ApportionmentGroupModal.hook'

jest.mock('@/@core/infra/api/ApportionmentsGroupsApiV3')

const spyApportionmentsGroupsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsGroupsApiV3'),
  'apportionmentsGroupsApiV3'
)

jest.mock('@/@core/infra/api/ApportionmentsGroupsEquipmentsApiV3')

const spyApportionmentsGroupsEquipmentsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsGroupsEquipmentsApiV3'),
  'apportionmentsGroupsEquipmentsApiV3'
)

describe('src/@core/presentation/shared/ApportionmentGroupModal.hook | usePageLanguage', () => {
  it('check de modal title', () => {
    const { result } = renderHookWithRedux(() => useLanguageModal())

    expect(result.current.titleNew).toBe('Create group')
  })
})

describe('src/@core/presentation/shared/ApportionmentGroupModal.hook | useStatePage', () => {
  it('should exec method onSubmit', async () => {
    const payload: {
      id: number | null
      name: string
      apportionmentId: number
    } = {
      id: null,
      name: 'Group',
      apportionmentId: 190
    }

    const { result } = renderHookWithRedux(() => ({
      method: useMethodsModal()
    }))

    let status: boolean | undefined

    /** request error */
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method
        .onSubmit({ ...payload, id: null })
        .catch((response) => (status = response.status))
    })
    expect(status).toBeFalsy()

    /** create request success */
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 201,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method
        .onSubmit({ ...payload, id: null })
        .then((response) => (status = response.status))
    })
    expect(status).toBeTruthy()

    /** update request success */
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 201,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method
        .onSubmit({ ...payload, id: 1 })
        .then((response) => (status = response.status))
    })
    expect(status).toBeTruthy()
  })

  it('should exec method refineEquipments', async () => {
    const formValues = {
      id: 1,
      name: 'Group',
      equipments: [
        {
          id: 1,
          name: 'Group 1',
          apportionmentGroupEquipmentId: 11
        },
        {
          id: 3,
          name: 'Group 3',
          apportionmentGroupEquipmentId: 13
        }
      ]
    }
    const targetValues = [
      {
        id: 1,
        name: 'Group 1'
      },
      {
        id: 2,
        name: 'Group 2'
      }
    ]

    const { result } = renderHookWithRedux(() => ({
      method: useMethodsModal()
    }))

    let resultEquipmentsEmpty = null

    /** execut error */
    spyApportionmentsGroupsEquipmentsApiV3.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({})
    }))

    await waitFor(async () => {
      resultEquipmentsEmpty = await result.current.method.refineEquipments(
        { ...formValues, id: null },
        targetValues
      )
    })
    expect(resultEquipmentsEmpty).toEqual([
      { id: 1, name: 'Group 1' },
      { id: 2, name: 'Group 2' }
    ])

    /** execut success */
    spyApportionmentsGroupsEquipmentsApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: apportionmentGroupEquipmentResponseMock1
      }),
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    await waitFor(async () => {
      resultEquipmentsEmpty = await result.current.method.refineEquipments(
        { ...formValues, id: 1 },
        targetValues
      )
    })
    expect(resultEquipmentsEmpty).toEqual([
      { id: 1, name: 'Group 1' },
      { id: 2, name: 'Group 2', apportionmentGroupEquipmentId: 120 }
    ])
  })

  it('should exec method formDataOutput', async () => {
    const input = {
      id: 4,
      name: 'Rateio 4',
      apportionmentId: 190
    }
    const output = {
      id: 4,
      name: 'Rateio 4',
      apportionmentId: 190
    }

    const result = formDataOutput(input)

    expect(result).toEqual(output)
  })
})
