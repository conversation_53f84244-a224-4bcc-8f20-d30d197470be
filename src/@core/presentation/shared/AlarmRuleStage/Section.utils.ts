import { IAlarmsConditionals } from '@/@core/domain/AlarmsConditionals'
import { IAlarmsRules } from '@/@core/domain/AlarmsRules'
import {
  IConditional,
  IConditionalCreate,
  IFormDataValues,
  IFormPayload
} from './Section.types'

export const getPropertyTypeByRuleId = (id: number) => {
  if (id === 1) return 'boolean'
  if (id === 3) return 'string'
  return 'string'
}

export const parseConditionalData = (
  data: IAlarmsConditionals
): IConditional => {
  return {
    alarmId: data.alarm.id,
    alarmStageId: data.alarmStageId,
    _id: new Date().getTime(),
    id: data.id,
    value: data.value,
    rule: { id: data.rule.id, name: data.rule.name },
    ruleType: null,
    operator: data.operator,
    property: data.property,
    processed: data.processed
  }
}

export const formConditionalDataInput = (
  values: IConditional,
  { ruleType, ...rule }: IAlarmsRules
): IFormDataValues => {
  return {
    _id: values._id,
    id: values.id,
    rule: { id: rule.id, name: rule.name },
    ruleType,
    processed: values.processed
      ? { id: values.processed.id, name: values.processed.name }
      : null,
    property: values.property,
    propertyType: getPropertyTypeByRuleId(rule.id),
    operator: values.operator,
    value: values.value
  }
}

export const formConditionalDataOutput = (
  values: IFormDataValues
): IConditionalCreate => {
  return {
    rule: values.rule,
    property: values.property,
    processed: values.processed,
    operator: values.operator,
    value: values.value,
    propertyType: values.propertyType
  }
}

export const formDataOutput = (values: IFormDataValues): IFormPayload => {
  return {
    id: values.id,
    alarmRuleId: Number(values.rule?.id),
    processedId: values.processed?.id ?? null,
    propertyId: values.property?.id ?? null,
    operatorLogicId: Number(values.operator?.id),
    value: values.value
  }
}

export const formatOutputProcessed = (e: unknown): IConditional['processed'] =>
  e as IConditional['processed']
