import { IAlarmsConditionals } from '@/@core/domain/AlarmsConditionals'
import { alarmsRulesMock1 } from '@/__mock__/content/api-alarms-rules.content'
import {
  IConditional,
  IConditionalCreate,
  IFormDataValues,
  IFormPayload
} from './Section.types'
import {
  formatOutputProcessed,
  formConditionalDataInput,
  formConditionalDataOutput,
  formDataOutput,
  getPropertyTypeByRuleId,
  parseConditionalData
} from './Section.utils'

describe('@core/presentation/shared/AlarmRuleStage/Section.utils', () => {
  beforeAll(() => {
    jest.useFakeTimers()
  })
  afterAll(() => {
    jest.useRealTimers()
  })

  it('check return the utility function getPropertyTypeByRuleId', () => {
    expect(getPropertyTypeByRuleId(1)).toBe('boolean')
    expect(getPropertyTypeByRuleId(3)).toBe('string')
    expect(getPropertyTypeByRuleId(4)).toBe('string')
  })

  it('check return the utility function parseConditionalData', () => {
    jest.setSystemTime(new Date('2023-12-15T12:00:00Z'))

    const dataInput: IAlarmsConditionals = {
      alarm: { id: 144, name: 'alarm 144' },
      alarmStageId: 2,
      id: 320,
      value: '44',
      rule: { id: 7, name: 'Medição Instantânea' },
      operator: { id: 3, name: 'Menor' },
      property: { id: 11, name: 'property 11' },
      processed: {
        id: 8,
        name: 'Ativação mensal de dispositivo',
        autoNormalized: false
      }
    }
    const dataOutput: IConditional = {
      _id: 1702641600000,
      alarmId: 144,
      alarmStageId: 2,
      id: 320,
      operator: { id: 3, name: 'Menor' },
      processed: {
        id: 8,
        name: 'Ativação mensal de dispositivo',
        autoNormalized: false
      },
      property: { id: 11, name: 'property 11' },
      rule: { id: 7, name: 'Medição Instantânea' },
      ruleType: null,
      value: '44'
    }
    const result = parseConditionalData(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('check return the utility function formConditionalDataInput', () => {
    /** execut full data */
    const dataInput: IConditional = {
      _id: 1702641600000,
      alarmId: 144,
      alarmStageId: 2,
      id: 320,
      operator: { id: 3, name: 'Menor' },
      processed: {
        id: 8,
        name: 'Ativação mensal de dispositivo',
        autoNormalized: false
      },
      property: { id: 11, name: 'property 11' },
      rule: { id: 7, name: 'Medição Instantânea' },
      ruleType: null,
      value: '44'
    }
    const dataOutput: IFormDataValues = {
      _id: 1702641600000,
      id: 320,
      rule: { id: 1, name: 'Equipamento desconectado' },
      ruleType: {
        requiredProcessed: false,
        requiredProperty: false
      },
      processed: {
        id: 8,
        name: 'Ativação mensal de dispositivo'
      },
      property: { id: 11, name: 'property 11' },
      propertyType: 'boolean',
      operator: { id: 3, name: 'Menor' },
      value: '44'
    }
    const rule = { ...alarmsRulesMock1 }

    expect(formConditionalDataInput(dataInput, rule)).toEqual(dataOutput)

    /** execut partial data */
    dataInput.processed = null
    dataOutput.processed = null
    expect(formConditionalDataInput(dataInput, rule)).toEqual(dataOutput)
  })

  it('check return the utility function formConditionalDataOutput', () => {
    const dataInput: IFormDataValues = {
      _id: 1702641600000,
      id: 320,
      rule: { id: 1, name: 'Equipamento desconectado' },
      ruleType: {
        requiredProcessed: false,
        requiredProperty: false
      },
      processed: {
        id: 8,
        name: 'Ativação mensal de dispositivo'
      },
      property: { id: 11, name: 'property 11' },
      propertyType: 'boolean',
      operator: { id: 3, name: 'Menor' },
      value: '44'
    }
    const dataOutput: IConditionalCreate = {
      rule: { id: 1, name: 'Equipamento desconectado' },
      processed: { id: 8, name: 'Ativação mensal de dispositivo' },
      property: { id: 11, name: 'property 11' },
      propertyType: 'boolean',
      operator: { id: 3, name: 'Menor' },
      value: '44'
    }
    expect(formConditionalDataOutput(dataInput)).toEqual(dataOutput)
  })

  it('check return the utility function formDataOutput', () => {
    const dataInput: IFormDataValues = {
      _id: 1702641600000,
      id: 320,
      rule: { id: 1, name: 'Equipamento desconectado' },
      ruleType: {
        requiredProcessed: false,
        requiredProperty: false
      },
      processed: {
        id: 8,
        name: 'Ativação mensal de dispositivo'
      },
      property: { id: 11, name: 'property 11' },
      propertyType: 'boolean',
      operator: { id: 3, name: 'Menor' },
      value: '44'
    }
    const dataOutput: IFormPayload = {
      id: 320,
      alarmRuleId: 1,
      processedId: 8,
      propertyId: 11,
      operatorLogicId: 3,
      value: '44'
    }
    expect(formDataOutput(dataInput)).toEqual(dataOutput)

    dataInput.processed = null
    dataInput.property = null
    dataOutput.processedId = null
    dataOutput.propertyId = null
    expect(formDataOutput(dataInput)).toEqual(dataOutput)
  })

  it('check return the utility function formatOutputProcessed', () => {
    const dataInput = {
      id: '1',
      name: 'company 1'
    }
    const dataOutput = {
      id: '1',
      name: 'company 1'
    }
    expect(formatOutputProcessed(dataInput)).toEqual(dataOutput)
  })
})
