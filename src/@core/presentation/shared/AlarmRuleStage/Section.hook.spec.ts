import '@/__mock__/logging/logger'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsConditionalsMock1 } from '@/__mock__/content/api-alarms-conditionals.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { renderHook, waitFor } from '@testing-library/react'
import { act } from 'react'
import {
  useLanguageSection,
  useMethodsSection,
  useStateSection
} from './Section.hook'
import { IConditionalCreate, IFormPayload } from './Section.types'
import { parseConditionalData } from './Section.utils'

jest.mock('@/@core/infra/api/AlarmsConditionalsApiV4/AlarmsConditionalsApiV4')
const spyAlarmsConditionalsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsConditionalsApiV4/AlarmsConditionalsApiV4'),
  'alarmsConditionalsApiV4'
)

describe('src/@core/presentation/shared/AlarmRuleStage/Section.hook | useLanguageSection', () => {
  it('check the title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageSection()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.form.input).toEqual({
      rule: 'Regras',
      processed: 'Dados processados',
      property: 'Propriedades',
      operator: 'Lógica',
      value: 'Valor'
    })
  })
})

describe('src/@core/presentation/shared/AlarmRuleStage/Section.hook | useStateSection', () => {
  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateSection()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        itemsConditionalNormalization: [],
        itemsConditionalTriggering: []
      })
    })

    expect(result.current.state.itemsConditionalNormalization).toHaveLength(0)
    expect(result.current.state.itemsConditionalTriggering).toHaveLength(0)
  })
})

describe('src/@core/presentation/shared/AlarmRuleStage/Section.hook | useMethodsSection', () => {
  const methodParams: { alarmId: number; alarmStageId: 1 | 2 } = {
    alarmId: 1,
    alarmStageId: 1
  }

  beforeEach(() => {
    methodParams.alarmStageId = 1
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  it('should check return the function fetchData', async () => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date('2023-12-15T12:00:00Z'))

    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsSection({ ...methodParams }),
        state: useStateSection()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    await act(() => result.current.method.reset())

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })

    /* request successful without data **/
    await act(() => result.current.method.reset())

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.itemsConditionalTriggering).toHaveLength(0)

    /* request success **/
    await act(() => result.current.method.reset())

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsConditionalsMock1]
        }
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.itemsConditionalTriggering[0]).toEqual(
      parseConditionalData(alarmsConditionalsMock1)
    )
  })

  it('should check the function handleDelete', async () => {
    methodParams.alarmStageId = 2

    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsSection({ ...methodParams }),
        state: useStateSection()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error unauthenticated **/
    await act(() => {
      result.current.toast.reset()
      result.current.method.reset()
    })

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 403
      })
    }))
    await act(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.state.itemsConditionalNormalization).toHaveLength(0)
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover condicional'
    )

    /* request error **/
    await act(() => {
      result.current.toast.reset()
      result.current.method.reset()
    })

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await act(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.state.itemsConditionalNormalization).toHaveLength(0)
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover condicional'
    )

    /* request success **/
    await act(() => {
      result.current.toast.reset()
      result.current.method.reset()
    })
    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      }),
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: { items: [alarmsConditionalsMock1] }
      })
    }))
    await act(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.state.itemsConditionalNormalization).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].type).toBe('success')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Condicional removida com sucesso'
    )
  })

  it('should check the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsSection({ ...methodParams }),
        state: useStateSection()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload: IFormPayload = {
      id: null,
      alarmRuleId: 1,
      processedId: 2,
      propertyId: null,
      operatorLogicId: 3,
      value: '5'
    }

    let resultRequest: undefined | boolean

    /* request CREATE error **/
    await act(() => result.current.method.reset())

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method
        .handleSubmit({ ...payload })
        .then(({ status }) => {
          resultRequest = status
        })
    })
    expect(resultRequest).toBeFalsy()

    /* request CREATE success **/
    await act(() => result.current.method.reset())

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 201,
        data: { ...alarmsConditionalsMock1 }
      })
    }))
    await act(async () => {
      await result.current.method
        .handleSubmit({ ...payload })
        .then(({ status }) => {
          resultRequest = status
        })
    })
    expect(resultRequest).toBeTruthy()

    /* request CREATE success **/
    await act(() => result.current.method.reset())

    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 201,
        data: { ...alarmsConditionalsMock1 }
      })
    }))
    await act(async () => {
      await result.current.method
        .handleSubmit({ ...payload, id: 123 })
        .then(({ status }) => {
          resultRequest = status
        })
    })
    expect(resultRequest).toBeTruthy()
  })

  it('should interact with local CRUD methods', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsSection({ ...methodParams }),
        state: useStateSection()
      }),
      { wrapper: AppStoreProvider }
    )

    jest.useFakeTimers()

    const currentConditional: IConditionalCreate = {
      rule: null,
      processed: null,
      property: null,
      operator: { id: 1, name: 'MAIOR QUE' },
      value: '11',
      propertyType: 'integer'
    }

    /* ADD conditional **/
    await waitFor(() => {
      jest.setSystemTime(new Date('2025-01-20T12:00:00Z'))
      result.current.method.addConditional({ ...currentConditional })
    })
    await waitFor(() => {
      jest.setSystemTime(new Date('2025-01-20T13:00:00Z'))
      result.current.method.addConditional({ ...currentConditional })
    })

    expect(result.current.state.itemsConditionalTriggering).toHaveLength(2)
    expect(result.current.state.itemsConditionalTriggering[0]._id).toBe(
      1737374400000
    )
    expect(result.current.state.itemsConditionalTriggering[1]._id).toBe(
      1737378000000
    )

    /* HAS conditional **/
    const hasConditionals = await waitFor(() =>
      result.current.method.hasConditionals()
    )
    expect(hasConditionals).toBeTruthy()

    /* UPDATE conditional **/
    await waitFor(() =>
      result.current.method.updateConditional(1737374400000, {
        ...currentConditional,
        value: '22'
      })
    )
    expect(result.current.state.itemsConditionalTriggering[0].value).toBe('22')

    /* DELETE conditional **/
    await waitFor(() => result.current.method.deleteConditional(1737374400000))
    expect(result.current.state.itemsConditionalTriggering).toHaveLength(1)
  })
})
