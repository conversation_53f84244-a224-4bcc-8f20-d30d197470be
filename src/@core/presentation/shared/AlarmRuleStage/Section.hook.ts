import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsConditionalsApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'
import { useMemo } from 'react'
import { create } from 'zustand'
import {
  IConditional,
  IConditionalCreate,
  IFormPayload,
  IState,
  IStateData
} from './Section.types'
import { parseConditionalData } from './Section.utils'

const stateData: IStateData = {
  itemsConditionalNormalization: [],
  itemsConditionalTriggering: []
}
export const useStateSection = create<IState>((set) => ({
  ...stateData,
  set: (values) => {
    set((state) => ({ ...state, ...values }))
  },
  reset: () => {
    set((state) => ({ ...state, ...stateData }))
  }
}))

export const useMethodsSection = ({
  alarmId,
  alarmStageId
}: {
  alarmId: number
  alarmStageId: 1 | 2
}) => {
  const stateSection = useStateSection()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const languageSection = useLanguageSection()

  const stageKey = useMemo(() => {
    return alarmStageId === 1
      ? 'itemsConditionalTriggering'
      : 'itemsConditionalNormalization'
  }, [alarmStageId])

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const { status, data } = await alarmsConditionalsApiV4(http).get({
        alarmId,
        alarmStageId
      })

      if (status !== 200) return

      stateSection.set({ [stageKey]: data.items.map(parseConditionalData) })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/shared/AlarmRuleStage/Section/useMethodsSection/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const handleSubmit = async ({ id, ...payload }: IFormPayload) => {
    const toast = {
      message: '',
      type: 'error' as 'success' | 'error'
    }

    try {
      systemLoading.setLoading(true)

      const { status, data } = id
        ? await alarmsConditionalsApiV4(http).update(id, {
            ...payload,
            alarmId,
            alarmStageId
          })
        : await alarmsConditionalsApiV4(http).create({
            ...payload,
            alarmId,
            alarmStageId
          })

      const conditionalRequest = [200, 201].includes(status)

      toast.type = 'success'
      toast.message = toastMessageSwitch(
        languageSection.form.messages,
        id,
        conditionalRequest
      )

      return { status: conditionalRequest, data: parseConditionalData(data) }
    } catch (error) {
      toast.type = 'error'
      toast.message = toastMessageSwitch(languageSection.form.messages, id)

      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/shared/AlarmRuleStage/Section/useMethodsSection/fetchData'
      })

      return { status: false, data: null }
    } finally {
      systemLoading.setLoading(false)

      systemToast.addToast({
        message: toast.message,
        type: toast.type
      })
    }
  }

  const handleDelete = async (id: number) => {
    const toast = {
      message: '',
      type: 'error' as 'success' | 'error'
    }

    const { messages } = languageSection.formDelete

    try {
      systemLoading.setLoading(true)

      const { status } = await alarmsConditionalsApiV4(http).delete(id)

      const conditionalRequest = status === 204

      toast.type = toastTypeSwitch(conditionalRequest)
      toast.message = toastRequestMessageSwitch(messages, conditionalRequest)

      if (!conditionalRequest) return

      const { data } = await alarmsConditionalsApiV4(http).get({
        alarmId,
        alarmStageId
      })
      stateSection.set({ [stageKey]: data.items })
    } catch (error) {
      toast.type = 'error'
      toast.message = toastRequestMessageSwitch(messages)

      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/shared/AlarmRuleStage/Section/useMethodsSection/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)

      systemToast.addToast({
        message: toast.message,
        type: toast.type
      })
    }
  }

  const reset = () => {
    stateSection.set({
      [stageKey]: []
    })
  }

  const hasConditionals = () => {
    return stateSection[stageKey].length > 0
  }
  const addConditional = (currentConditional: IConditionalCreate) => {
    const newConditional: IConditional = {
      _id: new Date().getTime(),
      alarmId,
      alarmStageId,
      id: null,
      value: currentConditional.value,
      rule: currentConditional.rule,
      ruleType: null,
      operator: currentConditional.operator,
      property: currentConditional.property,
      processed: currentConditional.processed
    }

    stateSection.set({
      [stageKey]: [...stateSection[stageKey], newConditional]
    })
  }
  const updateConditional = (
    _id: number,
    currentConditional: IConditionalCreate
  ) => {
    stateSection.set({
      [stageKey]: [...stateSection[stageKey]].map((conditional) =>
        conditional._id === _id
          ? {
              ...conditional,
              ...currentConditional
            }
          : conditional
      )
    })
  }
  const deleteConditional = (_id: number) => {
    stateSection.set({
      [stageKey]: [...stateSection[stageKey]].filter(
        (conditional) => conditional._id !== _id
      )
    })
  }

  return {
    reset,
    hasConditionals,
    addConditional,
    updateConditional,
    deleteConditional,
    fetchData,
    handleSubmit,
    handleDelete
  }
}
export const useLanguageSection = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, btn, components } = languageByMode(lang)
  const { cancel, save, add, clean } = btn
  const { requiredField } = validationFields

  const { form, formDelete, triggering, normalization } =
    components.SectionRuleTarget

  return {
    triggering,
    normalization,
    form: {
      input: {
        rule: form.input.rule,
        processed: form.input.processed,
        property: form.input.property,
        operator: form.input.operator,
        value: form.input.value
      },
      messages: form.messages,
      messageAlert: form.messageAlert,
      requiredField,
      requiredConditionalTriggering: form.requiredConditionalTriggering,
      requiredConditionalNormalization: form.requiredConditionalNormalization,
      btn: { cancel, save, add, clean }
    },
    formDelete: formDelete
  }
}
