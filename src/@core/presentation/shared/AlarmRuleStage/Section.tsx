import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef
} from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { IAlarmsOperatorLogic } from '@/@core/domain/AlarmsOperatorLogic'
import { IAlarmsRules } from '@/@core/domain/AlarmsRules'
import IProperty from '@/@core/domain/Property'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useListAlarmsOperatorLogic from '@/@core/framework/store/hook/useListAlarmsOperatorLogic'
import useListAlarmsRules from '@/@core/framework/store/hook/useListAlarmsRules'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { processedDataApiV3, propertiesApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import {
  formatInputValue,
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'

import { PageSection } from '../pages'
import {
  useLanguageSection,
  useMethodsSection,
  useStateSection
} from './Section.hook'
import { IConditional, IFormDataValues, IOptionProperty } from './Section.types'
import {
  formatOutputProcessed,
  formConditionalDataInput,
  formConditionalDataOutput,
  formDataOutput,
  getPropertyTypeByRuleId
} from './Section.utils'

const LOGICAL_OPERATORS_NOT_ALLOWED = [1, 2, 3, 4]

export interface ISectionRef {
  resetForm?: () => void
  resetConditionals?: () => void
  validateConditionals?: () => void
}
interface ISectionProps {
  title: string
  titleList: string
  alarmStageId: 1 | 2
  dataEntityId: number | null | undefined
  alarmId?: number | null | undefined
  isEdit?: boolean
  disabled?: boolean
  onCheckBeforeSubmit?: (status: boolean) => void
}

const AlarmRuleStage = forwardRef<ISectionRef, ISectionProps>((props, ref) => {
  const { title, titleList, alarmId, alarmStageId, dataEntityId, disabled } =
    props

  const isMounted = useRef<boolean>(false)

  const system = useSystemStore()
  const systemToast = useSystemToastStore()
  const systemLoading = useSystemLoadingStore()
  const log = useLog()

  const stateSection = useStateSection()
  const methodsSection = useMethodsSection({
    alarmId: Number(alarmId),
    alarmStageId
  })
  const languageSection = useLanguageSection()

  const listAlarmsOperatorLogic = useListAlarmsOperatorLogic()
  const listAlarmsRules = useListAlarmsRules()

  const formFields = useFormFields()

  const checkBeforeSubmit = () => {
    const { itemsConditionalNormalization, itemsConditionalTriggering } =
      stateSection

    let listConditionals: IConditional[] = []

    itemsConditionalTriggering.forEach((el) => listConditionals.push(el))
    itemsConditionalNormalization.forEach((el) => listConditionals.push(el))

    if (formFields.values.id)
      listConditionals = listConditionals.filter(
        (el) => el.id !== formFields.values.id
      )

    let currentOperatorId: number[] = []

    if ([1, 2].includes(Number(formFields.values.operator?.id))) {
      currentOperatorId = [1, 2]
    } else if ([3, 4].includes(Number(formFields.values.operator?.id))) {
      currentOperatorId = [3, 4]
    }

    let status = false

    listConditionals.forEach((el) => {
      if (
        !!formFields.values.property?.id &&
        Number(el.rule?.id) === Number(formFields.values.rule?.id) &&
        Number(el.property?.id) === Number(formFields.values.property?.id) &&
        currentOperatorId.includes(Number(el.operator?.id))
      ) {
        status = true
      } else if (
        !formFields.values.property?.id &&
        Number(el.rule?.id) === Number(formFields.values.rule?.id) &&
        currentOperatorId.includes(Number(el.operator?.id))
      ) {
        status = true
      }
    })

    props?.onCheckBeforeSubmit?.(!status)

    return status
  }
  const handleBeforeSubmit = () => {
    systemToast.addToast({
      message: languageSection.form.messageAlert,
      type: 'error'
    })
  }

  const onSubmit = async () => {
    if (checkBeforeSubmit()) {
      handleBeforeSubmit()
      return
    }

    const conditional = formConditionalDataOutput(formFields.values)

    const currentId = Number(formFields.values._id)

    if (!props.isEdit) {
      currentId
        ? methodsSection.updateConditional(currentId, conditional)
        : methodsSection.addConditional(conditional)

      formFields.reset()
      return
    }

    try {
      const { status, data } = await methodsSection.handleSubmit(
        formDataOutput(formFields.values)
      )

      if (!status && !data) return

      methodsSection.updateConditional(currentId, {
        operator: data!.operator,
        processed: data!.processed,
        property: data!.property,
        propertyType: data!.rule!.name,
        rule: data!.rule,
        value: data!.value
      })

      formFields.reset()

      await methodsSection.fetchData()
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/shared/AlarmRuleStage/Section/useMethodsSection/onSubmit'
      })
    }
  }
  const handleInputRule = (alarmsRules: IAlarmsRules) => {
    const { ruleType } = alarmsRules

    formFields.setValues({
      rule: {
        id: +alarmsRules.id,
        name: alarmsRules.name
      },
      ruleType,
      property: null,
      propertyType: getPropertyTypeByRuleId(+alarmsRules.id),
      processed: null,
      operator: null
    })
  }
  const handleEditConditional = (conditional: IConditional) => {
    const rule = listAlarmsRules.state.list.find(
      (el) => el.id === conditional.rule?.id
    ) as IAlarmsRules

    formFields.setValues(formConditionalDataInput(conditional, rule))
  }

  useImperativeHandle(ref, () => ({
    resetForm: () => {
      formFields.reset()
    },
    resetConditionals: () => {
      methodsSection.reset()
    },
    validateConditionals: () => {
      const hasConditionals = methodsSection.hasConditionals()

      if (!hasConditionals)
        formFields.setError('rule.id', { message: 'CAMPO OBRIGATÓRIO' })

      return hasConditionals
    }
  }))

  useEffect(() => {
    const _keyComponente = `component-SectionRuleStage[${alarmStageId}]`

    if (
      props.isEdit &&
      !isMounted.current &&
      !system.state.mountComponent?.[_keyComponente]
    ) {
      const handler = () => {
        if (!alarmId && !dataEntityId) return

        methodsSection.fetchData()
      }
      handler()

      system.setMountComponent(_keyComponente)
    }

    return () => {
      isMounted.current = true
      formFields.reset()
    }
  }, [alarmId, dataEntityId])

  const logicOptions = useMemo<IAlarmsOperatorLogic[]>(() => {
    const { itemsConditionalNormalization, itemsConditionalTriggering } =
      stateSection

    let items: IAlarmsOperatorLogic[] = JSON.parse(
      JSON.stringify(listAlarmsOperatorLogic.state.list)
    )

    if (formFields.values.id) return items

    const { rule, property } = formFields.values

    const filterPropertyId = !!property?.id

    let listConditionals: IConditional[] = []

    const removeIdsList: number[] = []

    Array.from([
      ...itemsConditionalNormalization,
      ...itemsConditionalTriggering
    ])
      .filter((conditional) => conditional.id !== formFields.values.id)
      .forEach((conditional) => listConditionals.push(conditional))

    listConditionals.forEach((conditional) => {
      if (
        !LOGICAL_OPERATORS_NOT_ALLOWED.includes(
          Number(conditional.operator?.id)
        )
      ) {
        return
      }
      if (
        filterPropertyId &&
        Number(conditional.rule?.id) === Number(rule?.id) &&
        Number(conditional.property?.id) === Number(property.id)
      ) {
        removeIdsList.push(Number(conditional.operator?.id))
        return
      }
      if (
        !filterPropertyId &&
        Number(conditional.rule?.id) === Number(rule?.id)
      ) {
        removeIdsList.push(Number(conditional.operator?.id))
      }
    })

    const hasRemoveIdsList = removeIdsList.length

    if (
      (hasRemoveIdsList && removeIdsList.includes(1)) ||
      removeIdsList.includes(2)
    ) {
      items = items.filter((e) => ![1, 2].includes(e.id))
    }

    if (
      (hasRemoveIdsList && removeIdsList.includes(3)) ||
      removeIdsList.includes(4)
    ) {
      items = items.filter((e) => ![3, 4].includes(e.id))
    }

    return items
  }, [stateSection, formFields.values, listAlarmsOperatorLogic.state.list])

  const isAutoNormalizacao = useMemo<boolean>(() => {
    if (alarmStageId === 1) return false

    const { itemsConditionalNormalization, itemsConditionalTriggering } =
      stateSection

    const findNormalized = [
      ...itemsConditionalNormalization,
      ...itemsConditionalTriggering
    ].filter((conditional) => conditional.processed?.autoNormalized)

    return !!findNormalized.length
  }, [stateSection, alarmStageId])

  const isDisabled = useMemo<boolean>(() => {
    return (
      isAutoNormalizacao ||
      disabled ||
      !dataEntityId ||
      systemLoading.state.loading
    )
  }, [disabled, dataEntityId, systemLoading.state.loading, isAutoNormalizacao])

  return (
    <>
      <PageSection.Root className="mb-2">
        <PageSection.Content title={title} />
      </PageSection.Root>

      <form
        id={`form-alarmRuleStage-${alarmStageId}`}
        className="form-container mb-4"
        onSubmit={formFields.handleSubmit(onSubmit)}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <TagInput.Root>
            <TagInput.Label htmlFor={`rule-${alarmStageId}`}>
              {languageSection.form.input.rule}
            </TagInput.Label>
            <TagInput.Content
              key={formFields.values.rule?.id}
              name={`rule-${alarmStageId}`}
              value={
                formFields.values.rule?.id
                  ? String(formFields.values.rule?.id)
                  : ''
              }
              onChange={(values) => {
                const items = formatOutputValues(
                  values
                ) as unknown[] as IAlarmsRules[]

                handleInputRule(items[0])
              }}
              options={listAlarmsRules.state.list.map(formatInputValue)}
              disabled={
                disabled || !dataEntityId || systemLoading.state.loading
              }
              helperText={formFields.errors.rule?.message}
            />
          </TagInput.Root>

          <div>
            {formFields.values.ruleType?.requiredProcessed && (
              <TagInput.Root>
                <TagInput.Label htmlFor={`processed-${alarmStageId}`}>
                  {languageSection.form.input.processed}
                </TagInput.Label>
                <TagInput.ContentApi
                  key={formFields.values.processed?.id}
                  name={`processed-${alarmStageId}`}
                  value={
                    formFields.values.processed
                      ? formatInputValues([formFields.values.processed])
                      : []
                  }
                  onChange={(values) => {
                    const [item] = formatOutputValues(values)

                    formFields.setValue(
                      'processed',
                      formatOutputProcessed(item)
                      // formatOutputValues(values)?.[0]
                    )
                  }}
                  featchData={(p) =>
                    processedDataApiV3(http).get({
                      ...p,
                      order: 'asc',
                      sort: 'name',
                      dataEntityId: Number(dataEntityId)
                    })
                  }
                  disabled={isDisabled}
                  helperText={formFields.errors.processed?.message}
                />
              </TagInput.Root>
            )}

            {formFields.values.ruleType?.requiredProperty && (
              <TagInput.Root>
                <TagInput.Label htmlFor={`property-${alarmStageId}`}>
                  {languageSection.form.input.property}
                </TagInput.Label>
                <TagInput.ContentApi
                  key={formFields.values.property?.id}
                  name={`property-${alarmStageId}`}
                  value={
                    formFields.values.property
                      ? formatInputValues([formFields.values.property])
                      : []
                  }
                  onChange={(values) => {
                    const [item] = formatOutputValues(
                      values
                    ) as IOptionProperty[]

                    formFields.setValues({
                      property: {
                        id: item.id,
                        name: item.name
                      },
                      propertyType: item.type
                    })
                  }}
                  featchData={(p) =>
                    propertiesApiV3(http).get({
                      ...p,
                      order: 'asc',
                      sort: 'nome',
                      limit: 15
                    })
                  }
                  featchDataItem={(e) => {
                    const item = e as IProperty
                    return {
                      value: String(item.id),
                      name: item.name,
                      label: item.name,
                      type: item.type
                    }
                  }}
                  disabled={isDisabled}
                  helperText={formFields.errors.property?.message}
                />
              </TagInput.Root>
            )}
          </div>

          <TagInput.Root>
            <TagInput.Label htmlFor={`operator-${alarmStageId}`}>
              {languageSection.form.input.operator}
            </TagInput.Label>
            <TagInput.Content
              key={formFields.values.operator?.id}
              name={`operator-${alarmStageId}`}
              value={
                formFields.values.operator?.id
                  ? String(formFields.values.operator?.id)
                  : ''
              }
              onChange={(values) => {
                const items = formatOutputValues(values)
                formFields.setValue('operator', {
                  id: Number(items[0].id),
                  name: items[0].name
                })
              }}
              options={logicOptions.map(formatInputValue)}
              disabled={isDisabled}
              helperText={formFields.errors.operator?.message}
            />
          </TagInput.Root>

          <div>
            <Input.Root>
              <Input.Label htmlFor={`value-${alarmStageId}`}>
                {languageSection.form.input.value}
              </Input.Label>

              {formFields.values.propertyType === 'integer' && (
                <Input.Content
                  type="number"
                  step="1"
                  id={`value-${alarmStageId}-integer`}
                  value={formFields.values.value}
                  onChange={(e) => {
                    formFields.setValue('value', e.target.value)
                  }}
                  disabled={isDisabled}
                  helperText={formFields.errors.value?.message}
                />
              )}

              {formFields.values.propertyType === 'float' && (
                <Input.Content
                  type="number"
                  step="0.000001"
                  id={`value-${alarmStageId}-float`}
                  value={formFields.values.value}
                  onChange={(e) => {
                    formFields.setValue('value', e.target.value)
                  }}
                  disabled={isDisabled}
                  helperText={formFields.errors.value?.message}
                />
              )}

              {formFields.values.propertyType === 'string' && (
                <Input.Content
                  type="text"
                  id={`value-${alarmStageId}-string`}
                  value={formFields.values.value}
                  onChange={(e) => {
                    formFields.setValue('value', e.target.value)
                  }}
                  disabled={isDisabled}
                  helperText={formFields.errors.value?.message}
                />
              )}

              {formFields.values.propertyType === 'boolean' && (
                <TagInput.Content
                  name={`value-${alarmStageId}-boolean`}
                  value={formFields.values.value}
                  onChange={(values) => {
                    const items = formatOutputValues(values)
                    formFields.setValue('value', String(items[0]?.id ?? ''))
                  }}
                  options={[
                    { id: '1', name: 'Verdadeiro (1)' },
                    { id: '0', name: 'Falso (0)' }
                  ].map(formatInputValue)}
                  disabled={isDisabled}
                  helperText={formFields.errors.rule?.message}
                  className="w-full"
                />
              )}

              {formFields.values.propertyType === 'array' && (
                <Input.Content
                  type="number"
                  step="1"
                  id={`value-${alarmStageId}`}
                  value={formFields.values.value}
                  onChange={(e) => {
                    formFields.setValue('value', e.target.value)
                  }}
                  disabled={isDisabled}
                  helperText={formFields.errors.value?.message}
                  placeholder="sem suporte"
                />
              )}
            </Input.Root>
          </div>
        </div>

        <span className="block mt-4 font-bold text-primary">{titleList}</span>

        {alarmStageId === 1 && (
          <ConditionalList
            isEdit={props.isEdit}
            alarmStageId={alarmStageId}
            conditionals={stateSection.itemsConditionalTriggering}
            handleEdit={handleEditConditional}
            handleDelete={methodsSection.handleDelete}
            handleDeleteState={methodsSection.deleteConditional}
            messageWarning={
              !!formFields.errors.rule?.id?.message
                ? languageSection.form.requiredConditionalTriggering
                : ''
            }
          />
        )}
        {alarmStageId === 2 && (
          <ConditionalList
            isEdit={props.isEdit}
            alarmStageId={alarmStageId}
            conditionals={stateSection.itemsConditionalNormalization}
            handleEdit={handleEditConditional}
            handleDelete={methodsSection.handleDelete}
            handleDeleteState={methodsSection.deleteConditional}
            messageWarning={
              !!formFields.errors.rule?.id?.message
                ? languageSection.form.requiredConditionalNormalization
                : ''
            }
          />
        )}

        <div className="footer-form">
          <Button
            type="button"
            onClick={() => formFields.reset()}
            disabled={isDisabled}
          >
            {formFields.values.id
              ? languageSection.form.btn.clean
              : languageSection.form.btn.cancel}
          </Button>
          <Button
            form={`form-alarmRuleStage-${alarmStageId}`}
            variant="primary"
            disabled={isDisabled}
          >
            {formFields.values.id
              ? languageSection.form.btn.save
              : languageSection.form.btn.add}
          </Button>
        </div>
      </form>
    </>
  )
})

const ConditionalList = ({
  alarmStageId,
  messageWarning,
  isEdit,
  disabled,
  conditionals,
  handleEdit,
  handleDelete,
  handleDeleteState
}: {
  alarmStageId?: number
  messageWarning?: string
  isEdit?: boolean
  disabled?: boolean
  conditionals: IConditional[]
  handleEdit: (p: IConditional) => void
  handleDelete: (_id: number) => void
  handleDeleteState: (_id: number) => void
}) => {
  return (
    <>
      {messageWarning && (
        <span className="block my-2 text-[13px] leading-6 font-normal text-comerc-error-400">
          {messageWarning}
        </span>
      )}

      <div className="flex gap-2 my-2">
        {conditionals.map((conditional, i) => (
          <div key={`${alarmStageId}-${i}`} className="flex border-2">
            <button
              type="button"
              className={cn('px-3 py-1', {
                'cursor-not-allowed': disabled
              })}
              onClick={() => handleEdit(conditional)}
              disabled={disabled}
            >
              {conditional.rule?.name}
            </button>
            <button
              type="button"
              className={cn('border-l-2 px-3 py-1', {
                'cursor-not-allowed': disabled
              })}
              onClick={() => {
                isEdit
                  ? handleDelete(Number(conditional.id))
                  : handleDeleteState(conditional._id)
              }}
              disabled={disabled}
            >
              X
            </button>
          </div>
        ))}
      </div>
    </>
  )
}

export default AlarmRuleStage

const useFormFields = () => {
  const {
    form: { requiredField }
  } = useLanguageSection()

  const formSchema = z
    .object({
      _id: z.number().nullable(),
      id: z.number().nullable(),
      rule: z
        .object(
          {
            id: z.number(),
            name: z.string()
          },
          { message: requiredField }
        )
        .nullable(),
      ruleType: z
        .object({
          requiredProperty: z.boolean(),
          requiredProcessed: z.boolean()
        })
        .nullable(),
      property: z.object({ id: z.number(), name: z.string() }).nullable(),
      propertyType: z.string(),
      processed: z
        .object({
          id: z.number(),
          name: z.string()
        })
        .nullable(),
      operator: z
        .object(
          {
            id: z.number(),
            name: z.string()
          },
          { message: requiredField }
        )
        .nullable(),
      value: z.string().min(1, { message: requiredField })
    })
    .superRefine((values, ctx) => {
      if (!values.rule) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: requiredField,
          path: ['rule']
        })
      }
    })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (data: Partial<IFormDataValues>): FormSchema => ({
    _id: data?._id ?? null,
    id: data?.id ?? null,
    rule: data?.rule ?? null,
    ruleType: data?.ruleType ?? null,
    property: data?.property ?? null,
    processed: data?.processed ?? null,
    operator: data?.operator ?? null,
    value: data?.value ?? '',
    propertyType: data.propertyType ?? 'integer'
  })

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    setError,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })
  const defineValues = (dataParsed: Partial<IFormDataValues>) => {
    Object.entries(dataParsed).forEach(([key, value]) => {
      setValue(key as keyof IFormDataValues, value)
    })
  }
  const reset = () => {
    defineValues(parseInitialData({}))
  }
  const setValues = (data: Partial<FormSchema> = {}) => {
    defineValues(data)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors, reset, setError }
}
