export interface IConditional {
  alarmId: number
  alarmStageId: number
  _id: number
  id: number | null
  rule: { id: number; name: string } | null
  ruleType: { requiredProperty: boolean; requiredProcessed: boolean } | null
  property: { id: number; name: string } | null
  processed: { id: number; name: string; autoNormalized?: boolean } | null
  operator: { id: number; name: string } | null
  value: string
}
export interface IConditionalCreate {
  // alarmId: number
  // alarmStageId: number
  // _id: number
  // id: number | null
  rule: { id: number; name: string } | null
  // ruleType: { requiredProperty: boolean; requiredProcessed: boolean } | null
  property: { id: number; name: string } | null
  propertyType: string
  processed: { id: number; name: string } | null
  operator: { id: number; name: string } | null
  value: string
}

export interface IStateData {
  itemsConditionalNormalization: IConditional[]
  itemsConditionalTriggering: IConditional[]
}
export interface IState extends IStateData {
  set: (p: Partial<IStateData>) => void
  reset: () => void
}

export interface IFormDataValues {
  _id: number | null
  id: number | null
  rule: { id: number; name: string } | null
  ruleType: { requiredProperty: boolean; requiredProcessed: boolean } | null
  property: { id: number; name: string } | null
  propertyType: string
  processed: { id: number; name: string } | null
  operator: { id: number; name: string } | null
  value: string
}
export interface IFormPayload {
  id: number | null
  alarmRuleId: number
  processedId: number | null
  propertyId: number | null
  operatorLogicId: number
  value: string
}

export interface IOptionProperty {
  id: number
  name: string
  type: string
}
