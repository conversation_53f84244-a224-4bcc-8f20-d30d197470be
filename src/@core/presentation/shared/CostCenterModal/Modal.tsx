import { forwardRef, useImperativeHandle, useRef, useState } from 'react'

import { ICostCenter } from '@/@core/domain/CostCenters'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Tabs } from '@/@core/presentation/shared/ui/tabs'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'

import { TabCustomAdditionais } from './_components/TabCustomAdditionais'
import { TabCustomTariffs } from './_components/TabCustomTariffs'
import { ITabDataRef, TabData } from './_components/TabData'
import { TabEquipments } from './_components/TabEquipments'
import { useLanguageModal } from './Modal.hooks'
import {
  ICostCenterData,
  ICostCenterModalProps,
  ICostCenterModalRef,
  IModalData
} from './Modal.types'

export const CostCenterModal = forwardRef<
  ICostCenterModalRef,
  ICostCenterModalProps
>((props, ref) => {
  const modalRef = useRef<IModalRootRef>(null)

  const tabDataRef = useRef<ITabDataRef>(null)

  const [stateData, setStateValues] = useState<IModalData>({
    tab: 'data',
    costCenter: {}
  })

  const systemStore = useSystemStore()
  const languageModal = useLanguageModal()

  const setState = (values: Partial<IModalData>) => {
    setStateValues((prev) => ({ ...prev, ...values }))
  }
  const handleClose = () => {
    systemStore.setUnmountComponent('costCenter-tabData')
    systemStore.setUnmountComponent('costCenter-tabCustomTariffs')
    systemStore.setUnmountComponent('costCenter-tabEquipments')

    memory.local.reset(['costCenterModal'])

    setStateValues({ tab: 'data', costCenter: {} })
  }
  const handleTabs = ({ costCenterId }: { costCenterId: number }) => {
    memory.local.set({
      costCenterModal: {
        tabCustomAdditionais: {
          search: { costCenterId, page: 1 }
        },
        tabCustomTariffs: {
          search: { costCenterId, page: 1 }
        },
        tabEquipments: {
          search: { costCenterIds: [costCenterId], page: 1 }
        }
      }
    })
  }
  const handler = (payload: Partial<ICostCenterData>) => {
    handleTabs({ costCenterId: payload.id! })

    /** update tabData */
    tabDataRef.current?.handler({
      id: payload.id!,
      name: payload.name!,
      email: payload.email!,
      apportionmentTariffTypeId: payload.apportionmentTariffTypeId!
    })

    setStateValues({ tab: 'data', costCenter: payload })
  }
  const handleCreated = (costCenter: ICostCenter) => {
    setState({ costCenter })

    handleTabs({ costCenterId: costCenter.id })

    props.handleCreatedTabData?.()
  }

  useImperativeHandle(ref, () => ({
    open: () => modalRef.current?.open(),
    close: () => modalRef.current?.close(),
    handler: (p) => setTimeout(() => handler(p), 0)
  }))

  return (
    <Modal.Root ref={modalRef} handleClose={handleClose} size="lg6">
      <Modal.Title>
        {stateData.costCenter.id
          ? languageModal.titleEdit
          : languageModal.titleNew}
      </Modal.Title>

      <Modal.Content>
        <Tabs.Root
          value={stateData.tab}
          onValueChange={(tab) => setState({ tab })}
          className="lg:flex items-start gap-2"
        >
          <TabControl
            active={stateData.tab}
            costCenter={stateData.costCenter}
            onChangeTab={(tab) => setState({ tab })}
          />

          <Tabs.Content value="data" className="w-full form-container">
            <TabData
              ref={tabDataRef}
              btnCancel={modalRef.current?.close}
              handleCreated={handleCreated}
              costCenter={stateData.costCenter}
              apportionmentId={props.apportionmentId}
            />
          </Tabs.Content>

          <Tabs.Content value="customTariffs" className="w-full">
            <TabCustomTariffs costCenter={stateData.costCenter} />
          </Tabs.Content>

          <Tabs.Content
            value="customAdditionais"
            className="w-full form-container"
          >
            <TabCustomAdditionais costCenter={stateData.costCenter} />
          </Tabs.Content>

          <Tabs.Content value="equipments" className="w-full">
            <TabEquipments costCenter={stateData.costCenter} />
          </Tabs.Content>
        </Tabs.Root>
      </Modal.Content>
    </Modal.Root>
  )
})

const TabControl = ({
  active,
  costCenter,
  onChangeTab
}: {
  active: string
  costCenter: Partial<ICostCenterData>
  onChangeTab: (tab: string) => void
}) => {
  const isEdit = !costCenter.id

  return (
    <>
      <Tabs.List className="hidden lg:flex justify-start flex-col pr-2 mr-6 min-w-[177px] min-h-[350px]">
        <Tabs.Trigger value="data">
          <p>Informações</p>
        </Tabs.Trigger>
        <Tabs.Trigger value="customTariffs" disabled={isEdit}>
          <p>Tarifas customizadas</p>
        </Tabs.Trigger>
        <Tabs.Trigger value="customAdditionais" disabled={isEdit}>
          <p>Tarifas adicionais</p>
        </Tabs.Trigger>
        <Tabs.Trigger value="equipments" disabled={isEdit}>
          <p>Equipamentos</p>
        </Tabs.Trigger>
      </Tabs.List>

      <TagInput.Content
        className="lg:hidden"
        value={active}
        onChange={(value) => onChangeTab(value![0].value)}
        optionDisabled={
          isEdit
            ? []
            : ['data', 'customTariffs', 'customAdditionais', 'equipments']
        }
        options={[
          { label: 'Informações', value: 'data' },
          { label: 'Tarifas customizadas', value: 'customTariffs' },
          { label: 'Tarifas adicionais', value: 'customAdditionais' },
          { label: 'Equipamentos', value: 'equipments' }
        ]}
      />
    </>
  )
}
