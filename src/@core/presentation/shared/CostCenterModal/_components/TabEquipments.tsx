import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useListCostCompositionTypes from '@/@core/framework/store/hook/useListCostCompositionTypes'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { apportionmentsGroupsApiV3 } from '@/@core/infra/api/ApportionmentsGroupsApiV3'
import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import {
  formatInputValue,
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { ListCostCompositionTypesService } from '@/@core/services/listCostCompositionTypesService'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import { ICostCenterData } from '../Modal.types'
import {
  formDataOutput,
  useLanguageTabEquipmentsModal,
  useMethodsTabEquipmentsModal,
  useStateTabEquipmentsModal
} from './TabEquipments.hooks'
import {
  ITabEquipmentsModal,
  ITabEquipmentsValues
} from './TabEquipments.types'

const formValuesTabEquipmentsInitial: ITabEquipmentsValues = {
  id: null,
  equipments: [],
  costCompositionTypeId: '1',
  usageLimit: '0',
  groups: []
}

export const TabEquipments = (props: {
  costCenter: Partial<ICostCenterData>
}) => {
  const isMounted = useRef(false)

  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const stateTabModal = useStateTabEquipmentsModal()
  const methodsTabModal = useMethodsTabEquipmentsModal()
  const languageTabModal = useLanguageTabEquipmentsModal()

  const searchFields = memory.local.get().costCenterModal.tabEquipments.search
  const formFields = useTabEquipmentsFormFields()

  const listCostCompositionTypes = useListCostCompositionTypes()
  const listCostCompositionTypesService = ListCostCompositionTypesService()

  const handler = async () => {
    if (systemStore.state.mountComponent?.['costCenter-tabEquipments']) return

    await methodsTabModal.fetchData()

    systemStore.setMountComponent('costCenter-tabEquipments')
  }

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true

      listCostCompositionTypesService.handler()

      handler()
      return
    }
    return () => {
      isMounted.current = false
    }
  }, [])

  const onSubmit = async () => {
    const costCenterId = Number(props.costCenter.id)

    const { status } = await methodsTabModal.handleSubmit(
      formDataOutput({ ...formFields.values, costCenterId })
    )

    if (!status) return

    await methodsTabModal.fetchData()

    formFields.reset()
  }

  return (
    <>
      <div className="form-container my-4">
        <form
          className="grid lg:grid-cols-3 gap-2"
          id="form-tabCustomTariffs"
          onSubmit={formFields.handleSubmit(onSubmit)}
        >
          <TagInput.Root>
            <TagInput.Label htmlFor="equipments">
              {languageTabModal.form.input.equipment}
            </TagInput.Label>
            <TagInput.ContentApi
              name="equipments"
              value={formatInputValues(formFields.values?.equipments)}
              onChange={(values) => {
                const value = formatOutputValues(values)
                formFields.setValue('equipments', value)
              }}
              featchData={(args) =>
                equipmentsApiV4(http).get({
                  ...args,
                  companiesGroup: [Number(props.costCenter.companyId)],
                  typeId: String(props.costCenter.apportionmentTariffTypeId)
                })
              }
              helperText={formFields.errors?.equipments?.message}
              disabled={systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="costCompositionTypeId">
              {languageTabModal.form.input.costCompositionType}
            </TagInput.Label>
            <TagInput.Content
              name="costCompositionTypeId"
              value={formFields.values?.costCompositionTypeId}
              onChange={(values) => {
                formFields.setValue('costCompositionTypeId', values![0].value)
              }}
              options={listCostCompositionTypes.state.list.map(
                formatInputValue
              )}
              helperText={formFields.errors?.equipments?.message}
              disabled={systemLoading.state.loading}
            />
          </TagInput.Root>

          {formFields.values.costCompositionTypeId === '1' && (
            <Input.Root>
              <Input.Label htmlFor="usageLimit">
                {languageTabModal.form.input.composition}
              </Input.Label>
              <Input.Content
                id="usageLimit"
                name="usageLimit"
                type="number"
                step="1"
                value={formFields.values.usageLimit}
                onChange={({ target }) => {
                  formFields.setValue('usageLimit', target.value)
                }}
                disabled={systemLoading.state.loading}
                helperText={formFields.errors.usageLimit?.message}
              />
            </Input.Root>
          )}
          {formFields.values.costCompositionTypeId === '2' && (
            <TagInput.Root>
              <TagInput.Label htmlFor="groups">
                {languageTabModal.form.input.group}
              </TagInput.Label>
              <TagInput.ContentApi
                name="groups"
                value={formatInputValues(formFields.values?.groups)}
                onChange={(values) => {
                  const value = formatOutputValues(values)
                  formFields.setValue('groups', value)
                }}
                featchData={(args) =>
                  apportionmentsGroupsApiV3(http).get({
                    ...args,
                    apportionmentId: props.costCenter.apportionmentId
                  })
                }
                helperText={formFields.errors?.equipments?.message}
                disabled={systemLoading.state.loading}
              />
            </TagInput.Root>
          )}
        </form>
        <div className="footer-form">
          <Button type="button" onClick={() => formFields.reset()}>
            {formFields.values.id
              ? languageTabModal.form.btn.cancel
              : languageTabModal.form.btn.clean}
          </Button>

          <Button type="submit" variant="primary" form="form-tabCustomTariffs">
            {formFields.values.id
              ? languageTabModal.form.btn.save
              : languageTabModal.form.btn.add}
          </Button>
        </div>
      </div>

      <Table.Root classNameWrapper="block">
        <Table.Header>
          <Table.Row>
            <Table.Head>{languageTabModal.table.columns.equipment}</Table.Head>
            <Table.Head>
              {languageTabModal.table.columns.composition}
            </Table.Head>
            <Table.Head>{languageTabModal.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {stateTabModal.table.items.map((costCenterEquipment) => (
            <Table.Row key={costCenterEquipment.id}>
              <Table.Cell>{costCenterEquipment.equipment.name}</Table.Cell>
              <Table.Cell>
                {costCenterEquipment.group?.name ??
                  `${costCenterEquipment.usageLimit}%`}
              </Table.Cell>
              <Table.Cell width={80} role="td-actions">
                <div className="flex items-center gap-2">
                  <ButtonDelete
                    costCenterEquipmentId={costCenterEquipment.id}
                    resetForm={formFields.reset}
                  />
                  <ButtonEdit
                    data={costCenterEquipment}
                    onClick={(args) => formFields.setValues(args)}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
          <Table.RowLoading status={systemLoading.state.loading} colSpan={4} />
        </Table.Body>

        <Table.Paginate
          status={systemLoading.state.loading}
          currentPage={searchFields.page}
          lastPage={stateTabModal.table.lastPage}
          handleChangePage={(page) => {
            memory.local.set({
              costCenterModal: {
                tabEquipments: { search: { page } }
              }
            })
            methodsTabModal.fetchData()
          }}
        />
      </Table.Root>
    </>
  )
}
const ButtonDelete = ({
  costCenterEquipmentId,
  resetForm
}: {
  costCenterEquipmentId: number
  resetForm: Function
}) => {
  const [open, setOpen] = useState<boolean>(false)

  const languageModal = useLanguageTabEquipmentsModal()
  const methodsTabModal = useMethodsTabEquipmentsModal()

  const handleClickConfirm = async () => {
    const { status } = await methodsTabModal.handleDelete(costCenterEquipmentId)

    if (!status) return

    await methodsTabModal.fetchData()

    setOpen(false)

    resetForm()
  }

  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger className="table-td-action hover:cursor-pointer">
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </Dialog.Trigger>
      <Dialog.Content size={'lg2'}>
        <Dialog.Header>
          <Dialog.Title>{languageModal.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>

        {languageModal.table.modalDelete.textInfo}

        <Dialog.Footer>
          <Button type="button" onClick={() => setOpen(false)}>
            {languageModal.table.modalDelete.btn.cancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languageModal.table.modalDelete.btn.confirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
const ButtonEdit = ({
  data,
  onClick
}: {
  data: ITabEquipmentsModal
  onClick: (payload: Partial<ITabEquipmentsValues>) => void
}) => {
  return (
    <button
      className="table-td-action hover:cursor-pointer"
      onClick={() => {
        onClick({
          id: data.id,
          equipments: [data.equipment],
          costCompositionTypeId: data.costCompositionType.id.toString(),
          usageLimit: data.usageLimit.toString(),
          groups: data.group ? [data.group] : []
        })
      }}
    >
      <Icon
        icon="edit"
        className="icon-menu-primary"
        height="20"
        width="20"
        viewBox="0 0 20 20"
      />
    </button>
  )
}

const useTabEquipmentsFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabEquipmentsModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z.object({
        id: z.number().nullable(),
        equipments: z
          .array(
            z.object({
              id: z.number(),
              name: z.string()
            })
          )
          .min(1, { message }),
        costCompositionTypeId: z.string().min(1, { message }),
        usageLimit: z.string(),
        groups: z.array(
          z.object({
            id: z.number(),
            name: z.string()
          })
        )
      })
    ),
    defaultValues: { ...formValuesTabEquipmentsInitial }
  })

  const values = form.watch()

  const setValues = (payload: Partial<ITabEquipmentsValues>) => {
    defineValuesFormFields(
      form.setValue,
      payload,
      formValuesTabEquipmentsInitial
    )
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
