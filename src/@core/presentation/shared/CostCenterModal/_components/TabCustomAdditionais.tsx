import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { useNumberFormatter } from '@/@core/framework/hooks/useNumberFormatter/hook'
import useListCostCompositionTypes from '@/@core/framework/store/hook/useListCostCompositionTypes'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { ListCostCompositionTypesService } from '@/@core/services/listCostCompositionTypesService'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import {
  formDataInput,
  formDataOutput,
  useLanguageTabCustomAdditionalModal,
  useMethodsTabCustomAdditionalModal,
  useStateTabCustomAdditionalModal
} from './TabCustomAdditionais.hooks'
import {
  ITabCusomAdditionaisModal,
  ITabCustomAdditionaisProps,
  ITabCustomAdditionaisValues
} from './TabCustomAdditionais.types'

export const TabCustomAdditionais = (props: ITabCustomAdditionaisProps) => {
  const isMounted = useRef(false)

  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const stateTabModal = useStateTabCustomAdditionalModal()
  const methodsTabModal = useMethodsTabCustomAdditionalModal()
  const languageTabModal = useLanguageTabCustomAdditionalModal()

  const searchFields = memory.local.get().costCenterModal.tabCustomAdditionais.search
  const formFields = useTabCustomAdditionaisFormFields()

  const listCostCompositionTypes = useListCostCompositionTypes()
  const listCostCompositionTypesService = ListCostCompositionTypesService()
  const { currencyFormat } = useNumberFormatter()

  const handler = async () => {
    // if (systemStore.state.mountComponent?.['costCenter-tabCustomAdditionais'])
    //   return

    await methodsTabModal.fetchData()

    // systemStore.setMountComponent('costCenter-tabCustomAdditionais')
  }

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true

      listCostCompositionTypesService.handler()

      handler()
      return
    }
    return () => {
      isMounted.current = false
    }
  }, [])

  const onSubmit = async () => {
    const costCenterId = Number(props.costCenter.id)

    const { status } = await methodsTabModal.handleSubmit(
      formDataOutput({ ...formFields.values, costCenterId })
    )
    if (!status) return

    await methodsTabModal.fetchData()

    formFields.reset()
  }

  return (
    <>
      <div className="form-container my-4">
        <form
          className="grid lg:grid-cols-2 gap-2"
          id="form-tabCustomTariffs"
          onSubmit={formFields?.handleSubmit?.(onSubmit)}
        >
          <Input.Root>
            <Input.Label>{languageTabModal.form.input.period}</Input.Label>
            <Input.ContentDate
              value={
                formFields.values.period
                  ? dayjs(formFields.values.period).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'period',
                  dayjs(value).startOf('month').format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.period?.message}
              disabled={systemLoading.state.modalLoading}
            >
              {formFields.values.period
                ? dayjs(formFields.values.period).format('DD/MM/YYYY')
                : ''}
            </Input.ContentDate>
          </Input.Root>

          <Input.Root>
            <Input.Label>{languageTabModal.form.input.value}</Input.Label>
            <Input.Content
              classNameInput="text-center"
              type="number"
              value={formFields.values.value}
              onChange={({ target }) =>
                formFields.setValue('value', target.value)
              }
              helperText={formFields.errors.value?.message}
              disabled={systemLoading.state.modalLoading}
            />
          </Input.Root>
        </form>

        <div className="footer-form">
          <Button type="button" onClick={() => formFields?.reset?.()}>
            {formFields.values.id
              ? languageTabModal.form.btn.cancel
              : languageTabModal.form.btn.clean}
          </Button>

          <Button type="submit" variant="primary" form="form-tabCustomTariffs">
            {formFields.values.id
              ? languageTabModal.form.btn.save
              : languageTabModal.form.btn.add}
          </Button>
        </div>
      </div>

      <Table.Root classNameWrapper="block">
        <Table.Header>
          <Table.Row>
            <Table.Head>{languageTabModal.table.columns.period}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.value}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {stateTabModal.table.items.map((customAdditional) => (
            <Table.Row key={customAdditional.id}>
              <Table.Cell>
                {dayjs(customAdditional.period).format('MM/YYYY')}
              </Table.Cell>
              <Table.Cell>
                {customAdditional.value
                  ? currencyFormat(customAdditional.value, {
                    locale: 'pt-BR',
                    currency: 'BRL'
                  })
                  : ''}
              </Table.Cell>
              <Table.Cell width={80} role="td-actions">
                <div className="flex items-center gap-2">
                  <ButtonDelete
                    customAdditionalId={customAdditional.id}
                    resetForm={formFields.reset}
                  />
                  <ButtonEdit
                    data={customAdditional}
                    onClick={formFields.setValues}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>

        <Table.Paginate
          currentPage={searchFields.page}
          lastPage={stateTabModal.table.lastPage}
          handleChangePage={(page) => {
            memory.local.set({
              costCenterModal: {
                tabCustomAdditionais: { search: { page } }
              }
            })
            methodsTabModal.fetchData()
          }}
        />
      </Table.Root>
    </>
  )
}
const ButtonDelete = ({
  customAdditionalId,
  resetForm
}: {
  customAdditionalId: number
  resetForm: Function
}) => {
  const modalRef = useRef<IModalRootRef>(null)

  const methodsTabModal = useMethodsTabCustomAdditionalModal()
  const languageTabModal = useLanguageTabCustomAdditionalModal()

  const handleClickConfirm = async () => {
    const { status } = await methodsTabModal.handleDelete(customAdditionalId)

    if (!status) return

    await methodsTabModal.fetchData()

    modalRef.current?.close()

    resetForm()
  }

  return (
    <>
      <button
        className="table-td-action hover:cursor-pointer"
        onClick={() => modalRef.current?.open()}
      >
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>

      <Modal.Root ref={modalRef} size="lg2">
        <Modal.Title>{languageTabModal.table.modalDelete.title}</Modal.Title>
        <Modal.Content>
          {languageTabModal.table.modalDelete.textInfo}
        </Modal.Content>
        <Modal.Footer>
          <Button type="button" onClick={() => modalRef.current?.close()}>
            {languageTabModal.table.modalDelete.btn.cancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languageTabModal.table.modalDelete.btn.confirm}
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
const ButtonEdit = ({
  data,
  onClick
}: {
  data: ITabCusomAdditionaisModal
  onClick: (payload: Partial<ITabCustomAdditionaisValues>) => void
}) => {
  return (
    <button
      className="table-td-action hover:cursor-pointer"
      onClick={() => {
        onClick(formDataInput(data))
      }}
    >
      <Icon
        icon="edit"
        className="icon-menu-primary"
        height="20"
        width="20"
        viewBox="0 0 20 20"
      />
    </button>
  )
}
const formValuesTabCustomAdditionaisInitial: ITabCustomAdditionaisValues = {
  id: null,
  period: '',
  value: ''
}
const useTabCustomAdditionaisFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabCustomAdditionalModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z.object({
        id: z.number().nullable(),
        period: z.string().min(1, { message }),
        value: z.string().min(1, { message })
      })
    ),
    defaultValues: { ...formValuesTabCustomAdditionaisInitial }
  })

  const values = form.watch()

  const setValues = (payload: Partial<ITabCustomAdditionaisValues>) => {
    defineValuesFormFields(
      form.setValue,
      payload,
      formValuesTabCustomAdditionaisInitial
    )
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
