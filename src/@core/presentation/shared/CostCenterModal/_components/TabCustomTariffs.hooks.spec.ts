import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import {
  costCentersTariffsMock1,
  costCentersTariffsMock2
} from '@/__mock__/content/api-cost-centers-tariffs.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  formDataOutput,
  useLanguageTabCustomTariffsModal,
  useMethodsTabCustomTariffsModal,
  useStateTabCustomTariffsModal
} from './TabCustomTariffs.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/CostCentersTariffsApiV3')

const spyCostCentersTariffsApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCentersTariffsApiV3'),
  'costCentersTariffsApiV3'
)

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomTariffs | useStateTabCustomTariffsModal', () => {
  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabCustomTariffsModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        table: {
          items: [costCentersTariffsMock1, costCentersTariffsMock2],
          lastPage: 1,
          total: 1
        }
      })
    })

    expect(result.current.state.table.items).toHaveLength(2)
    expect(result.current.state.table.lastPage).toBe(1)
    expect(result.current.state.table.total).toBe(1)

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.table.items).toHaveLength(0)
    expect(result.current.state.table.lastPage).toBe(0)
    expect(result.current.state.table.total).toBe(0)
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomTariffs | useMethodsTabCustomTariffsModal', () => {
  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabCustomTariffsModal(),
        method: useMethodsTabCustomTariffsModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* fetchData with error **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersTariffsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({ status: 500, data: null })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.table.items).toHaveLength(0)

    /* request successful without data **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersTariffsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({ status: 200, data: { items: [] } })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.table.items).toHaveLength(0)

    /* fetchData successful **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersTariffsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [costCentersTariffsMock1, costCentersTariffsMock2],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.table.items).toHaveLength(2)
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        state: useStateTabCustomTariffsModal(),
        method: useMethodsTabCustomTariffsModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    let status: boolean | undefined

    spyCostCentersTariffsApiV3.mockImplementationOnce(() => ({
      delete: jest.fn().mockRejectedValue({ status: 500 })
    }))

    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover tarifa customizada'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersTariffsApiV3.mockImplementationOnce(() => ({
      delete: jest.fn().mockResolvedValueOnce({ status: 204 })
    }))

    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Tarifa customizada removido com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        state: useStateTabCustomTariffsModal(),
        method: useMethodsTabCustomTariffsModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const payload = {
      id: null,
      costCenterId: 123,
      value: 1,
      vigencyStart: '2025-04-01',
      vigencyEnd: '2025-04-10'
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersTariffsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao cadastrar tarifa customizada'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request CREATE success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersTariffsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCentersTariffsMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Tarifa customizada adicionado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersTariffsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar tarifa customizada'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersTariffsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCentersTariffsMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Tarifa customizada atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomTariffs | useLanguageTabCustomTariffsModal', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabCustomTariffsModal()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.form.input).toEqual({
      value: 'Valor',
      vigencyEnd: 'Fim da vigência',
      vigencyStart: 'Início da vigência'
    })
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomTariffs | utils', () => {
  const dataInput = {
    id: 1,
    vigencyStart: '',
    vigencyEnd: '',
    value: '123',
    costCenterId: 1
  }
  const dataOutput = {
    id: 1,
    vigencyStart: '',
    vigencyEnd: '',
    value: 123,
    costCenterId: 1
  }

  it('should check return the function formDataOutput', () => {
    const result = formDataOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
