import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'

import { dashboardsApiV3 } from '@/@core/infra/api/DashboardsApiV3'
import { menuApiV4 } from '@/@core/infra/api/MenuApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { filterMenuActiveByMenu, renderInputData } from './page.utils'

import { IDashboardTab } from '@/@core/domain/DashboardTabs'
import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import {
  IMenuPartialValue,
  IPagePartialValue,
  IStateTestData
} from './page.types'

export const useStatePage = create<
  IStateTestData & { set: (p: Partial<IStateTestData>) => void }
>((set) => ({
  triggerRequest: '',
  menu: {
    open: false,
    items: [],
    itemActive: null,
    inputSearch: '',
    inputAccount: null
  },
  page: {
    tab: null,
    tabs: [],
    inputData: null,
    period: {
      initial: '',
      final: ''
    }
  },
  set: (payload: Partial<IStateTestData>) => {
    set((state) => ({ ...state, ...payload }))
  }
}))

export const useMethodPage = () => {
  const statePage = useStatePage()

  const handleReloadWidgets = () => {
    statePage.set({
      ...statePage,
      triggerRequest: new Date().getTime().toString()
    })
  }
  const clearMenuItems = () => {
    const values = {
      itemActive: null,
      inputSearch: '',
      inputAccount: null
    }
    memory.local.set({
      dashboard: {
        menu: { ...values },
      }
    })
    statePage.set({
      ...statePage,
      menu: { ...statePage.menu, ...values, items: [] },
      page: {
        ...statePage.page,
        inputData: null,
        tab: null,
        tabs: []
      }
    })
  }
  const handleInputSearch = (inputSearch: string) => {
    memory.local.set({
      dashboard: {
        menu: { inputSearch }
      }
    })
    statePage.set({
      ...statePage,
      menu: { ...statePage.menu, inputSearch }
    })
  }
  const handleInputAccount = async ({
    accountId,
    inputAccount
  }: {
    accountId: number
    inputAccount: { id: number; name: string }
  }) => {
    try {
      const { status, data } = await menuApiV4(http).dashboard({
        accountId: accountId
      })

      if (status !== 200) return

      const itemActive: IMenuDashboard | null = filterMenuActiveByMenu(
        data.items,
        statePage.menu.itemActive?.entityId
      )

      memory.local.set({
        dashboard: {
          menu: { inputAccount, itemActive }
        }
      })
      statePage.set({
        menu: {
          ...statePage.menu,
          inputAccount,
          items: data.items,
          itemActive: null
        },
        page: {
          ...statePage.page,
          inputData: null,
          tab: null,
          tabs: []
        }
      })

      return { reload: true }
    } catch (error) {
      console.log(error)
      return { reload: false }
    }
  }
  const handleToggleMenuMobile = () => {
    const open = !statePage.menu.open

    memory.local.set({
      dashboard: {
        menu: { open }
      }
    })
    statePage.set({
      ...statePage,
      menu: { ...statePage.menu, open }
    })
  }
  const selectMenuItem = async (
    menuItem: IMenuDashboard,
    parent?: IMenuDashboard
  ) => {
    const menuValues: IMenuPartialValue = {
      itemActive: null,
      open: true
    }
    const pageValues: IPagePartialValue = {
      tab: null,
      tabs: [],
      inputData: null
    }

    try {
      const { status, data } = await dashboardsApiV3(http).tabs({
        entityType: menuItem.entityType,
        entityId: menuItem.entityId
      })

      if (status === 200) {
        window.document.querySelector('.dashboard-tabs')?.scrollTo({
          left: 0,
          behavior: 'smooth'
        })

        menuValues.itemActive = {
          entityId: menuItem.entityId,
          entityName: menuItem.entityName,
          entityType: menuItem.entityType,
          parent: parent ?? null,
          timezone: menuItem.timezone
        }
        menuValues.open = false

        pageValues.inputData = renderInputData({
          period: statePage.page.period,
          itemActive: menuItem
        })
        pageValues.tabs = data
        pageValues.tab = data?.[0] ?? null
      }
    } catch (error) {
      console.log(error)
    } finally {
      memory.local.set({
        dashboard: {
          menu: { ...menuValues },
          page: { tab: pageValues.tab }
        }
      })
      statePage.set({
        ...statePage,
        menu: { ...statePage.menu, ...menuValues },
        page: { ...statePage.page, ...pageValues }
      })
    }
  }
  const selectTabItem = (tab: IDashboardTab) => {
    statePage.set({
      ...statePage,
      page: { ...statePage.page, tab }
    })
  }

  return {
    handleReloadWidgets,
    clearMenuItems,
    handleInputSearch,
    handleInputAccount,
    handleToggleMenuMobile,
    selectMenuItem,
    selectTabItem
  }
}

export const useLanguagePage = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages, btn, validationFields, errors } = languageByMode(lang)

  const { cancel, confirm, add } = btn

  const { request } = errors
  const { requiredField } = validationFields
  const {
    createDashboardModal,
    deleteDashboardModal,
    createAlarmModal,
    ...page
  } = pages.dashboard

  return {
    page,
    tableHistoricalAlarm: { title: pages.alarmsId.tabHistorical.title },
    createDashboardModal: {
      ...createDashboardModal,
      buttonCancel: cancel,
      buttonConfirm: confirm,
      requiredField,
      errorRequest: request
    },
    deleteDashboardModal: {
      ...deleteDashboardModal,
      buttonCancel: btn.cancel,
      buttonConfirm: btn.confirm,
      requiredField,
      errorRequest: request
    },
    createAlarmModal: {
      title: createAlarmModal.title,
      form: {
        input: {
          name: createAlarmModal.form.input.name,
          frequency: createAlarmModal.form.input.frequency,
          email: createAlarmModal.form.input.email,
          placeholderEmail: createAlarmModal.form.input.placeholderEmail
        },
        messages: createAlarmModal.form.messages,
        requiredField,
        btn: { cancel, add }
      }
    }
  }
}
