import {
  IDashboardTab,
  IDashboardTabWidgetY
} from '@/@core/domain/DashboardTabs'
import { IMenuDashboard } from '@/@core/domain/MenuDashboard'

export interface IInputData extends Record<string, unknown> {
  route: string
  date: string
  instance: string
  timezone: string
  id: number
  codigo: number
  initial_date: number
  final_date: number
}

export type IStructureBodyData = any

export type IStructureHeadData = any

export interface IStructureDynamicListCard {
  title: string
  type: string
  behavior: string
  routes: {
    config: {
      descriptionLabel: string
      descriptionValue: string
      fieldLabel: string
      fieldValue: string
      textLabel: string
      textValue: string
      unit: string
    }
    apiRoute: {
      method: string
      url: string
      params: Record<string, string>
    }
  }[]
  parseValues: 'booleanToInteger' | null
}
export interface IStructureDynamicDemand {
  title: string
  type: string
  peak: boolean
  footer: {
    id: string
    show: boolean
    title: string
    fields: {
      firstValue: string
      thirdField: string
      titleField: string
      secondValue: string
      firstValueSufix: string
      titleFieldSufix: string
      secondValueSufix: string
    }
    loading: boolean
  }
  routes: {
    method: string
    apiRoute: string
    config: {
      id: string
      last: boolean
      unit: string
      title: string
      value: string
      loading: boolean
      description: string
      dataType: string
    }
  }[]
}
export interface IStructureDynamicIntake {
  title: string
  type: string
  peak: string
  routes: {
    method: 'post' // string,
    apiRoute: string // "/api/v3/energy-consumption/{instance}"
    config: {
      description: string // 'endDate'
      peakTime: boolean // false
      title: string // ''
      unit: string // 'MWh'
      value: string // 'value'
    }
  }[]
}
export interface IStructureDynamicChart {
  title: string
  type: string
  apiRoute: { main: string; range?: string }
  chartOptions: Record<string, any>
  forNoRecords: {
    config: {
      contractedOutOfPeak: string
      contractedPeak: string
      hour: string // 'time'
      peakTime: boolean
      reactiveDemand: string
      value: string
    }
  }
  rangeTitle?: string
}
export interface IStructureDynamicWidgetHeadList {
  style?: {}
  spanTitle: string
  spanPrefix: string
  spanSuffix: string
  data: string
}
export interface IStructureDynamicChartNew {
  type: string
  apiRoutes: {
    config: string // "separate_series"
    method: string // "post"
    params: Record<string, string> & {
      accumulate: boolean // true
      aggregate: string // "sum",
      aggregation_interval: string // "days",
      aggregation_interval_number: number // 1,
      final_date: string // "last_day",
      id: Record<string, string> & {
        path: string // 'id'
      }
      initial_date: string // "first_day",
      limit: number // 100000,
      order: string // "asc",
      properties: string[] // ['consumo_agua']
    }
    postParamsForm: Record<string, string> & {
      aggregation_interval: Record<string, string> // path: 'view_date', source: 'head[1].dataStructure.values'}
      final_date: Record<string, string> // path: 'date[1]', source: 'head[2].dataStructure.values'}
      initial_date: Record<string, string> // path: 'date
    }
    url: string
    urlParams: {
      id: Record<string, string> & {
        path: string // 'id'
      }
      instance: Record<string, string> & {
        path: string // 'id'
      }
    }
  }[]
  chartOptions: Record<string, string>
}
export interface IStructureDynamicDonutCard {
  title: string
  type: string
  apiRoutes: {
    apiRoute: {
      url: string
      method: string // "post"
      params: Record<string, string> & {
        aggregate?: string // "sum",
        id: number
        order: string // "asc",
        properties: string[] // ['consumo_agua']
        summarize_by: string // "sum",
        // accumulate: boolean // true
        // aggregation_interval: string // "days",
        // aggregation_interval_number: number // 1,
        // final_date: string // "last_day",
        // initial_date: string // "first_day",
        // limit: number // 100000,
      }
    }
    content: Record<string, string> & {
      fieldLabel: string // ''
      fieldValue: string // 'value'
      textLabel: string // ''
      textValue: string // ''
      unit: string // 'kW'
    }
  }[]
  chartOptions: Record<string, string>
}
export interface IStructureDynamicChartGauge {
  apiRoutes: {
    url: string
    config: string
    method: string
    params: Record<string, string> & {
      id: number
      final_date: string
      initial_date: string
      limit: number
      order: string
      properties: string[]
    }
    postParamsForm: {
      final_date: {
        path: string
        source: string
      }
      initial_date: {
        path: string
        source: string
      }
    }
    urlParams: {
      id: number
      instance: string
    }
  }[]
  chartOptions: Record<string, string>
}
export interface IWidgetXPops extends React.HTMLAttributes<HTMLDivElement> {
  /** */
}
export interface IWidgetYPops {
  widgetData: IDashboardTabWidgetY
  inputData: IInputData
  yKey: string
  triggerRequest: string
  entityId: number
}
export interface IWidgetProps {
  dataStructure: unknown
  inputData: IInputData
  triggerRequest: string
  entityId: number
  yKey?: string
}
export interface IWidgetHubProps extends IWidgetProps {
  name: string
}

export type IMenuPartialValue = {
  itemActive: Partial<IMenuDashboard> | null
  open: boolean
}
export type IPagePartialValue = {
  tab: IDashboardTab | null
  tabs: IDashboardTab[]
  inputData: IInputData | null
}

export type IStateTestData = {
  triggerRequest: string
  menu: {
    open: boolean
    items: IMenuDashboard[]
    itemActive: Partial<IMenuDashboard> | null
    inputSearch: string
    inputAccount: { id: number; name: string } | null
  }
  page: {
    tab: IDashboardTab | null
    tabs: IDashboardTab[]
    inputData: IInputData | null
    period: { initial: string; final: string }
  }
}
