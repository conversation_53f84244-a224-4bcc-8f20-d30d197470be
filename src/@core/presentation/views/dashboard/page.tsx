import { useEffect, useRef, useState } from 'react'

import { IDashboardTab } from '@/@core/domain/DashboardTabs'
import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import { dashboardsApiV3 } from '@/@core/infra/api/DashboardsApiV3'
import { menuApiV4 } from '@/@core/infra/api/MenuApiV4'
import { http } from '@/@core/infra/http'
import { memory, memoryApp } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'

import { ButtonReloadWidgets } from './_components/ButtonReloadWidgets'
import { CreateDashboardModal } from './_components/CreateDashboardModal'
import { DashboardTabs } from './_components/DashboardTabs'
import { DashboardTitleSection } from './_components/DashboardTitleSection'
import { DeleteDashboardModal } from './_components/DeleteDashboardModal'
import { InputDateRange } from './_components/InputDateRange'
import { MenuDashboard } from './_components/MenuDashboard'
import WidgetX from './_components/WidgetX'
import WidgetY from './_components/WidgetY'
import { ContextPageProvider } from './page.context'
import { useLanguagePage, useMethodPage, useStatePage } from './page.hook'
import { IInputData } from './page.types'
import { renderInputData, renderPeriodData } from './page.utils'

export const Page = () => {
  const isMounted = useRef<boolean>(false)

  const authStore = useAuthStore()
  const log = useLog()

  const statePage = useStatePage()
  const language = useLanguagePage()

  const createDashboadRef = useRef<{ open: () => void }>(null)
  const deleteDashboadRef = useRef<{ open: () => void }>(null)
  const menuRef = useRef<HTMLDivElement>(null)

  const [menuFixed, setMenuFixed] = useState(false)
  const [isDesktopMenuOpen, setIsDesktopMenuOpen] = useState(false)

  const {
    handleReloadWidgets,
    clearMenuItems,
    handleInputSearch,
    handleInputAccount,
    handleToggleMenuMobile,
    selectMenuItem,
    selectTabItem
  } = useMethodPage()

  useBreadcrumb('dashboard')
  useTitlePage(language.page.title)

  const restoreData = async () => {
    try {
      const staticData = memory.local.get().dashboard

      const {
        inputSearch = '',
        open = false,
        itemActive = null,
        menuFixed: savedMenuFixed,
        isDesktopMenuOpen: savedIsDesktopMenuOpen
      } = staticData?.menu!
      const { tab = null, period: periodData } = staticData?.page!
      const period = renderPeriodData(periodData)

      const inputAccount = authStore.state.isSuperAdmin
        ? staticData?.menu?.inputAccount || null
        : {
            id: Number(authStore.state.me.accountId),
            name: authStore.state.me.accountName
          }

      let inputData = null

      if (authStore.state.isSuperAdmin && !inputAccount) return

      let items: IMenuDashboard[] = []
      let tabs: IDashboardTab[] = []

      await menuApiV4(http)
        .dashboard({
          accountId: Number(inputAccount?.id)
        })
        .then(({ status, data }) => {
          if (status === 200) {
            items = data.items
          }
        })

      if (itemActive) {
        await dashboardsApiV3(http)
          .tabs({
            entityType: itemActive?.entityType as string,
            entityId: itemActive?.entityId as number
          })
          .then(({ status, data }) => {
            if (status === 200) {
              tabs = data
            }
          })

        inputData = renderInputData({ period, itemActive })
      }

      if (typeof savedMenuFixed === 'boolean') setMenuFixed(savedMenuFixed)
      if (typeof savedIsDesktopMenuOpen === 'boolean')
        setIsDesktopMenuOpen(savedIsDesktopMenuOpen)

      memory.local.set({
        dashboard: {
          menu: { ...staticData?.menu!, inputAccount }
        }
      })
      statePage.set({
        ...statePage,
        menu: {
          open,
          itemActive,
          items,
          inputSearch,
          inputAccount
        },
        page: { tab, tabs, period, inputData }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'restoreData - @core/presentation/views/dashboard/page.tsx'
      })
      memoryApp.down()
    }
  }

  useEffect(() => {
    if (menuFixed) return

    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsDesktopMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDesktopMenuOpen, menuFixed])

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      restoreData()
      return
    }

    return () => {
      isMounted.current = false
    }
  }, [])

  useEffect(() => {
    memory.local.set({
      dashboard: {
        menu: {
          ...statePage.menu,
          menuFixed,
          isDesktopMenuOpen
        }
      }
    })
  }, [menuFixed, isDesktopMenuOpen])

  /** HOOK-METHODS */
  const openCreateDashboardModal = () => {
    createDashboadRef.current?.open()
    handleToggleMenuMobile()
  }
  const openDeleteDashboardModal = () => {
    deleteDashboadRef.current?.open()
    handleToggleMenuMobile()
  }

  return (
    <PageContent id="page-dashboard" className="desktopSm:relative">
      <ContextPageProvider
        value={{
          handleReloadWidgets,
          clearMenuItems,
          handleInputSearch,
          handleInputAccount,
          handleToggleMenuMobile,
          openCreateDashboardModal,
          openDeleteDashboardModal,
          selectMenuItem,
          selectTabItem
        }}
      >
        {/* MENU */}
        <div ref={menuRef} className="laptopLg:sticky laptopLg:top-0 z-30 flex">
          <MenuDashboard
            menuFixed={menuFixed}
            setMenuFixed={setMenuFixed}
            menuFixedOpen={isDesktopMenuOpen}
          />
        </div>

        <div
          className={
            menuFixed && isDesktopMenuOpen
              ? 'laptopLg:ml-[400px] transition-all duration-200'
              : ''
          }
        >
          <div className="flex flex-wrap gap-3 mt-2 mb-[32px] m-auto items-start">
            <div className="flex-auto">
              <DashboardTitleSection
                setIsDesktopMenuOpen={setIsDesktopMenuOpen}
                handleToggleMenuMobile={handleToggleMenuMobile}
              />
            </div>

            <div className="flex flex-1 items-center justify-end gap-3">
              {!!statePage.page.inputData?.id && (
                <>
                  <InputDateRange />
                  <ButtonReloadWidgets />
                </>
              )}
            </div>
          </div>

          <DashboardTabs />

          {statePage.page.tab?.xWidgets?.map((row) => (
            <WidgetX key={row.key} className="border-transparent">
              <div className="dashboard-widgets">
                {row?.yWidgets?.map((widget) => (
                  <WidgetY
                    key={widget.key}
                    yKey={widget.key}
                    widgetData={widget}
                    inputData={statePage.page.inputData as IInputData}
                    triggerRequest={statePage.triggerRequest}
                    entityId={Number(statePage.menu.itemActive?.entityId)}
                  />
                ))}
              </div>
            </WidgetX>
          ))}

          {/* MODAL */}
          <CreateDashboardModal ref={createDashboadRef} />
          <DeleteDashboardModal ref={deleteDashboadRef} />
        </div>
      </ContextPageProvider>
    </PageContent>
  )
}

export const Debug = ({
  title,
  data,
  className,
  status: initialStatus = false
}: {
  title: string
  data: unknown
  status?: boolean
  className?: string
}) => {
  const [status, setStatus] = useState(initialStatus)

  if (!process.env.NEXT_PUBLIC_WIDGETS) return null

  return (
    <div className="border-2 border-comerc-tertiary-800 shadow-md mb-5">
      <button
        className="mb-2 block bg-comerc-grayDark-50 w-full text-left py-3"
        onClick={() => setStatus((prev) => !prev)}
      >
        {title}
      </button>
      <pre
        className={cn(
          `duration-200 h-0 overflow-x-hidden overflow-y-scroll px-2 border`,
          { 'h-[400px]': status },
          className
        )}
      >
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  )
}
