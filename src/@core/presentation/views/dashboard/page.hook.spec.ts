import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { act, renderHook } from '@testing-library/react'

import { memory, memoryApp } from '@/@core/infra/memory'
import { dashboardTabMock1 } from '@/__mock__/content/api-dashboard.content'
import { menuDashboardMock1 } from '@/__mock__/content/api-menu-dashbaord.content'

import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import { useLanguagePage, useMethodPage, useStatePage } from './page.hook'

jest.mock('@/@core/infra/api/MenuApiV4/MenuApiV4')
const spyMenuApiV4 = jest.spyOn(
  require('@/@core/infra/api/MenuApiV4/MenuApiV4'),
  'menuApiV4'
)
jest.mock('@/@core/infra/api/DashboardsApiV3/DashboardsApiV3')
const spyDashboardsApiV3 = jest.spyOn(
  require('@/@core/infra/api/DashboardsApiV3/DashboardsApiV3'),
  'dashboardsApiV3'
)

describe('src/@core/presentation/views/dashboard/page.hook', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  it('shoud check data after execut method clearMenuItems', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await act(async () => {
      const values = {
        itemActive: null,
        inputSearch: 'DOC88',
        inputAccount: { id: 119, name: 'DOC88' }
      }

      memory.local.set({
        dashboard: {
          ...memory.local.get().dashboard,
          menu: { ...memory.local.get().dashboard?.menu!, ...values }
        }
      })
      result.current.state.set({
        ...result.current.state,
        menu: { ...result.current.state.menu, ...values }
      })
    })

    expect(memory.local.get().dashboard?.menu?.inputSearch).toBe('DOC88')
    expect(memory.local.get().dashboard?.menu?.inputAccount).toEqual({
      id: 119,
      name: 'DOC88'
    })
    expect(result.current.state.menu?.inputSearch).toBe('DOC88')
    expect(result.current.state.menu?.inputAccount).toEqual({
      id: 119,
      name: 'DOC88'
    })

    await act(async () => {
      result.current.method.clearMenuItems()
    })

    // Test that inputSearch remains in memory, but inputAccount is stored as an object, not a string
    expect(memory.local.get().dashboard?.menu?.inputSearch).toBe('DOC88')
    expect(memory.local.get().dashboard?.menu?.inputAccount).toEqual({
      id: 119,
      name: 'DOC88'
    })
    expect(result.current.state.menu?.inputSearch).toBe('')
    expect(result.current.state.menu?.inputAccount).toBeNull()

    // Additional test: clearMenuItems should reset menu.items and page.tabs
    expect(result.current.state.menu?.items).toEqual([])
    expect(result.current.state.page?.tabs).toEqual([])

    // Additional test: clearMenuItems should reset itemActive and page.tab
    expect(result.current.state.menu?.itemActive).toBeNull()
    expect(result.current.state.page?.tab).toBeNull()

    // Additional test: clearMenuItems should reset inputAccount in state and memory
    expect(result.current.state.menu?.inputAccount).toBeNull()
    expect(memory.local.get().dashboard?.menu?.inputAccount).toEqual({
      id: 119,
      name: 'DOC88'
    })

    // Test that calling clearMenuItems again does not throw and keeps state clean
    await act(async () => {
      result.current.method.clearMenuItems()
    })
    expect(result.current.state.menu?.inputSearch).toBe('')
    expect(result.current.state.menu?.inputAccount).toBeNull()
    expect(result.current.state.menu?.items).toEqual([])
    expect(result.current.state.menu?.itemActive).toBeNull()
    expect(result.current.state.page?.tab).toBeNull()
    expect(result.current.state.page?.tabs).toEqual([])

    // Test that memory still has inputSearch after multiple clears
    expect(memory.local.get().dashboard?.menu?.inputSearch).toBe('DOC88')

    // Test that memory inputAccount is still the same object
    expect(memory.local.get().dashboard?.menu?.inputAccount).toEqual({
      id: 119,
      name: 'DOC88'
    })

    // Test that inputAccount is not a stringified object
    expect(typeof memory.local.get().dashboard?.menu?.inputAccount).toBe(
      'object'
    )

    // Test that inputAccount is not null in memory, but is null in state
    expect(memory.local.get().dashboard?.menu?.inputAccount).toEqual({
      id: 119,
      name: 'DOC88'
    })
    expect(result.current.state.menu?.inputAccount).toBeNull()
    expect(memory.local.get().dashboard?.menu?.inputSearch).toBe('DOC88')
    expect(result.current.state.menu?.inputSearch).toBe('')

    act(() => {
      result.current.method.handleInputSearch('DOC88')
    })

    expect(memory.local.get().dashboard?.menu?.inputSearch).toBe('DOC88')
    expect(result.current.state.menu?.inputSearch).toBe('DOC88')
  })
  it('shoud check data afterexecut method handleInputAccount', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /** execut method width error request */
    spyMenuApiV4.mockImplementation(() => ({
      dashboard: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await act(async () => {
      result.current.method.handleInputAccount({
        accountId: 119,
        inputAccount: { id: 119, name: 'DOC88' }
      })
    })
    expect(result.current.state.menu.items).toHaveLength(0)

    /** execut method width success request */
    spyMenuApiV4.mockImplementation(() => ({
      dashboard: jest.fn().mockResolvedValue({
        status: 200,
        data: { items: [menuDashboardMock1] }
      })
    }))

    await act(async () => {
      result.current.method.handleInputAccount({
        accountId: 119,
        inputAccount: { id: 119, name: 'DOC88' }
      })
    })
    expect(result.current.state.menu.items).toHaveLength(1)
  })
  it('execut method handleToggleMenu', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    expect(memory.local.get().dashboard?.menu?.open).toBeFalsy()
    expect(result.current.state.menu?.open).toBeFalsy()

    await act(async () => {
      result.current.method.handleToggleMenuMobile()
    })

    expect(memory.local.get().dashboard?.menu?.open).toBeTruthy()
    expect(result.current.state.menu?.open).toBeTruthy()
  })
  it('execut method selectMenuItem width request data', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const menuItem: IMenuDashboard = { ...menuDashboardMock1 }

    /** execut method width error request */
    spyDashboardsApiV3.mockImplementation(() => ({
      tabs: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await act(async () => {
      result.current.method.selectMenuItem(menuItem)
    })

    expect(result.current.state.page.inputData).toBeNull()
    expect(result.current.state.page.tab).toBeNull()
    expect(result.current.state.page.tabs).toHaveLength(0)

    /** execut method width success request and no data */
    spyDashboardsApiV3.mockImplementation(() => ({
      tabs: jest.fn().mockResolvedValue({
        status: 200,
        data: []
      })
    }))

    await act(async () => {
      result.current.method.selectMenuItem(menuItem)
    })

    expect(result.current.state.page.inputData).not.toBeNull()
    expect(result.current.state.page.tab).toBeNull()
    expect(result.current.state.page.tabs).toHaveLength(0)

    /** execut method width success  */
    spyDashboardsApiV3.mockImplementation(() => ({
      tabs: jest.fn().mockResolvedValue({
        status: 200,
        data: [dashboardTabMock1]
      })
    }))

    await act(async () => {
      result.current.method.selectMenuItem(menuItem)
    })

    expect(result.current.state.page.inputData).not.toBeNull()
    expect(result.current.state.page.tab).not.toBeNull()
    expect(result.current.state.page.tabs).toHaveLength(1)
  })

  it('execut method selectTabItem width request data', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /** reset values */
    await act(async () => {
      result.current.state.set({
        ...result.current.state,
        page: { ...result.current.state.page, tab: null }
      })
    })

    expect(result.current.state.page.tab).toBeNull()

    await act(async () => {
      result.current.method.selectTabItem(dashboardTabMock1)
    })

    expect(result.current.state.page.tab).not.toBeNull()
  })
})

describe('useLanguagePage', () => {
  it('check de page title', () => {
    const { result } = renderHook(() => useLanguagePage(), {
      wrapper: AppStoreProvider
    })

    expect(result.current).toEqual({
      tableHistoricalAlarm: {
        title: 'Histórico de alarmes'
      },
      createDashboardModal: {
        buttonCancel: 'Cancelar',
        buttonConfirm: 'Confirmar',
        errorRequest: 'Ocorreu um erro, tente novamente mais tarde',
        inputAccount: 'Conta',
        inputEntity: 'Criar dashboard para:',
        inputReplace: 'Substituir',
        inputReplaceInfo:
          'Caso esta opção seja selecionada, as dashboards antigas serão substituídas. Caso contrário, a dashboard atual será combinada com as dashboards já existentes.',
        inputSearch: 'Pesquisar',
        inputTemplate: 'Template',
        requiredField: 'Campo obrigatório',
        submitMessages: {
          errorMessage: 'Erro ao criar dashboard',
          successMessage: 'Dashboard criada com sucesso'
        },
        title: 'Criar dashboard'
      },
      deleteDashboardModal: {
        buttonCancel: 'Cancelar',
        buttonConfirm: 'Confirmar',
        errorRequest: 'Ocorreu um erro, tente novamente mais tarde',
        inputAccount: 'Conta',
        inputSearch: 'Pesquisar',
        requiredField: 'Campo obrigatório',
        submitMessages: {
          errorMessage: 'Erro ao deletar dashboard',
          successMessage: 'Dashboard deletado com sucesso'
        },
        title: 'Excluir dashboard'
      },
      createAlarmModal: {
        form: {
          btn: {
            add: 'Adicionar',
            cancel: 'Cancelar'
          },
          input: {
            email: 'E-mail',
            frequency: 'Frequencia',
            name: 'Nome',
            placeholderEmail: 'Digite e pressione Enter para adicionar'
          },
          messages: {
            errorMessage: 'Erro ao criar alarme',
            successMessage: 'Alarme criado com sucesso'
          },
          requiredField: 'Campo obrigatório'
        },
        title: 'Criar Alarme'
      },
      page: { title: 'Dashboard' }
    })
  })
})
