import DashboardWidgetDynamicAlarmes from './DashboardWidgetDynamicAlarmes'
import DashboardWidgetDynamic<PERSON>larmsNew from './DashboardWidgetDynamicAlarmsNew'
import DashboardWidgetDynamicChart from './DashboardWidgetDynamic<PERSON>hart'
import DashboardWidgetDynamicChartGauge from './DashboardWidgetDynamicChartGauge'
import DashboardWidgetDynamicChartNew from './DashboardWidgetDynamicChartNew'
import DashboardWidgetDynamicDefault from './DashboardWidgetDynamicDefault'
import DashboardWidgetDynamicDemand from './DashboardWidgetDynamicDemand'
import DashboardWidgetDynamicDonutCard from './DashboardWidgetDynamicDonutCard'
import DashboardWidgetDynamicIntake from './DashboardWidgetDynamicIntake'
import DashboardWidgetDynamicListCard from './DashboardWidgetDynamicListCard'

import { IWidgetHubProps } from '../page.types'

export default function WidgetHub(props: IWidgetHubProps) {
  const { name, ...rest } = props

  switch (name) {
    case 'DynamicListCard':
      return <DashboardWidgetDynamicListCard {...rest} />
    case 'DynamicIntake':
      return <DashboardWidgetDynamicIntake {...rest} />
    case 'DynamicDemand':
      return <DashboardWidgetDynamicDemand {...rest} />
    case 'DynamicChart':
      return <DashboardWidgetDynamicChart {...rest} />
    case 'DynamicChartNew':
      return <DashboardWidgetDynamicChartNew {...rest} />
    case 'DynamicChartGauge':
      return <DashboardWidgetDynamicChartGauge {...rest} />
    case 'DynamicDonutCard':
      return <DashboardWidgetDynamicDonutCard {...rest} />
    case 'AlarmsNew':
      return <DashboardWidgetDynamicAlarmsNew {...rest} />
    case 'Alarmes':
      return <DashboardWidgetDynamicAlarmes {...rest} />
    case 'DynamicTableNew':
      return (
        <DashboardWidgetDynamicDefault
          name="DynamicTableNew"
          label="Componente em desenvolvimento"
        />
      )
    case 'DynamicMap':
      return (
        <DashboardWidgetDynamicDefault
          name="DynamicMap"
          label="Componente em desenvolvimento"
        />
      )
    default:
      return <DashboardWidgetDynamicDefault name={name} />
  }
}
