import { useEffect, useState } from 'react'

import { IDashboardTabStructure } from '@/@core/domain/DashboardTabs'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicChartGaugeService from '@/utils/dashboard/DynamicChartGaugeService'
import DynamicChartNewService from '@/utils/dashboard/DynamicChartNewService'
import DynamicChartService from '@/utils/dashboard/DynamicChartService'
import DynamicDemandService from '@/utils/dashboard/DynamicDemandService'
import DynamicDonutCardService from '@/utils/dashboard/DynamicDonutCardService'
import DynamicIntakeService from '@/utils/dashboard/DynamicIntakeService'
import DynamicListCardService from '@/utils/dashboard/DynamicListCardService'
import getValueByString from '@/utils/dashboard/getValueByString'
import transformObjectToQueryString from '@/utils/dashboard/transformObjectToQueryString'

import { useStatePage } from '../page.hook'
import {
  IInputData,
  IStructureBodyData,
  IStructureHeadData,
  IWidgetYPops
} from '../page.types'
import WidgetHub from './WidgetHub'
import {
  DynamicWidgetHeadContainer,
  DynamicWidgetHeadItem
} from './WidgetY.parts'

type ILocalData = {
  isProcessing: boolean
  error: unknown
  dataStructure: IDashboardTabStructure
}
export default function WidgetY({
  widgetData,
  inputData,
  yKey,
  triggerRequest,
  entityId
}: IWidgetYPops) {
  const statePage = useStatePage()

  const [localData, setData] = useState<ILocalData>({
    isProcessing: true,
    error: null as unknown,
    dataStructure: {
      body: [],
      head: [],
      dataProcessing: false
    }
  })

  async function handler() {
    try {
      await new Promise((res) => setTimeout(res, 100))

      let currentStructure: IDashboardTabStructure = JSON.parse(
        JSON.stringify(widgetData.dataStructure)
      )

      const _inputData = JSON.parse(JSON.stringify(inputData))
      const _body = [...currentStructure.body]
      const _head = [...currentStructure.head]
      const _options = { widgetData: { ...currentStructure } }

      currentStructure.head = _head
      currentStructure.body = ReadyDynamicBody(
        _inputData,
        _body,
        _head,
        _options
      )

      setData((prev) => ({
        ...prev,
        isProcessing: false,
        dataStructure: currentStructure
      }))
    } catch (error) {
      console.log('... WidgetY error:', error)

      setData((prev) => ({ ...prev, error: error as unknown }))
    }
  }

  const dynamicEvent = (value: { headIndex: number; field: any }) => {
    try {
      /** atualiza o dataStructure.head no localData */
      const { headIndex, field } = value

      let currentStructure: IDashboardTabStructure = JSON.parse(
        JSON.stringify(localData.dataStructure)
      )

      let { head } = { ...localData.dataStructure }

      /** atribui o novo VALUE dentro do dataStructure no array head com base no index onde houve interação (headIndex) */
      head[headIndex].dataStructure!.values![field.key] = field.value

      /** força o useEffect entender que houve mudanças no head */
      head = JSON.parse(JSON.stringify(head))

      const _inputData = JSON.parse(JSON.stringify(inputData))
      const _body = [...currentStructure?.body]
      const _head = head
      const _options = { widgetData: { ...currentStructure } }

      currentStructure.head = _head
      currentStructure.body = ReadyDynamicBody(
        _inputData,
        _body,
        _head,
        _options
      )

      currentStructure.body[0].triggerRender =
        !currentStructure.body[0].triggerRender

      setData((p) => ({
        ...p,
        dataStructure: currentStructure
      }))

      /** atualiza o dataStructure.head no statePage.page.tabs */
      const tabs = [...statePage.page.tabs]
        .map(({ ...tab }) => tab)
        .map((tab) => {
          tab.xWidgets = tab.xWidgets.map((xWidget) => {
            xWidget.yWidgets = xWidget.yWidgets.map((yWidget) => {
              return yWidget.key === yKey
                ? { ...yWidget, dataStructure: currentStructure }
                : { ...yWidget }
            })
            return xWidget
          })
          return tab
        })
      statePage.set({
        page: {
          ...statePage.page,
          tabs
        }
      })
    } catch (error) {
      console.log(' ... dynamicEvent error', error)
    }
  }

  useEffect(() => {
    handler()
  }, [widgetData.triggerByHeadField])

  return (
    <div className="dashboard-widget">
      {localData.isProcessing ? (
        <Skeleton />
      ) : (
        <>
          <DynamicWidgetHeadContainer
            hasHead={!!localData.dataStructure.head?.length}
          >
            {localData.dataStructure.head.map((structureHead, headIndex) => (
              <DynamicWidgetHeadItem
                key={`headItem-[${yKey}].${headIndex}`}
                headIndex={headIndex}
                dynamicEvent={dynamicEvent}
                {...structureHead}
              />
            ))}
          </DynamicWidgetHeadContainer>

          {localData.dataStructure.body.map((widget, widgetIndex) => {
            return (
              <WidgetHub
                key={`widgetHub-[${yKey}].${widgetIndex}`}
                name={widget.name}
                dataStructure={widget.dataStructure}
                inputData={inputData}
                triggerRequest={`${triggerRequest}-${widget.triggerRender}`}
                entityId={entityId}
                yKey={yKey}
              />
            )
          })}
        </>
      )}
    </div>
  )
}

const formats = {
  array: (e: any) => [e]
}
type IRoute = {
  config: string
  method: string
  params: Record<string, any>
  postParamsForm: Record<string, any>
  serieskey: string
  url: string
  urlParams: Record<string, any>
  yAxis: 0
}
export function ReadyDynamicBody(
  inputData: IInputData,
  body: IStructureBodyData[],
  head: IStructureHeadData[],
  options: { widgetData?: any }
) {
  function getParametersValue(route: IRoute) {
    let currentData: IRoute = { ...route }

    Object.keys(route.params).forEach((key) => {
      let result
      let checkParams

      checkParams = [
        !!route.params?.[key],
        !!route.params?.[key],
        !!route.params?.[key]?.path
      ].every(Boolean)

      if (checkParams) {
        const o = inputData
        const s = route.params[key].path

        result = getValueByString(o, s) as IRoute
      }

      checkParams = [
        !!route.postParamsForm,
        !!route.postParamsForm?.[key]?.source,
        !!route.postParamsForm?.[key]?.path
      ].every(Boolean)

      if (checkParams) {
        const o = { head }
        const s = `${route.postParamsForm[key].source}.${route.postParamsForm[key].path}`

        result = getValueByString(o, s) as IRoute

        if (!result && !!route.postParamsForm?.[key]?.default) {
          result = route.postParamsForm[key].default
        }
      }

      if (!result) return

      currentData.params[key] = result ?? route.params[key]
    })

    return currentData
  }
  function getUrlParameters(route: IRoute, { dataStructure }: any = {}) {
    const currentRoute = { ...route } as any

    const newRoute = currentRoute.url ? 'url' : 'apiRoute'
    const allParams: Record<string, any> = {}

    Object.keys(currentRoute.urlParams).forEach((item) => {
      if (currentRoute.urlParams[item]?.path) {
        const value = item
        const path = currentRoute.urlParams[item].path

        if (currentRoute.urlParams[item]?.source) {
          const rule = new RegExp(
            `(${currentRoute.urlParams[item].path}=[^&]+)`,
            'g'
          )

          let data = getValueByString(
            dataStructure,
            `${currentRoute.urlParams[item].source}.${currentRoute.urlParams[item].path}`
          )

          if (rule.test(currentRoute[newRoute])) {
            currentRoute[newRoute] = currentRoute[newRoute].replace(
              rule,
              data || currentRoute.urlParams[item].default
                ? `${path}=${data || currentRoute.urlParams[item].default}`
                : ''
            )
          } else if (data) {
            currentRoute[newRoute] = currentRoute[newRoute].split('?')[0]

            const format =
              (formats as { [key: string]: (data: any) => any })?.[
                currentRoute.urlParams?.[item]?.ruleFormat
              ] || false

            data = typeof data === 'object' ? Math.round(data / 1000) : data

            allParams[item] = format ? format(data) : data
          }
        } else {
          currentRoute.urlParams[item] = getValueByString(inputData, path)
          currentRoute[newRoute] = currentRoute[newRoute].replace(
            `{${value}}`,
            route.urlParams[item]
          )
        }
      }
    })

    const query = transformObjectToQueryString(allParams)

    currentRoute[newRoute] = query
      ? `${currentRoute[newRoute]}?${query}`
      : currentRoute[newRoute]

    return currentRoute
  }

  return body
    .map((child) => {
      const isDataProcessing = [
        'dataProcessing' in options.widgetData,
        options.widgetData?.dataProcessing === false
      ].every(Boolean)

      if (isDataProcessing) {
        return { ...child }
      }

      const currentChild = { ...child }

      const isArrayApiRoutes = !!(
        child.dataStructure?.apiRoutes &&
        Array.isArray(child?.dataStructure?.apiRoutes)
      )

      if (!isArrayApiRoutes) {
        const routes = child.dataStructure?.apiRoutes ?? {}

        Object.keys(routes).forEach((route) => {
          if (routes[route].params) {
            currentChild.dataStructure.apiRoutes[route] = getParametersValue(
              routes[route]
            )
          }
          if (routes[route].urlParams) {
            currentChild.dataStructure.apiRoutes[route] = getUrlParameters(
              routes[route]
            )
          }
        })
      }

      /** new-version */
      child.dataStructure?.apiRoutes?.forEach(
        (childRoute: IRoute, indexChildRoute: number) => {
          let currentChildRoute = { ...childRoute } as any

          const hasKeyUrl = !!childRoute?.url
          const hasKeyParams = !!childRoute?.params
          const hasKeyUrlParams = !!childRoute?.urlParams

          if (!hasKeyUrl) {
            Object.keys(currentChildRoute).forEach((route) => {
              if (currentChildRoute[route].params) {
                currentChild.dataStructure.apiRoutes[indexChildRoute][route] =
                  getParametersValue(currentChildRoute[route])
              }
              if (currentChildRoute[route].urlParams) {
                currentChild.dataStructure.apiRoutes[indexChildRoute] =
                  getUrlParameters((childRoute as any)[route], {
                    dataStructure: options.widgetData
                  })
              }
            })
            return currentChild
          }

          if (hasKeyParams) {
            currentChild.dataStructure.apiRoutes[indexChildRoute] =
              getParametersValue(childRoute)
          }

          if (hasKeyUrlParams) {
            currentChild.dataStructure.apiRoutes[indexChildRoute] =
              getUrlParameters(childRoute, {
                dataStructure: options.widgetData
              })
          }
        }
      )

      if (
        !!child.dataStructure &&
        !!child.dataStructure?.apiRoute &&
        !!child.dataStructure?.urlParams
      ) {
        currentChild.dataStructure = getUrlParameters(child.dataStructure)
      }
      return currentChild
    })
    .map((child) => {
      const { dataStructure } = child

      switch (child.name) {
        case 'DynamicListCard':
          return {
            ...child,
            dataStructure: DynamicListCardService.parseStructure(dataStructure)
          }
        case 'DynamicIntake':
          return {
            ...child,
            dataStructure: DynamicIntakeService.parseStructure(dataStructure)
          }
        case 'DynamicDemand':
          return {
            ...child,
            dataStructure: DynamicDemandService.parseStructure(dataStructure)
          }
        case 'DynamicChart':
          return {
            ...child,
            dataStructure: DynamicChartService.parseStructure(dataStructure)
          }
        case 'DynamicChartGauge':
          return {
            ...child,
            dataStructure:
              DynamicChartGaugeService.parseStructure(dataStructure)
          }
        case 'AlarmsNew':
          return {
            ...child,
            dataStructure: dataStructure
          }
        case 'DynamicDonutCard':
          return {
            ...child,
            dataStructure: DynamicDonutCardService.parseStructure(dataStructure)
          }
        case 'DynamicChartNew':
          return {
            ...child,
            dataStructure: DynamicChartNewService.parseStructure(dataStructure)
          }
        default:
          return { ...child, dataStructure }
      }
    })
}
