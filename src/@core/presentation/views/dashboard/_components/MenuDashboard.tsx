import Link from 'next/link'
import { useRouter } from 'next/router'
import React, {
  FC,
  forwardRef,
  ReactNode,
  RefObject,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState
} from 'react'

import { mapColorsIconsEquipmentByName } from '@/@core/content/mapColorsIconsEquipment.content'
import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { accountsApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import {
  formatInputValues,
  formatOutputValues,
  IValueCommonOut,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { getLabelByStatusDepreciated } from '@/utils/dashboard-deprecated'

import { useContextPage } from '../page.context'
import { useStatePage } from '../page.hook'
import { filterMenuByText } from '../page.utils'

export const MenuDashboard = ({
  menuFixed,
  setMenuFixed,
  menuFixedOpen
}: {
  menuFixed: boolean
  setMenuFixed: React.Dispatch<React.SetStateAction<boolean>>
  menuFixedOpen: boolean
}) => {
  const router = useRouter()

  const statePage = useStatePage()
  const { handleToggleMenuMobile } = useContextPage()

  const authStore = useAuthStore()
  const systemStore = useSystemStore()
  const loadingStore = useSystemLoadingStore()

  const InputSearchRef = React.useRef<SectionInputAccountRef>(null)

  const getPositions = () => {
    const body = document.body
    const el = document.getElementById('page-dashboard')

    const values = {
      left: 0,
      height: 0,
      width: 0,
      innerWidth: typeof window !== 'undefined' ? window.innerWidth : 0
    }

    if (el) {
      values.left = el.offsetLeft
      values.height = el.offsetHeight
      values.width =
        body?.offsetWidth && body.offsetWidth < 640 ? el.offsetWidth : 370
    }

    return { ...values }
  }

  const [positions, setPositions] = useState<{
    left: number
    height: number
    width: number
    innerWidth: number
  }>({ left: 0, height: 0, width: 0, innerWidth: 0 })

  useEffect(() => {
    const handleResize = () => {
      setPositions(getPositions())
    }

    handleResize()

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const isDesktop = positions.innerWidth >= 1366

  return isDesktop ? (
    <MenuDesktop
      menuFixedOpen={menuFixedOpen}
      menuFixed={menuFixed}
      setMenuFixed={setMenuFixed}
      InputSearchRef={InputSearchRef}
    />
  ) : (
    <MenuMobile
      statePage={statePage}
      handleToggleMenu={handleToggleMenuMobile ?? (() => { })}
      positions={positions}
      InputSearchRef={InputSearchRef}
      menuFixed={menuFixed}
      setMenuFixed={setMenuFixed}
    />
  )
}

const SectionHeader = ({
  focusInputAccount,
  menuFixed,
  setMenuFixed
}: {
  focusInputAccount: () => void
  menuFixed: boolean
  setMenuFixed: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const authStore = useAuthStore()
  const statePage = useStatePage()
  const {
    openDeleteDashboardModal,
    openCreateDashboardModal,
    handleInputAccount,
    handleReloadWidgets
  } = useContextPage()
  const permissions = useSystemStore().state.permissions
  const systemLoading = useSystemLoadingStore()

  const reloadEntities = async () => {
    const accountId = authStore.state.isSuperAdmin
      ? statePage.menu.inputAccount?.id
      : authStore.state.me?.accountId

    if (!accountId) {
      focusInputAccount()
      return
    }

    const { reload } =
      (await handleInputAccount?.({
        accountId,
        inputAccount: { ...statePage.menu.inputAccount! }
      })) ?? {}

    reload && handleReloadWidgets?.()
  }

  return (
    <div className="flex items-center justify-end gap-2 mb-2 relative">
      {!!permissions.dashboard?.create && (
        <Button
          className={cn(
            'bg-comerc-vibra-brandComerc-700 border-1 flex-1 text-comerc-grayLight-50 hover:text-comerc-vibra-brandComerc-700',
            'dark:bg-comerc-vibra-brandComerc-700',
            'h-[38px]'
          )}
          onClick={openCreateDashboardModal}
          title="Criar Dashboard"
        >
          Criar Dashboard
        </Button>
      )}

      {!!permissions.dashboard?.delete && (
        <Button
          variant="error-primary"
          className="p-2 h-[38px] w-[38px] text-center"
          disabled={systemLoading.state.loading}
          onClick={openDeleteDashboardModal}
          title="Excluir Dashboard"
        >
          <Icon
            icon="trash01"
            width="20"
            height="20"
            viewBox="0 0 22 22"
            className="icon-menu-secondary"
          />
        </Button>
      )}

      <Button
        className={cn('p-0 h-[38px] w-[38px]')}
        variant="icon-only"
        onClick={reloadEntities}
        title="Recarregar Dashboards"
      >
        <Icon
          icon="refreshccw05"
          width="20"
          height="20"
          className="icon-menu-primary mx-auto"
        />
      </Button>

      <FixedMenuToggleButton
        menuFixed={menuFixed}
        setMenuFixed={setMenuFixed}
      />
    </div>
  )
}
const SectionInputSearch = () => {
  const statePage = useStatePage()
  const { handleInputSearch } = useContextPage()

  const handleSearch = (value: string) => {
    // stateMenu.setSearch(value)
    handleInputSearch?.(value)

    Array.from([
      ['data-dashboardmenu_entity_btn', value ? 'open' : 'close'],
      ['data-dashboardmenu_entity_entities', value ? 'show' : 'hide']
    ]).forEach(([attr, value]) => {
      document
        .querySelectorAll(`[${attr}]`)
        .forEach((el) => el.setAttribute(attr, value))
    })
  }

  return (
    <Input.Root>
      <Input.Content
        slotStart={
          <Icon
            icon="searchLg"
            className="icon-menu-primary"
            height="24"
            width="24"
            viewBox="0 0 20 20"
          />
        }
        type="search"
        placeholder="Pesquisar dashboard"
        value={statePage.menu.inputSearch}
        onChange={(e) => handleSearch(e.target.value)}
      />
    </Input.Root>
  )
}
const SectionEntities = () => {
  const statePage = useStatePage()

  return (
    <div
      className={cn('flex-1 overflow-y-auto py-1 px-2 min-h-80', {
        'opacity-50': false // systemLoading.state.loading
      })}
    >
      {filterMenuByText(statePage.menu.items, statePage.menu.inputSearch).map(
        (entity) => (
          <EntityItem key={entity.entityName} entity={entity} isRoot />
        )
      )}
    </div>
  )
}
const EntityItem = ({
  entity,
  parentEntity,
  className,
  handleToggleParent,
  isRoot
}: {
  entity: IMenuDashboard
  className?: string
  parentEntity?: IMenuDashboard
  handleToggleParent?: () => void
  isRoot?: boolean
}) => {
  const statePage = useStatePage()
  const { selectMenuItem } = useContextPage()

  const isActive = statePage.menu.itemActive?.entityId === entity.entityId

  const isSuperAdmin = useAuthStore().state.isSuperAdmin
  const systemLoading = useSystemLoadingStore()
  const permissionsStore = useSystemStore().state.permissions

  /** company */
  const isEntityCompany = entity.entityType === 'company'

  const hasPermissionCompanytList = useMemo<boolean>(() => {
    return !!permissionsStore['register-company']?.list
  }, [])

  const titleCompany = useMemo(() => {
    if (entity.entityType !== 'equipment' && !entity.entityData?.status?.slug) {
      return 'Ir para a empresa'
    }
    const label = getLabelByStatusDepreciated(
      entity.entityData?.status?.slug ?? 'desativado'
    )
    return `Ir para a empresa | status: ${label}`
  }, [entity])

  /** equipament */
  const isEntityEquipment = entity.entityType === 'equipment'

  const hasPermissionEquipmentList = useMemo(() => {
    return !!permissionsStore['register-equipment']?.list
  }, [])

  const titleEquipment = useMemo(() => {
    return `Ir para o equipamento | Status - ${entity.entityData?.status.name ?? 'Desconectado'
      }`
  }, [entity])

  /** device */
  const titleDevice = useMemo(() => {
    return 'ir para o monitoramento'
  }, [])

  const showLinkDevice = useMemo<boolean>(() => {
    const isType = entity.entityType === 'equipment'
    const hasDevice = !!entity?.entityData?.device?.id

    return isType && hasDevice && isSuperAdmin
  }, [entity, isSuperAdmin])

  const handleToggleEntities = () => {
    const elButton = document.querySelector(
      `.dashboardmenu_entity_btn-${entity.entityId}`
    )
    const elEntities = document.querySelector(
      `.dashboardmenu_entity_entities-${entity.entityId}`
    )
    const isOpen =
      elButton?.getAttribute('data-dashboardmenu_entity_btn') === 'open'

    elButton?.setAttribute(
      'data-dashboardmenu_entity_btn',
      isOpen ? 'close' : 'open'
    )
    elEntities?.setAttribute(
      'data-dashboardmenu_entity_entities',
      isOpen ? 'hide' : 'show'
    )
  }

  useEffect(() => {
    isActive && handleToggleParent?.()
  }, [])

  return (
    <div className={cn('', className)}>
      <div className="flex items-center gap-1 min-h-6 mb-1">
        <LinkTo
          title={titleCompany}
          disabled={!hasPermissionCompanytList || systemLoading.state.loading}
          href={`/companies/${entity.entityId}`}
          hidden={!isEntityCompany}
        >
          <Icon
            icon="building07"
            width="20"
            height="20"
            viewBox="-2 -2 28 28"
            className="icon-menu-primary"
          />
        </LinkTo>

        <LinkTo
          title={titleEquipment}
          disabled={!hasPermissionEquipmentList || systemLoading.state.loading}
          href={`/equipments/${entity.entityId}`}
          hidden={!isEntityEquipment}
        >
          <Icon
            icon="signal02"
            width="20"
            height="20"
            viewBox="-2 -2 28 28"
            className={cn(
              'icon-menu-primary',
              'menu-equipment-icon-status',
              mapColorsIconsEquipmentByName[
              entity.entityData?.status.name || 'default'
              ]
            )}
          />
        </LinkTo>

        {/* <LinkTo
          title={titleDevice}
          // disabled={!permissionsStore['register-equipment']?.list || true} VERIFICAR O NOME DA PERMISSÃO PARA MONITORAMENTO
          disabled
          href={`/monitoring/${entity.entityData?.device?.id}`}
          hidden={!showLinkDevice}
        >
          <Icon
            icon="settings01"
            width="20"
            height="20"
            viewBox="-2 -2 28 28"
            className="icon-menu-primary"
          />
        </LinkTo> */}

        <button
          title={entity.entityName}
          className={cn(
            'text-xs text-left truncate px-1 min-h-6 flex-1 py-[4px] leading-6',
            'dark:text-comerc-grayLight-50',
            {
              'items-center rounded-[4px] bg-comerc-primary-50 text-comerc-primary-700 dark:text-comerc-grayLight-800':
                isActive
            }
          )}
          disabled={systemLoading.state.loading}
          onClick={() => !isActive && selectMenuItem?.(entity, parentEntity)}
        >
          {entity.entityName}
        </button>

        <button
          data-dashboardmenu_entity_btn={isRoot ? 'open' : 'close'}
          className={cn(
            'flex h-[20px] w-[20px] [&>*]:m-auto',
            `dashboardmenu_entity_btn-${entity.entityId}`,
            {
              invisible: entity.entities.length === 0
            }
          )}
          onClick={() => handleToggleEntities()}
          disabled={systemLoading.state.loading}
        >
          <Icon
            icon="chevronDown"
            width="18"
            height="18"
            viewBox="0 0 20 20"
            className="icon-menu-primary duration-200"
          />
        </button>
      </div>

      <section
        data-dashboardmenu_entity_entities={isRoot ? 'show' : 'hide'}
        className={`dashboardmenu_entity_entities-${entity.entityId}`}
      >
        {entity.entities.map((el) => (
          <EntityItem
            key={el.entityName}
            entity={el}
            parentEntity={entity}
            handleToggleParent={() => {
              handleToggleEntities()
              handleToggleParent?.()
            }}
            className={cn('pl-1 border-l border-comerc-grayDark-400 mb-1.5', {
              'ml-1': !!className
            })}
          />
        ))}
      </section>
    </div>
  )
}
const LinkTo: FC<{
  disabled?: boolean
  className?: string
  title?: string
  hidden?: boolean
  href: string
  children: ReactNode
}> = ({ disabled, className, title, hidden, href, children }) => {
  const css = cn(
    'flex h-5 w-5 [&_*]:auto',
    {
      hidden: hidden
    },
    className
  )

  return disabled ? (
    <span className={css} title={title}>
      {children}
    </span>
  ) : (
    <Link className={css} title={title} href={href}>
      {children}
    </Link>
  )
}

interface SectionInputAccountRef {
  focus: () => void
}
interface SectionInputAccountProps { }
const SectionInputAccount = forwardRef<
  SectionInputAccountRef,
  SectionInputAccountProps
>(({ }, ref) => {
  const authStore = useAuthStore()

  const statePage = useStatePage()
  const { clearMenuItems, handleInputAccount } = useContextPage()
  const systemLoading = useSystemLoadingStore()

  const inputRef = React.useRef<{ focus: () => void } | null>(null)

  const handle = (value: IValueCommonOut) => {
    if (value) {
      handleInputAccount?.({ accountId: value.id, inputAccount: value })
      return
    }

    clearMenuItems?.()
  }

  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus()
  }))

  return !authStore.state.isSuperAdmin ? (
    <></>
  ) : (
    <div className="flex items-center gap-2">
      <TagInput.Root className="w-full">
        <TagInput.ContentApi
          ref={inputRef}
          placeholder="Conta"
          value={formatInputValues(
            statePage.menu.inputAccount ? [statePage.menu.inputAccount] : []
          )}
          onChange={(values) => {
            const [account] = formatOutputValues(values)
            handle(account ?? null)
          }}
          featchData={(args) => accountsApiV3(http).get({ ...args })}
          disabled={systemLoading.state.loading}
        />
      </TagInput.Root>
    </div>
  )
})

const MenuContent = ({
  InputSearchRef,
  menuFixed,
  setMenuFixed
}: {
  InputSearchRef: RefObject<SectionInputAccountRef>
  menuFixed: boolean
  setMenuFixed: React.Dispatch<React.SetStateAction<boolean>>
}) => (
  <div className="flex flex-col gap-4 justify-between h-[calc(100vh-110px)] p-2">
    <SectionHeader
      focusInputAccount={() => InputSearchRef.current?.focus()}
      menuFixed={menuFixed}
      setMenuFixed={setMenuFixed}
    />
    <SectionInputAccount ref={InputSearchRef} />
    <SectionInputSearch />
    <SectionEntities />
  </div>
)

interface FixedMenuToggleButtonProps {
  menuFixed: boolean
  setMenuFixed: (value: boolean | ((prev: boolean) => boolean)) => void
}

const FixedMenuToggleButton: React.FC<FixedMenuToggleButtonProps> = ({
  menuFixed,
  setMenuFixed
}) => (
  <Button
    variant="icon-only"
    className={cn('p-0 h-[38px] w-[38px] hidden laptopLg:flex')}
    onClick={() => setMenuFixed((prev) => !prev)}
    title={menuFixed ? 'Desafixar menu' : 'Fixar menu'}
    type="button"
  >
    <Icon
      icon="pin"
      width="20"
      height="20"
      className={cn('icon-menu-primary mx-auto', {
        'stroke-comerc-vibra-brandComerc-700 fill-comerc-vibra-brandComerc-700':
          menuFixed
      })}
    />
  </Button>
)

interface MenuDesktopProps {
  menuFixedOpen: boolean
  menuFixed: boolean
  setMenuFixed: React.Dispatch<React.SetStateAction<boolean>>
  InputSearchRef: RefObject<SectionInputAccountRef>
}

// Componente do menu fixo
const MenuDesktop: React.FC<MenuDesktopProps> = ({
  menuFixedOpen,
  menuFixed,
  setMenuFixed,
  InputSearchRef
}) => (
  <div
    className={cn(
      'absolute top-0 left-0 h-[calc(100vh-106px)] z-30 border-[1px] bg-comerc-grayLight-25 border-comerc-grayLight-300 rounded-lg',
      'dark:bg-comerc-grayLight-900 dark:border-comerc-grayLight-800 overflow-auto',
      'transition-all duration-200 ease-in-out',
      menuFixedOpen
        ? 'w-[370px] opacity-100 translate-x-0'
        : 'w-[16px] opacity-0 -translate-x-full pointer-events-none'
    )}
  >
    <MenuContent
      InputSearchRef={InputSearchRef}
      menuFixed={menuFixed}
      setMenuFixed={setMenuFixed}
    />
  </div>
)

interface StatePage {
  menu: {
    open: boolean
  }
}

interface MenuPositions {
  left: string | number
  height: string | number
  width: string | number
}

interface MenuMobileProps {
  statePage: StatePage
  handleToggleMenu: () => void
  positions: MenuPositions
  InputSearchRef: RefObject<SectionInputAccountRef>
  menuFixed: boolean // ← novo
  setMenuFixed: React.Dispatch<React.SetStateAction<boolean>>
}

// Componente do menu flutuante
const MenuMobile: React.FC<MenuMobileProps> = ({
  statePage,
  handleToggleMenu,
  positions,
  InputSearchRef,
  menuFixed,
  setMenuFixed
}) => (
  <>
    <button
      data-state={statePage.menu.open ? 'open' : 'closed'}
      className={cn(
        'duration-150 bg-comerc-neutral-black/50 z-[50] w-full h-full fixed top-0 left-0 ',
        statePage.menu.open ? 'opacity-1 visible' : 'opacity-0 invisible'
      )}
      onClick={handleToggleMenu}
    />

    <div
      className={cn(
        'bg-comerc-grayLight-25 border-comerc-grayLight-300 md:duration-150 rounded shadow-lg absolute overflow-y-auto',
        'dark:bg-comerc-grayLight-900 dark:border-comerc-grayLight-800',
        'flex flex-col gap-2 p-2',
        statePage.menu.open
          ? 'opacity-1 visible z-50'
          : 'opacity-0 invisible -z-10'
      )}
      style={{
        top: '50%',
        left: positions.left,
        width: positions.width,
        transform: 'translateY(-50%)'
      }}
    >
      <MenuContent
        InputSearchRef={InputSearchRef}
        menuFixed={menuFixed}
        setMenuFixed={setMenuFixed}
      />
    </div>
  </>
)
