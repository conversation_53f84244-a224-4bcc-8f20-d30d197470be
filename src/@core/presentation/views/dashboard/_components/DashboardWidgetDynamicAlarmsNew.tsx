import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import dayjs from 'dayjs'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { IAlarmTriggered } from '@/@core/domain/AlarmTriggered'
import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import {
  alarmsApiV4,
  alarmsConditionalsApiV4,
  alarmsMultiTargetsApiV4
} from '@/@core/infra/api'
import { alarmsTriggeredApiV4 } from '@/@core/infra/api/AlarmsTriggeredApiV4'
import { TdoSearch } from '@/@core/infra/api/AlarmsTriggeredApiV4/AlarmsTriggeredApiV4.types'
import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http, Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import AlarmRuleStage, {
  ISectionRef
} from '@/@core/presentation/shared/AlarmRuleStage/Section'
import {
  useLanguageSection,
  useStateSection
} from '@/@core/presentation/shared/AlarmRuleStage/Section.hook'
import { IConditional } from '@/@core/presentation/shared/AlarmRuleStage/Section.types'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import {
  formatInputValue,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { ListAlarmsOperatorLogicService } from '@/@core/services/listAlarmsOperatorLogicService'
import { ListAlarmsRulesService } from '@/@core/services/listAlarmsRulesService'
import { checkValueEmail } from '@/@core/utils/regex'
import { mapAlarmFrequecy } from '@/content/mapAlarmFrequecy.content'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { parseRuleStageOutput } from '../../alarms.id/page.utils'
import { useLanguagePage } from '../page.hook'
import { IWidgetProps } from '../page.types'

const CONST_CATEGORY_USER_ID = 3

const instanceMap: Record<string, Record<string, string>> = {
  equipment: {
    entityType: 'equipmentId',
    targetType: 'equipments'
  },
  company: {
    entityType: 'companyId',
    targetType: 'companies'
  }
}

type ILocalData = {
  isLoading: boolean
  response: {
    items?: IAlarmTriggered[]
    total?: number
    page: number
    lastPage: number
  }
}

const DashboardWidgetDynamicAlarmsNew = ({
  inputData,
  triggerRequest,
  entityId
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isFetching = useRef(false)
  const modalRef = useRef<IModalRootRef>(null)
  const sectionTriggeringRef = useRef<ISectionRef>({})
  const sectionNormalizationRef = useRef<ISectionRef>({})

  const authStore = useAuthStore()
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()
  const permissions = useSystemStore().state.permissions

  const listAlarmsOperatorLogicService = ListAlarmsOperatorLogicService()
  const listAlarmsRulesService = ListAlarmsRulesService()

  const languagePage = useLanguagePage()

  const stateSection = useStateSection()
  const languageSection = useLanguageSection()

  const [localData, setData] = useState<ILocalData>({
    isLoading: true,
    response: {
      items: [],
      total: 0,
      page: 1,
      lastPage: 0
    }
  })

  /** TABLE */
  const fetchData = async (query: { page?: number } = {}) => {
    isFetching.current = true

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set('DynamicAlarmsNew', controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      let entity: string | undefined

      entity = instanceMap[inputData.instance].entityType

      const params: TdoSearch = {
        sort: 'id',
        order: 'desc',
        limit: 15,
        page: query?.page ?? localData.response.page
      }

      if (entity) {
        ;(params as Record<'companyId' | 'equipmentId', number>)[
          entity as 'companyId' | 'equipmentId'
        ] = entityId
      }

      const result = await alarmsTriggeredApiV4(instanceHttp).get(params)

      setData((p) => ({
        ...p,
        isLoading: false,
        response: result.data
      }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicAlarmsNew error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  /** MODAL */
  const dataEntityId = useMemo(() => {
    return inputData.instance === 'company' ? 2 : 1
  }, [inputData])

  const formFields = useFormFields()

  const handleClose = () => {
    modalRef.current?.close()
  }
  const handleSubmitAlarm = async () => {
    const accountId = !authStore.state.isSuperAdmin
      ? authStore.state.me.accountId!
      : await (async () => {
          const result =
            instanceMap[inputData.instance].targetType === 'equipments'
              ? await equipmentsApiV4(http).getById(inputData.id)
              : await companiesApiV4(http).getById(inputData.id)
          return result.data.accountId
        })()

    const alarmData = {
      name: formFields.values.name,
      description: '',
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysWeek: [0, 1, 2, 3, 4, 5, 6],
      daysRetention: null,
      categoryId: CONST_CATEGORY_USER_ID,
      status: true,
      accountId
    }
    const {
      data: { id: alarmId }
    } = await alarmsApiV4(http).create(alarmData)

    return { alarmId }
  }
  const handleSubmitTarget = async (alarmId: number) => {
    const { targetType } = instanceMap[inputData.instance]
    const isTargetTypeCompany = targetType === 'companies'

    const targetData = {
      alarmId,
      companies: isTargetTypeCompany ? [inputData.id] : [],
      equipments: !isTargetTypeCompany ? [inputData.id] : [],
      targets: [],
      type: targetType
    }
    await alarmsMultiTargetsApiV4(http).create(targetData)
  }
  const handleSubmitConditionals = async (alarmId: number) => {
    const promises: Promise<unknown>[] = []

    const mapConditionals: Record<number, IConditional[]> = {
      1: stateSection.itemsConditionalNormalization,
      2: stateSection.itemsConditionalTriggering
    }
    Array.from([1, 2]).forEach((alarmStageId) => {
      mapConditionals[alarmStageId].map((conditional) => {
        const conditionalData = parseRuleStageOutput({
          alarmId,
          alarmStageId,
          conditional
        })
        promises.push(alarmsConditionalsApiV4(http).create(conditionalData))
      })
    })
    await Promise.all(promises)
  }
  const handleSubmit = async () => {
    try {
      setData((p) => ({ ...p, isLoading: true }))

      /** CREATE ALARM */
      const { alarmId } = await handleSubmitAlarm()

      /** CREATE TARGET */
      await handleSubmitTarget(alarmId)

      /** CREATE CONDITIONALS */
      await handleSubmitConditionals(alarmId)

      systemToast.addToast({
        message: languagePage.createAlarmModal.form.messages.successMessage
      })

      handleClose()
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title:
          '@core/presentation/views/dashboard/_components/DashboardWidgetDynamicAlarmsNew'
      })

      systemToast.addToast({
        message: languagePage.createAlarmModal.form.messages.errorMessage,
        type: 'error'
      })
    } finally {
      setData((p) => ({ ...p, isLoading: false }))
    }
  }

  /** abort request when unmounted */
  useEffect(() => {
    listAlarmsOperatorLogicService.handler()
    listAlarmsRulesService.handler()

    return () => abortControllers.current.get('DynamicAlarmsNew')?.abort()
  }, [])

  /** fetchData */
  useEffect(() => {
    if (isFetching.current) {
      abortControllers.current.get('DynamicAlarmsNew')?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <>
          <Table.Root>
            <Table.Info className="flex justify-between">
              <Table.InfoTitle>
                {languagePage.tableHistoricalAlarm.title}
              </Table.InfoTitle>
              <Table.InfoNewRegister
                permission={permissions['alarms']?.create}
                onClick={() => modalRef.current?.open()}
              />
            </Table.Info>

            <Table.Header>
              <Table.Row>
                <Table.Head>Nome do alarme</Table.Head>
                <Table.Head textClassName="justify-center">
                  Acionado em:
                </Table.Head>
                <Table.Head textClassName="justify-center">
                  Normalizado em:
                </Table.Head>
              </Table.Row>
            </Table.Header>

            <Table.Body className="min-h-[500px]">
              {localData.response?.items?.map((larmTriggered) => (
                <Table.Row key={larmTriggered.id}>
                  <Table.Cell>{larmTriggered.alarmName}</Table.Cell>
                  <Table.Cell className="text-center">
                    {dayjs(larmTriggered.triggeredAt).format('DD/MM/YYYY')}
                  </Table.Cell>
                  <Table.Cell className="text-center">
                    {larmTriggered.normalizedAt
                      ? dayjs(larmTriggered.normalizedAt).format('DD/MM/YYYY')
                      : 'N/D'}
                  </Table.Cell>
                </Table.Row>
              ))}

              <Table.RowLoading
                status={systemLoading.state.loading}
                colSpan={3}
              />
            </Table.Body>

            <Table.Mobile>
              {localData.response?.items?.map((larmTriggered) => (
                <TableMobile.Item key={larmTriggered.id}>
                  <TableMobile.Row>
                    <TableMobile.Cell>Nome do alarme</TableMobile.Cell>
                    <TableMobile.Cell>
                      {larmTriggered.alarmName}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Acionado em:</TableMobile.Cell>
                    <TableMobile.Cell>
                      {dayjs(larmTriggered.triggeredAt).format('DD/MM/YYYY')}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Normalizado em:</TableMobile.Cell>
                    <TableMobile.Cell>
                      {larmTriggered.normalizedAt
                        ? dayjs(larmTriggered.normalizedAt).format('DD/MM/YYYY')
                        : 'N/D'}
                    </TableMobile.Cell>
                  </TableMobile.Row>
                </TableMobile.Item>
              ))}
            </Table.Mobile>

            <Table.Paginate
              status={systemLoading.state.loading}
              currentPage={localData.response.page}
              lastPage={localData.response.lastPage}
              handleChangePage={(page) => fetchData({ page })}
            />
          </Table.Root>

          <Modal.Root
            ref={modalRef}
            size="lg3"
            contentProps={{
              style: {
                height: '90vh',
                overflow: 'hidden'
              }
            }}
            handleClose={() => {
              formFields.reset()
            }}
          >
            <Modal.Title>{languagePage.createAlarmModal.title}</Modal.Title>

            <Modal.Content>
              <form
                onSubmit={formFields.handleSubmit(handleSubmit)}
                className="flex flex-col gap-2"
                id="dynamicAlarmsNewForm"
              >
                {/*TabData*/}
                <div>
                  <Input.Root>
                    <Input.Label htmlFor="name">
                      {languagePage.createAlarmModal.form.input.name}
                    </Input.Label>
                    <Input.Content
                      id="name"
                      value={formFields.values.name}
                      onChange={(e) => {
                        formFields.setValue('name', e.target.value)
                      }}
                      helperText={formFields.errors.name?.message}
                      disabled={systemLoading.state.loading}
                    />
                  </Input.Root>
                </div>
              </form>

              {/*TabRules*/}
              <AlarmRuleStage
                ref={sectionTriggeringRef}
                title={languageSection.triggering.title}
                titleList={languageSection.triggering.titleList}
                alarmStageId={1}
                dataEntityId={dataEntityId}
                disabled={!dataEntityId}
              />
              <AlarmRuleStage
                ref={sectionNormalizationRef}
                title={languageSection.normalization.title}
                titleList={languageSection.normalization.titleList}
                alarmStageId={2}
                dataEntityId={dataEntityId}
                disabled={!dataEntityId}
              />

              {/*TabNotification*/}
              <div>
                <TagInput.Root>
                  <TagInput.Label htmlFor="frequency">
                    {languagePage.createAlarmModal.form.input.frequency}
                  </TagInput.Label>
                  <TagInput.Content
                    key={formFields.values.frequency?.id}
                    name="frequency"
                    value={String(formFields.values.frequency?.id ?? '')}
                    onChange={(values) => {
                      const [item] = formatOutputValues(values)
                      formFields.setValue('frequency', item)
                    }}
                    options={mapAlarmFrequecy.map(formatInputValue)}
                    disabled={systemLoading.state.loading}
                    helperText={formFields.errors.frequency?.message}
                  />
                </TagInput.Root>

                <TagInput.Root>
                  <TagInput.Label htmlFor="configs">
                    {languagePage.createAlarmModal.form.input.email}
                  </TagInput.Label>
                  <TagInput.ContentCreatable
                    name="configs"
                    value={formFields.values.configs}
                    onChange={(values) =>
                      formFields.setValue('configs', values)
                    }
                    placeholder={
                      languagePage.createAlarmModal.form.input.placeholderEmail
                    }
                    helperText={formFields.errors.configs?.message}
                    disabled={systemLoading.state.loading}
                    isMulti={true}
                    onInputFilter={checkValueEmail}
                  />
                </TagInput.Root>
              </div>
            </Modal.Content>

            <Modal.Footer>
              <Button type="button" onClick={handleClose}>
                {languagePage.createAlarmModal.form.btn.cancel}
              </Button>

              <Button
                type="submit"
                variant="primary"
                form="dynamicAlarmsNewForm"
              >
                {languagePage.createAlarmModal.form.btn.add}
              </Button>
            </Modal.Footer>
          </Modal.Root>
        </>
      )}
    </>
  )
}

const useFormFields = () => {
  const {
    createAlarmModal: {
      form: { requiredField }
    }
  } = useLanguagePage()

  const formSchema = z.object({
    name: z.string().min(1, { message: requiredField }),
    frequency: z
      .object({
        id: z.number(),
        name: z.string()
      })
      .nullable(),
    configs: z.array(z.string()).min(1, { message: requiredField })
  })

  type FormSchema = z.infer<typeof formSchema>

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      frequency: null,
      configs: []
    }
  })

  const values = watch()

  return { handleSubmit, setValue, values, errors, reset }
}

export default DashboardWidgetDynamicAlarmsNew
