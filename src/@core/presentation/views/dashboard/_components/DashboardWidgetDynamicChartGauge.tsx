import { AxiosError } from 'axios'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { useEffect, useRef, useState } from 'react'

// sunburst(Highcharts)
// highchartsMore(Highcharts)
// solidGauge(Highcharts)
// indicators(Highcharts)
// HighchartSankey(Highcharts)
// HighchartsWheel(Highcharts)
// accessibility(Highcharts)
// Exporting(Highcharts)
// exportDataModule(Highcharts)

import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicChartGaugeService from '@/utils/dashboard/DynamicChartGaugeService'

import { IStructureDynamicChartGauge, IWidgetProps } from '../page.types'

type IDataResponse = {}
type IRequest = {}

type ILocalData = {
  widgetData: IStructureDynamicChartGauge
  isLoading: boolean
  response: any
}
const _key = 'DynamicChartGauge'
const DashboardWidgetDynamicChartGauge = ({
  dataStructure,
  inputData,
  triggerRequest
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isMounted = useRef(false)
  const isFetching = useRef(false)

  const [localData, setData] = useState<ILocalData>({
    widgetData: dataStructure as IStructureDynamicChartGauge,
    isLoading: true,
    response: []
  })

  const fetchData = async () => {
    isFetching.current = true
    const rangeDate = null

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const currentInputData = { ...inputData }

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set(_key, controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      const requests = DynamicChartGaugeService.preparePromise(
        localData.widgetData.apiRoutes,
        currentInputData,
        rangeDate,
        instanceHttp
      )

      const results: any = Array.from(await Promise.all(requests)).map(
        (response: unknown, responseIndex: number) => {
          const currentResponse = response as IRequest

          return DynamicChartGaugeService.parseResponse(currentResponse, {
            inputData: currentInputData
            // config:
          })
        }
      )

      const responses = new ReadyChartGaugeNewResponse(
        results,
        localData.widgetData
      ).exec()

      setData((p) => ({ ...p, isLoading: false, response: responses }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicChartGauge error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get(_key)?.abort()
  }, [])

  useEffect(() => {
    if (isFetching.current && abortControllers.current.size > 0) {
      abortControllers.current.get(_key)?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return localData.isLoading && localData?.response ? (
    <>
      <Skeleton className="h-10 mb-2" />
      <Skeleton className="h-36" />
    </>
  ) : (
    <>
      <HighchartsReact highcharts={Highcharts} options={localData?.response} />

      <p className="text-sm text-right">
        {localData?.response?.lastUpdate
          ? `Atualizado em: ${localData?.response?.lastUpdate}`
          : 'Nenhum dado obtido'}
      </p>
    </>
  )
}

export default DashboardWidgetDynamicChartGauge

class ReadyChartGaugeNewResponse {
  private lastUpdate: any = null

  constructor(private apiResponse: any, private structure: any) {}

  exec() {
    return this.responseForSeries()
  }

  /** ForSeries */
  private responseForSeries() {
    const structure = this.structure
    const apiResponse = this.apiResponse

    let keys = 0
    const series: any = {}
    // this.setTimezoneForSeriesResponse()

    structure.apiRoutes.forEach((route: any, key: number) => {
      route.response = apiResponse[key] ? apiResponse[key] : apiResponse
    })
    structure.apiRoutes.forEach((route: any, key: number) => {
      const finded = structure.chartOptions.series.find(
        (serie: any) => serie.key === route.seriesKey
      )

      const newKey = finded ? route.seriesKey : key

      if (!series[newKey]) {
        series[newKey] = []
      }
      if (route.config && route.config === 'separate_series') {
        route.response.series.forEach((children: any) => {
          children.source = route
          series[newKey].push(children)
        })
      }
    })

    Object.keys(series).forEach((serie) => {
      const finded = structure.chartOptions.series.find(
        (item: any) => item.key === serie
      )

      if (finded) {
        const keySerie: any = {
          source: {},
          data: []
        }
        series[serie].forEach((item: any) => {
          keySerie.source = item.source
          const list = this.formatSeries(item.data)
          list.forEach((child: any) => {
            keySerie.data.push(child)
          })
        })
        this.getSourceSeries(finded, keySerie)
        finded.data = keySerie.data.sort((itemA: any, itemB: any) => {
          return itemA[0] - itemB[0]
        })
      } else {
        series[serie].forEach((item: any) => {
          this.getSourceSeries(structure.chartOptions.series[keys], item)
          structure.chartOptions.series[keys].data = this.formatSeries(
            item.data
          ).sort((itemA: any, itemB: any) => {
            return itemA[0] - itemB[0]
          })
          keys = keys + 1
        })
      }
    })

    return {
      // lastUpdate: this.lastUpdate,
      ...structure.chartOptions,
      chart: {
        ...structure.chartOptions.chart,
        type: 'line',
        zoomType: 'x',
        backgroundColor: 'transparent'
      },
      title: {
        ...structure.chartOptions.chart,
        text: null
      },
      legend: {
        ...this.structure?.chartOptions?.legend,
        enabled: true,
        padding: 5
      },
      credits: {
        enabled: false
      }
    }
  }

  private getSourceSeries(finded: any, item: any) {
    finded.name = finded.name ? finded.name : item.entity_name
    finded.property = item.property
    return finded
  }

  private formatSeries(source: any) {
    const serieList: any = []

    source.forEach((serie: any) => {
      // this.lastUpdate = dayjs(serie.timestamp * 1000).format('DD/MM/YYYY HH:mm')
      serieList.push([serie.timestamp * 1000, serie.value])
    })
    return serieList
  }
}
