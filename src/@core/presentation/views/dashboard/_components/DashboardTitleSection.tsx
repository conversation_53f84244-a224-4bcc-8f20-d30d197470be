import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { useStatePage } from '../page.hook'

interface DashboardTitleSectionProps {
  setIsDesktopMenuOpen: React.Dispatch<React.SetStateAction<boolean>>
  handleToggleMenuMobile: () => void
}

export const DashboardTitleSection = ({
  setIsDesktopMenuOpen,
  handleToggleMenuMobile
}: DashboardTitleSectionProps) => {
  const statePage = useStatePage()

  const accountName = statePage.menu.inputAccount?.name
  const parentCompany = statePage.menu.itemActive?.parent?.entityName

  return (
    <div
      className={cn(
        'col-span-12',
        'flex flex-col gap-[12px] overflow-x-auto h-full w-full'
      )}
    >
      <div className="flex items-center gap-4">
        <button
          className={cn(
            'p-2 flex items-center justify-center h-[38px] min-w-[38px] rounded border border-comerc-primary-700',
            'dark:bg-primary',
            'hidden laptopLg:flex'
          )}
          onClick={() => setIsDesktopMenuOpen((prev) => !prev)}
        >
          <Icon
            icon="menu"
            width="20"
            height="20"
            className="icon-menu-primary stroke-comerc-primary-700"
          />
        </button>

        <button
          className={cn(
            'flex items-center justify-center h-[38px] min-w-[38px] rounded border border-comerc-primary-700',
            'dark:bg-primary',
            'laptopLg:hidden'
          )}
          onClick={handleToggleMenuMobile}
        >
          <Icon
            icon="menu"
            width="20"
            height="20"
            className="icon-menu-primary stroke-comerc-primary-700"
          />
        </button>

        <h1 className="font-semibold text-left text-[30px] leading-[38px] text-comerc-grayLight-900 dark:text-comerc-grayLight-50">
          {statePage.menu.itemActive?.entityName ?? 'Dashboard'}
        </h1>
      </div>
      {statePage.menu.itemActive?.entityName &&
        (accountName || parentCompany) && (
          <div className="flex flex-col gap-2 ml-14">
            {accountName && (
              <div>
                <b className="text-[20px] leading-[30px] text-comerc-grayDark-700 dark:text-comerc-grayLight-500">
                  Conta:{' '}
                </b>
                <span className="font-acuminPro-Regular text-[18px] leading-[28px] text-comerc-grayDark-700 dark:text-comerc-grayLight-500">
                  {accountName}
                </span>
              </div>
            )}
            {parentCompany && (
              <div>
                <b className="text-[20px] leading-[30px] text-comerc-grayDark-700 dark:text-comerc-grayLight-500">
                  Unidade:{' '}
                </b>
                <span className="font-acuminPro-Regular text-[18px] leading-[28px] text-comerc-grayDark-700 dark:text-comerc-grayLight-500">
                  {parentCompany}
                </span>
              </div>
            )}
          </div>
        )}
      {!statePage.menu.itemActive?.entityName && (
        <p className="text-text-comerc-grayLight-900 text-[16px] leading-[24px] w-full break-words whitespace-pre-line">
          Acompanhe de forma simples os principais dados de consumo, gráficos,
          histórico e alertas para monitorar sua operação e apoiar decisões.
        </p>
      )}
    </div>
  )
}
