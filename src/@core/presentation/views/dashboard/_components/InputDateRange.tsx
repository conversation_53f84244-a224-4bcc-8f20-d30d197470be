import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Button } from '@/@core/presentation/shared/ui/button'
import { CalendarRange } from '@/@core/presentation/shared/ui/calendar'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import dayjs from 'dayjs'
import { useState } from 'react'
import { useStatePage } from '../page.hook'
import { renderInputData } from '../page.utils'

export const InputDateRange = () => {
  const statePage = useStatePage()
  const [openDialog, setOpenDialog] = useState(false)

  const closeModal = () => {
    setOpenDialog(false)
  }
  const handleChange = ({
    from,
    to
  }: {
    from: Date | undefined
    to: Date | undefined
  }) => {
    const period = {
      initial: from ? dayjs(from).format('YYYY-MM-DD') : '',
      final: to ? dayjs(to).format('YYYY-MM-DD') : ''
    }

    let inputData = renderInputData({
      period,
      itemActive: statePage.menu.itemActive
    })

    statePage.set({
      page: { ...statePage.page, period, inputData }
    })
    closeModal()
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger
        className="p-1 w-8A bg-red-900A text-center text-lg flex gap-0.5"
        asChild
      >
        <Button className={cn('p-2 h-[40px]  w-[219px]')} variant="icon-only">
          <Icon
            icon="calendar2"
            width="20"
            height="20"
            className="icon-menu-primary"
          />
          <PeriodLabel {...statePage.page.period} />
        </Button>
      </Dialog.Trigger>

      <Dialog.Content size="sm">
        <Dialog.Title hidden />
        <Dialog.Description hidden />

        <CalendarRange
          values={{
            from: statePage.page.period.initial
              ? dayjs(statePage.page.period.initial).toDate()
              : undefined,
            to: statePage.page.period.final
              ? dayjs(statePage.page.period.final).toDate()
              : undefined
          }}
          onChange={(value) => handleChange(value)}
          handleCancel={closeModal}
        />
      </Dialog.Content>
    </Dialog.Root>
  )
}
const PeriodLabel = ({
  initial,
  final
}: {
  initial: string
  final: string
}) => {
  return (
    <p className="text-sm text-comerc-grayLight-700 dark:text-comerc-grayLight-500">
      {!initial && !final ? (
        <span>Período</span>
      ) : (
        <>
          <span>{dayjs(initial).format('DD/MM/YYYY')}</span>
          <span> - </span>
          <span>{dayjs(final).format('DD/MM/YYYY')}</span>
        </>
      )}
    </p>
  )
}
