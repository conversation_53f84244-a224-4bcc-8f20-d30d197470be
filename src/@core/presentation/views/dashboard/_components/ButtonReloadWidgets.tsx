import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { But<PERSON> } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { useContextPage } from '../page.context'

export const ButtonReloadWidgets = () => {
  const { handleReloadWidgets } = useContextPage()

  return (
    <Button
      className={cn('p-0 min-h-[38px] min-w-[38px]')}
      variant="icon-only"
      onClick={handleReloadWidgets}
    >
      <Icon
        icon="refreshccw05"
        width="20"
        height="20"
        className="icon-menu-primary mx-auto"
      />
    </Button>
  )
}
