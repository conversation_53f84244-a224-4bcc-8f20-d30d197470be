import { AxiosError } from 'axios'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { useEffect, useRef, useState } from 'react'

import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicDonutCardService from '@/utils/dashboard/DynamicDonutCardService'

import { IStructureDynamicDonutCard, IWidgetProps } from '../page.types'

type IRequest = {}

type ILocalData = {
  widgetData: IStructureDynamicDonutCard
  isLoading: boolean
  // response: IDataResponse[]
  data: any
  valueGraph: any
}
const _key = 'DynamicDonutCard'

const DashboardWidgetDynamicDonutCard = ({
  dataStructure,
  inputData,
  triggerRequest
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isMounted = useRef(false)
  const isFetching = useRef(false)

  const [localData, setData] = useState<ILocalData>({
    widgetData: dataStructure as IStructureDynamicDonutCard,
    isLoading: true,
    data: null,
    valueGraph: null
  })

  const timeRef = useRef<NodeJS.Timeout>()

  const fetchData = async () => {
    isFetching.current = true
    const rangeDate = null

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const currentInputData = { ...inputData }

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set(_key, controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      const routes = [...localData.widgetData.apiRoutes]

      const requests = DynamicDonutCardService.preparePromise(
        routes,
        currentInputData,
        rangeDate,
        instanceHttp
      )

      const results: any = Array.from(await Promise.all(requests)).map(
        (response: unknown, responseIndex: number) => {
          const currentResponse = response as IRequest

          return DynamicDonutCardService.parseResponse(currentResponse, {
            inputData: currentInputData,
            config: routes[responseIndex].content
          })
        }
      )

      const readyChart = new ReadyChartResponse(results, localData.widgetData)

      const responses = { data: readyChart, valueGraph: readyChart.getData() }

      setData((p) => ({ ...p, ...responses, isLoading: false }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicDonutCard error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  const handleSearch = (query: string) => {
    clearTimeout(timeRef.current)

    timeRef.current = setTimeout(() => {
      setData((p) => ({
        ...p,
        valueGraph: localData.data?.getFiltered(query)
      }))
    }, 350)
  }

  /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get(_key)?.abort()
  }, [])

  /** fetchData */
  useEffect(() => {
    if (isFetching.current) {
      abortControllers.current.get(_key)?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <>
          <div className="sm:flex items-center">
            <p className="dashboard-widget-title m-0">
              {localData.widgetData.title}
            </p>
            <Input.Content
              className="sm:w-min min-w-[150px] sm:ml-auto mb-2 sm:mb-2"
              placeholder="Buscar por:"
              onChange={(e) => handleSearch(e.target.value)}
              disabled={!localData?.valueGraph?.series?.length}
            />
          </div>

          <HighchartsReact
            highcharts={Highcharts}
            options={localData.valueGraph}
          />
        </>
      )}
    </>
  )
}

export default DashboardWidgetDynamicDonutCard

class ReadyChartResponse {
  private serieFull: any = null

  constructor(private apiResponse: any, private structure: any) {
    this.serieFull = this.serializeResponse()
  }

  // exec() {
  //   return this.serializeResponseDonut()
  // }

  serializeResponse() {
    const donutList: any = []

    this.apiResponse?.forEach((apiResponseItem: any, index: number) => {
      apiResponseItem?.forEach(({ name, y }: any) => {
        donutList.push({ name, y })
      })
    })

    return donutList
  }
  getData() {
    const chartOptions = JSON.parse(JSON.stringify(this.structure.chartOptions))

    chartOptions.series[0].data = [...this.serieFull]

    return chartOptions
  }
  getFiltered(query: string) {
    const chartOptions = JSON.parse(JSON.stringify(this.structure.chartOptions))
    const _q = String(query).toLowerCase()

    if (!Array.isArray(chartOptions.series) || !chartOptions.series[0]) {
      return chartOptions
    }

    let filteredData = [...this.serieFull]
    if (_q) {
      filteredData = filteredData.filter((el) =>
        String(el.name).toLowerCase().includes(_q)
      )
    }

    chartOptions.series[0].data = filteredData

    return chartOptions
  }
}
