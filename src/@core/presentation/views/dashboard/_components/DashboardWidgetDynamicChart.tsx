import { AxiosError } from 'axios'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { useEffect, useRef, useState } from 'react'

import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicChartService from '@/utils/dashboard/DynamicChartService'

import { IStructureDynamicChart, IWidgetProps } from '../page.types'

type IRequest = {}
type IRangeForm = {
  check: boolean
  date: Date[]
}
type ILocalData = {
  widgetData: IStructureDynamicChart
  isLoading: boolean
  response: any
}
const _key = 'DynamicChart'

const DashboardWidgetDynamicChart = ({
  dataStructure,
  inputData,
  triggerRequest
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isFetching = useRef(false)
  const isMounted = useRef(false)

  const [localData, setData] = useState<ILocalData>({
    widgetData: dataStructure as IStructureDynamicChart,
    isLoading: true,
    response: {
      title: {
        text: null
      },
      legend: {
        enabled: true,
        padding: 5
      },
      credits: {
        enabled: false
      },
      series: []
    }
  })

  const [rangeForm, setRangeFormValue] = useState<IRangeForm>({
    check: false,
    date: [
      new Date(inputData.initial_date * 1000),
      new Date(inputData.final_date * 1000)
    ]
  })

  const setRangeForm = (val: Partial<IRangeForm>) => {
    setRangeFormValue((p) => ({ ...p, ...val }))
  }

  const fetchData = async () => {
    isFetching.current = true

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const currentInputData = { ...inputData }

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set(_key, controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      const request = DynamicChartService.preparePromise(
        localData.widgetData.apiRoute,
        currentInputData,
        rangeForm,
        instanceHttp
      )

      const requests = await request.then((apiResponse: IRequest) =>
        DynamicChartService.parseResponse(apiResponse, {
          inputData
        })
      )

      const responses = new ReadyChartResponse(
        requests,
        localData.widgetData,
        currentInputData
      ).exec()

      setData((p) => ({ ...p, isLoading: false, response: responses }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicChart error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  useEffect(() => {
    if (!isMounted.current) return

    setRangeForm({
      date: [
        new Date(inputData.initial_date * 1000),
        new Date(inputData.final_date * 1000)
      ]
    })
  }, [inputData.initial_date, inputData.final_date])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Carrega o módulo de exportação
      import('highcharts/modules/exporting').then((module) => {
        if (module && typeof module.default === 'function') {
          module.default(Highcharts)
        }
      })
      // Carrega o módulo de exportação de dados
      import('highcharts/modules/export-data').then((module) => {
        if (module && typeof module.default === 'function') {
          module.default(Highcharts)
        }
      })
    }
  }, [])

  // /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get(_key)?.abort()
  }, [])

  useEffect(() => {
    if (!isMounted.current) isMounted.current = true

    if (isFetching.current && abortControllers.current.size > 0) {
      abortControllers.current.get(_key)?.abort()
    }
    fetchData()
  }, [rangeForm, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <>
          <div className="grid grid-cols-2 lg:flex items-center px-2 py-1 mb-2">
            <span
              className="dashboard-widget-title truncate mr-auto"
              title={localData.widgetData?.title}
            >
              {localData.widgetData?.title}
            </span>

            {localData.widgetData?.rangeTitle && (
              <Checkbox.Root className="ml-auto">
                <Checkbox.Content
                  id={localData.widgetData?.rangeTitle}
                  className="m-0 p-0"
                  checked={rangeForm.check}
                  onCheckedChange={(value: any) =>
                    setRangeForm({ check: !!value })
                  }
                  disabled={localData.isLoading}
                />
                <Checkbox.Label
                  htmlFor={localData.widgetData?.rangeTitle}
                  className="text-xs"
                >
                  {localData.widgetData?.rangeTitle}
                </Checkbox.Label>
              </Checkbox.Root>
            )}
          </div>
          <HighchartsReact
            highcharts={Highcharts}
            options={localData.response}
          />
        </>
      )}
    </>
  )
}

export default DashboardWidgetDynamicChart

const DEFAULT_EXPORT_FILE_NAME = 'zordon-exporting-data'
const ENTITY_NAME_PLACEHOLDER = 'entity_name'
/** const TIMEZONE_DEFAULT = -3 */

class ReadyChartResponse {
  private timezone: number

  constructor(
    private apiResponse: any,
    private structure: any,
    private inputData: any
  ) {
    this.timezone = this.inputData.timezone
  }

  private renderChartOption() {
    return {
      ...this.structure?.chartOptions,
      chart: {
        ...this.structure?.chartOptions.chart,
        zoomType: 'x',
        backgroundColor: 'transparent'
      },
      time: {
        /** timezoneOffset: typeof this.timezone === 'number' ? this.timezone : TIMEZONE_DEFAULT * -60  */
        timezoneOffset: this.timezone * -60
      },
      title: {
        ...this.structure?.chartOptions.title,
        text: ' '
      },
      legend: {
        ...this.structure?.chartOptions?.legend,
        enabled: true,
        padding: 5
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: true,
        buttons: {
          contextButton: {
            menuItems: [
              'viewData',
              'viewFullscreen',
              'printChart',
              'separator',
              'downloadPNG',
              'downloadJPEG',
              'downloadPDF',
              'downloadSVG',
              'separator',
              'downloadCSV',
              'downloadXLS'
            ]
          }
        },
        ...this.structure?.chartOptions?.exporting
      },
      lang: {
        contextButtonTitle: 'Menu de exportação do gráfico',
        viewFullscreen: 'Ver em tela cheia',
        printChart: 'Imprimir gráfico',
        downloadPNG: 'Baixar em PNG',
        downloadJPEG: 'Baixar em JPEG',
        downloadPDF: 'Baixar em PDF',
        downloadSVG: 'Baixar em SVG',
        hideData: 'Esconder tabela',
        viewData: 'Ver dados da tabela',
        downloadCSV: 'Baixar CSV',
        downloadXLS: 'Baixar XLS',
        noData: 'Sem dados'
      }
      // lang: {
      //   noData: 'No data'
      // },
      // noData: {}
    }
  }

  exec() {
    this.timezone = this.inputData.timezone

    const isForSeries = 'series' in this.apiResponse

    if (isForSeries) {
      return this.responseForSeries()
    }
    if (Array.isArray(this.apiResponse)) {
      return this.responseForData()
    }
    return this.responseForDays()
  }

  /** ForSeries */
  private responseForSeries() {
    /** this.setTimezoneForSeriesResponse() */

    const series: any[] = []

    this.apiResponse?.series?.forEach((item: any, key: any) => {
      if (!this.structure.chartOptions?.series?.[key]?.data) return

      series[key] = []

      item.data.forEach((serie: any) => {
        if (serie.timestamp && serie.value) {
          series[key].push([serie.timestamp * 1000, serie.value])
        } else {
          series[key].push([serie])
        }
      })

      this.structure.chartOptions.series[key].data = series[key]
    })

    return this.renderChartOption()
  }

  /** ForData */
  private responseForData() {
    this.apiResponse.forEach((serie: any, key: any) => {
      if (!this.structure.chartOptions?.series?.[key]?.data) return

      this.structure.chartOptions.series[key].data = serie.data
    })

    return this.renderChartOption()
  }

  /** ForDays */
  private responseForDays() {
    /**  this.setTimezoneForDaysResponse() */

    if (this.structure && !!this.structure?.chartOptions) {
      this.prepareResult()
      this.graficMutation()
      this.formatLabels()
      this.formatTooltip()
      this.formatTitle()
      this.formatTimezone()
      this.formatExportingFileName()
    }

    this.structure.chartOptions = {
      ...this.structure?.chartOptions,
      xAxis: {
        ...this.structure?.chartOptions?.xAxis,
        type: 'datetime',
        dateTimeLabelFormats: {
          hour: '%H:%M',
          day: '%e de %b',
          week: '%e %b',
          month: '%b de %Y',
          year: '%Y'
        }
      }
    }

    return this.renderChartOption()
  }
  private prepareResult() {
    const originSeries = this.structure.chartOptions?.series ?? []

    const groupedSeries: any = this.groupSeriesByRule(originSeries)

    if (originSeries && Array.isArray(originSeries)) {
      originSeries.forEach((item) => {
        item.data = groupedSeries[item.key] ? groupedSeries[item.key] : []
      })
    }

    this.structure.chartOptions.series = originSeries
  }
  private groupSeriesByRule(originSeries: any) {
    const finalSeries: any = {}

    if (originSeries) {
      originSeries.forEach((serie: any) => {
        finalSeries[serie.key] = []
      })
    }

    Object.keys(this.apiResponse).forEach((currentDate) => {
      const seriesList = this.formatActivityRecords(currentDate)

      if (Array.isArray(seriesList)) {
        seriesList.forEach((currentSerie) => {
          if (currentSerie) {
            this.chartAssemblyRules(
              originSeries,
              finalSeries,
              currentDate,
              currentSerie
            )
          }
        })
      }
    })

    return finalSeries
  }
  private formatActivityRecords(currentDate: any) {
    const rules = this.structure.originForNoRecords

    const rule: any = { date: currentDate.replace(/-/g, '/') }

    let forNoRecords = null

    if (rules && rule.content) {
      Object.keys(rules.content).forEach((key) => {
        if (rules.content[key] === 'time') {
          rule[key] = '00:00:00'
        } else {
          rule[key] = rules.content[key]
        }
      })

      forNoRecords = [rule]
    }

    return this.apiResponse?.[currentDate]
      ? this.apiResponse[currentDate]
      : forNoRecords
  }
  private chartAssemblyRules(
    originSeries: any,
    finalSeries: any,
    currentDate: any,
    currentSerie: any
  ) {
    Object.keys(originSeries).forEach((field) => {
      const rules = originSeries[field]
      const hour = currentSerie.hour ? currentSerie.hour : '00:00:00'
      const event = new Date(`${currentDate} ${hour}`).getTime()
      const valueDefault = rules.valueDefault ? rules.valueDefault : rules.key
      const value =
        currentSerie[valueDefault] !== null
          ? currentSerie[valueDefault]
          : this.structure.originForNoRecords.content[valueDefault]
      const group = this.groupByRules(rules, currentSerie)

      const finalSeriesGroup = finalSeries[group]

      const definition: any = { event, value }

      if (finalSeriesGroup) {
        const structure: any = {}

        if (rules.format) {
          Object.keys(rules.format).forEach((item) => {
            structure[item] =
              definition[rules.format[item]] !== undefined ||
              definition[rules.format[item]] === null
                ? definition[rules.format[item]]
                : rules.format[item]
          })
        }

        const data = rules.format ? structure : [event, value]

        finalSeriesGroup.push(data)
      }

      finalSeries[group] = finalSeriesGroup
    })

    return finalSeries
  }
  private groupByRules(rules: any, currentSerie: any) {
    let group = rules.key
    const groupBy = rules.groupBy

    if (groupBy) {
      group = Array.isArray(groupBy)
        ? this.rulesForCombination(groupBy, currentSerie, rules)
        : this.rulesForOptions(groupBy, currentSerie, rules)
    }

    return group
  }
  private rulesForCombination(groupBy: any, currentSerie: any, rules: any) {
    const newRule: any = {
      group: null,
      valid: []
    }

    groupBy.forEach((child: any) => {
      const rule: any =
        currentSerie[child.field] === child.value ||
        (currentSerie[child.field] && child.value === 'length')

      newRule.group = rules.key

      newRule.valid.push(rule)
    })

    if (!newRule.valid.includes(false)) {
      return newRule.group
    }

    return null
  }
  private rulesForOptions(groupBy: any, currentSerie: any, rules: any) {
    let group = ''

    groupBy.values.forEach((rule: any) => {
      if (
        currentSerie[groupBy.field] === rule ||
        (currentSerie[groupBy.field] && rule === 'length')
      ) {
        group = rules.key
      }
    })

    return group
  }
  private graficMutation() {
    if (this.structure.chartOptions.series) {
      const limits: { min: number[][]; max: number[][] } = {
        min: [[], []] as number[][],
        max: [[], []] as number[][]
      }

      this.structure.chartOptions.series.forEach((serie: any) => {
        if (serie.yAxis !== null && serie.yAxis !== undefined) {
          serie.data.forEach((value: any) => {
            const val = Array.isArray(value) ? value[1] : value.y
            if (val !== null && val !== undefined) {
              limits.min[serie.yAxis].push(val)
              limits.max[serie.yAxis].push(val)
            }
          })
        }
      })

      const allValues = [...limits.min[0], ...limits.min[1]]
      if (allValues.length === 0) {
        return
      }

      const globalMin = Math.min(...allValues)
      const globalMax = Math.max(...allValues)

      const alignedMin = Math.floor(globalMin / 0.05) * 0.05 - 0.05
      const alignedMax = Math.ceil(globalMax / 0.05) * 0.05

      this.structure.chartOptions.yAxis.forEach((axis: any) => {
        axis.min = alignedMin
        axis.max = alignedMax
        axis.tickInterval = 0.05
        axis.startOnTick = false
        axis.endOnTick = false
      })
    }
  }

  private formatLabels() {
    if (!!this.structure.chartOptions && !!this.structure.chartOptions?.xAxis) {
      if (!this.structure.chartOptions?.xAxis?.labels) {
        this.structure.chartOptions.xAxis.labels = {}
      }
    }
  }
  private formatTooltip() {
    const chartOptions = this.structure.chartOptions

    if (!!chartOptions && !!chartOptions?.tooltip) {
      if (!chartOptions.tooltip?.headerFormat) {
        chartOptions.tooltip = {
          ...chartOptions.tooltip,
          headerFormat: '<b>{series.name}: <b>{point.y}</b><br/>'
        }
      }

      if (!chartOptions.tooltip?.pointFormatter) {
        const pointFormatter = chartOptions.tooltip.pointFormatter

        chartOptions.tooltip.pointFormatter = this.formatMoment(
          chartOptions,
          pointFormatter,
          this.timezone / -60
        )
      }

      this.structure.chartOptions = chartOptions
    }
  }
  private formatTitle() {
    const chartOptions = this.structure.chartOptions

    const textDefault = ''

    if (chartOptions?.title === undefined) {
      chartOptions.title = {
        text: textDefault
      }
      this.structure.chartOptions = chartOptions
      return
    }

    if (!!chartOptions?.title && chartOptions?.title?.text === null) {
      chartOptions.title.text = textDefault
    }

    this.structure.chartOptions = chartOptions
  }
  private formatTimezone() {
    if (!this.structure.chartOptions?.time) {
      return
    }

    const time = {
      timezoneOffset: this.timezone * -60
    }
    this.structure.chartOptions.time = time
  }
  private formatExportingFileName() {
    let chartOptions = this.structure.chartOptions

    /** const entityExportFileName = this.$props.entityName */

    if (!chartOptions?.exporting) {
      chartOptions = {
        ...chartOptions,
        exporting: {
          filename: DEFAULT_EXPORT_FILE_NAME
        }
      }
    }

    if (chartOptions?.exporting?.filename === ENTITY_NAME_PLACEHOLDER) {
      chartOptions.exporting.filename = 'entityExportFileName'
    }

    this.structure.chartOptions = chartOptions
  }

  /* UTILS */
  /** Formata a estrutura do evento */
  private formatMoment(chartOptions: any, pointFormatter: any, timezone: any) {
    return (pointFormatter = function (this: any) {
      if (this.x) {
        const date = new Date(this.x)
        const day = date.toLocaleString(
          chartOptions.lang ? chartOptions.lang : this.lang,
          { weekday: 'short', month: 'short', day: 'numeric' }
        )
        const H = date.getHours()
        const M = ('0' + date.getMinutes()).slice(-2)

        return `${day} às ${H}:${M}<br/>GMT ${timezone}`
      }
    })
  }
  /**
  private setTimezoneForSeriesResponse() {
    const val = this.apiResponse ?? {}
    this.timezone = (val?.[0]?.series?.[0]?.timezone ?? TIMEZONE_DEFAULT) * -60
  }
  private setTimezoneForDaysResponse() {
    const val = this.apiResponse ?? {}
    this.timezone = (val[Object.keys(val)[0]]?.[0]?.timezone ?? TIMEZONE_DEFAULT) * -60
  }
  */
}
