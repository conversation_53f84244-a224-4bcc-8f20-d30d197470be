import { AxiosError } from 'axios'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import NumericMeasurement from '@/@core/presentation/shared/NumericMeasurement'
import { ProgressBar } from '@/@core/presentation/shared/ui/progressBar'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicDemandService from '@/utils/dashboard/DynamicDemandService'

import { IStructureDynamicDemand, IWidgetProps } from '../page.types'

type IDataResponse = {
  values: {
    id: string
    title: string
    description: string
    value: number
    unit: string
    decimal: number | undefined
    dataType: string
  }[]
  bar: {
    used: {
      prefix: string
      value: string
      sufix: string
    }
    contract: {
      prefix: string
      value: string
      sufix: string
    }
    contractValue: number
    usedValue: number
    exceededValue: number
  }
}
const dataResponseInitial: IDataResponse = {
  values: [],
  bar: {
    used: {
      prefix: '',
      value: '',
      sufix: ''
    },
    contract: {
      prefix: '',
      value: '',
      sufix: ''
    },
    contractValue: 0,
    exceededValue: 0,
    usedValue: 0
  }
}
const _key = 'DynamicDemand'

const DashboardWidgetDynamicDemand = ({
  dataStructure,
  inputData,
  triggerRequest
}: IWidgetProps) => {
  const widgetData = dataStructure as IStructureDynamicDemand

  const isMounted = useRef(false)

  const [localData, setData] = useState<{
    isLoading: boolean
    response: IDataResponse
  }>({
    isLoading: true,
    response: dataResponseInitial
  })

  const abortControllers = useRef(new Map())

  const fetchData = async () => {
    try {
      setData((p) => ({ ...p, isLoading: true }))

      const abortController = new AbortController()
      abortControllers.current.set(_key, abortController)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(abortController.signal)

      const requests = widgetData.routes.map((route) => {
        const currentInputData = {
          ...inputData,
          // empresa_id: route?.empresa_id ?? inputData.empresa_id,
          empresa_id: inputData.empresa_id,
          horario_ponta: widgetData.peak
        }

        const rangeDate = currentInputData.date

        return DynamicDemandService.preparePromise(
          route,
          currentInputData,
          rangeDate,
          instanceHttp
        )
      })

      const result = await Promise.all(requests)

      const currentData: any = DynamicDemandService.parseResponse(
        { ...inputData },
        result,
        {
          routes: widgetData.routes,
          footer: widgetData.footer
        }
      )

      setData((p) => ({ ...p, isLoading: false, response: currentData }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicDemand error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    }
  }

  useEffect(() => {
    if (!isMounted.current) {
      fetchData()
      isMounted.current = true
      return () => { }
    }

    fetchData()

    return () => {
      abortControllers.current.size > 0 &&
        abortControllers.current.get(_key)?.abort()
    }
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <Skeleton />
      ) : (
        <>
          <p className="dashboard-widget-title">{widgetData.title}</p>
          <div className="min-h-[70px] flex justify-between mb-[8px] mx-1.5 dashboard-widget-content">
            {localData.response?.values?.map((data, dataIndex) => (
              <div
                key={`${data.title}-${dataIndex}`}
                className="flex-1 overflow-hidden"
              >
                <span className="text-comerc-grayDark-600 text-sm font-normal leading-6 dark:text-comerc-grayLight-500">
                  {data.title}
                </span>
                <span className="text-comerc-grayLight-600 block text-xs mb-1 dark:text-comerc-grayLight-400 h-2">
                  {data.description}
                </span>
                <NumericMeasurement
                  className="card-body-content-data-content-value"
                  classNameValue={data.dataType}
                  value={data.value}
                  unit="kW"
                  decimal={data.decimal}
                />
              </div>
            ))}
          </div>

          <div
            className={cn(
              'min-h-[16px] flex items-center justify-between mx-1.5 mb-[2px]',
              'duration-200'
            )}
          >
            <span className="text-xs text-comerc-grayLight-600 dark:text-comerc-grayLight-500">
              {localData.response.bar.used.prefix}{' '}
              {localData.response.bar.used.value}{' '}
              {localData.response.bar.used.sufix}
            </span>

            <span className="text-xs text-comerc-grayLight-600 dark:text-comerc-grayLight-500">
              {localData.response.bar.contract.prefix} /{' '}
              {localData.response.bar.contract.value}{' '}
              {localData.response.bar.contract.sufix}
            </span>
          </div>
          <div className={cn('min-h-[3px] flex mx-1.5', 'duration-200')}>
            <ProgressBar.Root>
              <ProgressBar.Main
                maxValue={localData.response.bar.contractValue}
                currentValue={localData.response.bar.usedValue}
              />
            </ProgressBar.Root>
          </div>
        </>
      )}
    </>
  )
}

export default DashboardWidgetDynamicDemand
