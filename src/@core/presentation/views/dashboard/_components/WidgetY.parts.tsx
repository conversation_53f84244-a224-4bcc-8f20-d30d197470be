import { ReactNode } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { IStructureDynamicWidgetHeadList } from '../page.types'

export const DynamicWidgetHeadContainer = ({
  hasHead,
  className,
  ...props
}: {
  hasHead: boolean
  className?: string
  children: ReactNode
}) => {
  return (
    <div
      className={cn(
        'flex items-center gap-1 [&>*]:text-xs justify-between',
        hasHead ? '[&>*]:p-1 mb-2' : 'h-0 overflow-hidden',
        className
      )}
      {...props}
    />
  )
}
interface HeadItemProps {
  dataStructure: any
  dynamicEvent: (p: any) => void
  headIndex: number
}
export const DynamicWidgetHeadItem = ({
  type,
  ...rest
}: HeadItemProps & { type: string }) => {
  if (type === 'span') {
    return <DynamicWidgetHeadSpan {...rest} />
  }
  if (type === 'dynamic') {
    return <DynamicWidgetHeadDynamic {...rest} />
  }
  if (type === 'DynamicIcon') {
    return <DynamicWidgetHeadIcon {...rest} />
  }
  if (type === 'DynamicFormGrid') {
    return <DynamicWidgetHeadFormGrid {...rest} />
  }
}
const DynamicWidgetHeadSpan = (
  props: HeadItemProps & {
    list?: IStructureDynamicWidgetHeadList[]
  }
) => {
  const { list } = props

  return (
    <div className="flex">
      {list?.map((el) => (
        <span key={el.spanTitle} className="dashboard-widget-title mb-0">
          {el.spanTitle}
        </span>
      ))}
    </div>
  )
}
const DynamicWidgetHeadDynamic = (props: HeadItemProps) => {
  return <div className="flex">DYNAMIC</div>
}
const DynamicWidgetHeadIcon = (props: HeadItemProps) => {
  return <div className="flex">ICON</div>
}
const DynamicWidgetHeadFormGrid = (props: HeadItemProps) => {
  const { dataStructure, headIndex, dynamicEvent } = props

  const handleInput = (field: object) => {
    try {
      dynamicEvent({ headIndex, field })
    } catch (error) {
      console.log(' ... handleInput', error)
    }
  }

  return (
    <>
      {dataStructure?.rules?.map((rule: any, ruleIndex: number) => (
        <div
          aria-description={`rule-key-${ruleIndex}`}
          key={`rule-key-${ruleIndex}`}
          className="flex flex-wrap items-center"
        >
          {rule?.title && <h2>{rule?.title}</h2>}
          {rule?.strong && <h3>{rule?.strong}</h3>}
          <FormField
            dataStructure={dataStructure}
            fieldsGroup={rule?.group}
            handleInput={handleInput}
          //  @valid="validForm"
          //  @selectOption="changeSelectOption"
          //  @changeText="changeSelectText"
          />
        </div>
      ))}
    </>
  )
}
const FormField = ({ fieldsGroup, handleInput, dataStructure }: any) => {
  return (
    <div className="flex flex-wrap items-center gap-1">
      {Array.isArray(fieldsGroup) &&
        fieldsGroup?.map((field, fieldIndex) => (
          <div key={`group-key-${fieldIndex}`} style={{ ...field?.style }}>
            {field?.before && (
              <span className="dashboard-widget-title mb-0">
                {field?.before}
              </span>
            )}

            {field?.fieldType === 'input' && (
              <Input.Root>
                {/* 
              {!!field?.label && (
                <Input.Label className="-mb-2 text-xs" id={field?.model}>
                  {field?.label}
                </Input.Label>
              )} 
              */}
                <Input.Content
                  value={dataStructure.values[field.model]}
                  onChange={({ target }) => {
                    handleInput({ key: field.model, value: target.value })
                  }}
                  id={field.model}
                  // v-model="dataStructure.values[field.model]"
                  name={field.model}
                  type={field?.type}
                  step={field?.step}
                  // :label="field.label"
                  // :placeholder="field.placeholder"
                  disabled={field.disabled}
                  className="min-w-[120px]"
                />
              </Input.Root>
            )}

            {field?.fieldType === 'select' && (
              <TagInput.Root className="min-w-[120px] max-w-28">
                {/* 
              {!!field?.label && (
                <TagInput.Label
                  className="-mb-2 truncate text-xs"
                  id={field?.model}
                  title={field?.label}
                >
                  {field?.label}
                </TagInput.Label>
              )} 
              */}
                <TagInput.Content
                  value={dataStructure.values[field.model]}
                  onChange={(value) => {
                    handleInput({
                      key: field.model,
                      value: value?.[0]?.value
                    })
                  }}
                  // id={field?.model}
                  name={field?.model}
                  // v-model="dataStructure.values[field.model]"
                  options={Array.isArray(field?.options) ? field?.options : []}
                  // :options="Array.isArray(field.options) ? field.options : []"
                  // :multiple="false"
                  // :removable="field.removable ? true : false"
                  // label={field?.label}
                  placeholder={field?.placeholder}
                  disabled={!!field?.disabled}
                // :searchable="field.search"
                // @search="changeText($event, field)"
                // @input="selectOption($event, field)"
                />
              </TagInput.Root>
            )}

            {field?.fieldType === 'toggle' && (
              <Switch.Content
                label={field.label}
                classNameLabel="text-sx min-w-[120px]"
                size="sm"
                labelPosition="end"
                checked={!!dataStructure.values[field.model]}
                onChange={(checked) => {
                  handleInput({ key: field.model, value: checked ? 1 : 0 })
                  // formFields.setValue('management', value ? 1 : 0)
                }}
                disabled={!!field?.disabled}
              />
            )}

            {field?.fieldType === 'checkbox' && (
              <div>
                <Checkbox.Root className="min-w-[120px]">
                  <Checkbox.Label htmlFor={field.label} className="text-xs">
                    {field.label}
                  </Checkbox.Label>
                  <Checkbox.Content
                    name={field.label}
                    onChange={(value) => {
                      console.log('Checkbox onChange', value)
                    }}
                    disabled={!!field?.disabled}
                  />
                </Checkbox.Root>
              </div>
            )}
          </div>
        ))}
    </div>
  )
}
