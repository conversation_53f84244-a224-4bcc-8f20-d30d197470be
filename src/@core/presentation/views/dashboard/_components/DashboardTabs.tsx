import React, { useEffect, useRef } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'

import { useContextPage } from '../page.context'
import { useStatePage } from '../page.hook'

export const DashboardTabs: React.FC = () => {
  const tabsRef = useRef<HTMLDivElement | null>(null)

  const statePage = useStatePage()
  const { selectTabItem } = useContextPage()

  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault()

      if (tabsRef.current?.scrollLeft)
        tabsRef.current.scrollLeft += event.deltaY
    }

    tabsRef.current?.addEventListener('wheel', handleWheel)
    return () => {
      tabsRef.current?.removeEventListener('wheel', handleWheel)
    }
  }, [])

  return (
    <div
      ref={tabsRef}
      className={cn('dashboard-tabs-container', {
        'min-h-[52px] mb-[32px]': statePage.page.tabs.length > 0
      })}
    >
      <div
        className="dashboard-tabs-bar"
        style={{ width: tabsRef.current?.scrollWidth || '100%' }}
      />
      {statePage.page.tabs.map((tab, i) => (
        <button
          key={`dashboard-tab-${tab.label}-${i}`}
          className={cn(
            'dashboard-tab',
            tab.label === statePage.page.tab?.label
              ? 'border-comerc-primary-700 text-comerc-primary-700'
              : 'border-transparent hover:border-b-[3px] hover:border-b-comerc-primary-700 hover:text-comerc-primary-700 '
          )}
          onClick={() => selectTabItem?.(tab)}
        >
          {tab.label}
        </button>
      ))}
    </div>
  )
}
