import { useEffect, useRef, useState } from 'react'

import { IAlarmsCustom } from '@/@core/domain/AlarmsCustom'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { alarmsCustomV3 } from '@/@core/infra/api/AlarmsCustomApiV3'
import { TdoSearchAlarmsCustom } from '@/@core/infra/api/AlarmsCustomApiV3/AlarmsCustomApiV3.types'
import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'

import { IWidgetProps } from '../page.types'

type ILocalData = {
  isLoading: boolean
  response: {
    items?: IAlarmsCustom[]
    total?: number
    page: number
    lastPage: number
  }
}
const DashboardWidgetDynamicAlarmes = ({
  inputData,
  triggerRequest,
  entityId
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isFetching = useRef(false)

  const systemLoading = useSystemLoadingStore()

  const [localData, setData] = useState<ILocalData>({
    isLoading: true,
    response: {
      items: [],
      total: 0,
      page: 1,
      lastPage: 0
    }
  })

  const fetchData = async (query: { page?: number } = {}) => {
    isFetching.current = true

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set('DynamicAlarmes', controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      const params: TdoSearchAlarmsCustom = {
        entity: inputData.instance,
        entityId: entityId,
        limit: 100,
        page: query?.page ?? localData.response.page
      }

      const result = await alarmsCustomV3(instanceHttp).get(params)

      setData((p) => ({
        ...p,
        isLoading: false,
        response: result.data
      }))
    } catch (error) {
      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get('DynamicAlarmes')?.abort()
  }, [])

  /** fetchData */
  useEffect(() => {
    if (isFetching.current) {
      abortControllers.current.get('DynamicAlarmes')?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <div>
          <Table.Root>
            <Table.Info>
              <Table.InfoTitle>Histórico de alarmes</Table.InfoTitle>
            </Table.Info>
            <Table.Header>
              <Table.Row>
                <Table.Head>ID</Table.Head>

                <Table.Head>Nome</Table.Head>
                <Table.Head>Tipo</Table.Head>
                <Table.Head>Periodicidade</Table.Head>
                <Table.Head>Disparo</Table.Head>
                <Table.Head>Valor do disparo</Table.Head>
                <Table.Head>Status</Table.Head>
              </Table.Row>
            </Table.Header>

            <Table.Body className="min-h-[500px]">
              {localData.response?.items?.map((alarmCustom) => (
                <Table.Row key={alarmCustom.id}>
                  <Table.Cell>{alarmCustom.id ?? ''}</Table.Cell>
                  <Table.Cell>{alarmCustom.name ?? ''}</Table.Cell>
                  <Table.Cell>{alarmCustom.ruleName ?? ''}</Table.Cell>
                  <Table.Cell>{alarmCustom.frequencyNotify ?? ''}</Table.Cell>
                  <Table.Cell>{alarmCustom.operatorTriggerId ?? ''}</Table.Cell>
                  <Table.Cell>{alarmCustom.valueTrigger ?? ''}</Table.Cell>
                  <Table.Cell>{alarmCustom.status ?? ''}</Table.Cell>
                </Table.Row>
              ))}

              <Table.RowLoading
                status={systemLoading.state.loading}
                colSpan={3}
              />
            </Table.Body>

            <Table.Mobile>
              {localData.response?.items?.map((alarmCustom) => (
                <TableMobile.Item key={alarmCustom.id}>
                  <TableMobile.Row>
                    <TableMobile.Cell>ID</TableMobile.Cell>
                    <TableMobile.Cell>{alarmCustom.id ?? ''}</TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Nome</TableMobile.Cell>
                    <TableMobile.Cell>
                      {alarmCustom.name ?? ''}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Tipo</TableMobile.Cell>
                    <TableMobile.Cell>
                      {alarmCustom.ruleName ?? ''}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Periodicidade</TableMobile.Cell>
                    <TableMobile.Cell>
                      {alarmCustom.frequencyNotify ?? ''}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Disparo</TableMobile.Cell>
                    <TableMobile.Cell>
                      {alarmCustom.operatorTriggerId ?? ''}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Valor do disparo</TableMobile.Cell>
                    <TableMobile.Cell>
                      {alarmCustom.valueTrigger ?? ''}
                    </TableMobile.Cell>
                  </TableMobile.Row>

                  <TableMobile.Row>
                    <TableMobile.Cell>Status</TableMobile.Cell>
                    <TableMobile.Cell>
                      {alarmCustom.status ?? ''}
                    </TableMobile.Cell>
                  </TableMobile.Row>
                </TableMobile.Item>
              ))}
            </Table.Mobile>

            <Table.Paginate
              status={systemLoading.state.loading}
              currentPage={localData.response.page}
              lastPage={localData.response.lastPage}
              handleChangePage={(page) => fetchData({ page })}
            />
          </Table.Root>
        </div>
      )}
    </>
  )
}

export default DashboardWidgetDynamicAlarmes
