import { AxiosError } from 'axios'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import NumericMeasurement from '@/@core/presentation/shared/NumericMeasurement'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DateUtilsService from '@/utils/dashboard/DateUtilsService'
import DynamicListCardService from '@/utils/dashboard/DynamicListCardService'

import { IStructureDynamicListCard, IWidgetProps } from '../page.types'

type IDataResponse = {
  data: { value: string; timestamp: number | null }
  decimal: number
  descriptionValue: string | null
  entity: string
  entity_name: string
  fieldLabel: string
  fieldValue: string
  property: string
  unit: string
}
type IRequestValue = {
  data: {
    timestamp: number
    value: string
    origem: string
  }[]
  entity: string
  entity_name: string
  property: string
}
type IRquestData = {
  series?: IRequestValue[]
  summary?: IRequestValue[]
}
type IRequest = {
  status: number
  data: IRquestData | null
}
type ILocalData = {
  widgetData: IStructureDynamicListCard
  isLoading: boolean
  response: IDataResponse[]
}
const _key = 'DynamicListCard'

const DashboardWidgetDynamicListCard = ({
  dataStructure,
  inputData,
  triggerRequest
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isFetching = useRef(false)

  const [localData, setData] = useState<ILocalData>({
    widgetData: dataStructure as IStructureDynamicListCard,
    isLoading: true,
    response: []
  })

  const fetchData = async () => {
    isFetching.current = true

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const currentInputData = { ...inputData }

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set(_key, controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      const routes = [...localData.widgetData.routes]

      const requests = routes.map(({ apiRoute }) =>
        DynamicListCardService.preparePromise(
          apiRoute,
          currentInputData,
          instanceHttp
        )
      )

      const results = await Promise.all(requests)

      const responses: IDataResponse[] = results.reduce(
        (acc: IDataResponse[], response: unknown, responseIndex: number) => {
          const currentResponse = response as IRequest
          const currentConfig = routes[responseIndex].config
          const { parseValues } = localData.widgetData

          const parsedData = DynamicListCardService.parseResponse(
            currentResponse,
            {
              config: currentConfig,
              toInteger: parseValues
            }
          )
          return [...acc, ...parsedData]
        },
        []
      )

      setData((p) => ({ ...p, isLoading: false, response: responses }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicListCard error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get(_key)?.abort()
  }, [])

  /** fetchData */
  useEffect(() => {
    if (isFetching.current && abortControllers.current.size > 0) {
      abortControllers.current.get(_key)?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  const formatDate = (value: string) => {
    return DateUtilsService.getDateFormat({
      value: value,
      gmt: inputData.timezone
    })
  }

  const isBehaviorHorizontal =
    localData.widgetData.behavior === 'horizontal-list'

  const isBehaviorVertical = localData.widgetData.behavior === 'vertical-list' //

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <>
          <p className="dashboard-widget-title">{localData.widgetData.title}</p>

          <div
            className={cn(
              'flex gap-4 p-2 overflow-auto',
              isBehaviorHorizontal ? 'items-center justify-between' : '',
              isBehaviorVertical ? 'flex-col justify-stretch' : ''
            )}
          >
            {localData.response.map((data, dataIndex) => (
              <div
                key={`${data.fieldLabel}-${dataIndex}`}
                className={cn({
                  'w-full': isBehaviorVertical
                })}
              >
                <div>
                  <span className="text-comerc-gray-800 text-xs font-semibold leading-6">
                    {data.fieldLabel}
                  </span>

                  <span className="text-comerc-grayLight-600 block text-xs mb-1">
                    {data.descriptionValue
                      ? formatDate(data.descriptionValue)
                      : ''}
                  </span>
                </div>

                <NumericMeasurement
                  className="w-full inline-block"
                  value={data.fieldValue}
                  unit={data.unit}
                  decimal={data.decimal}
                />
              </div>
            ))}
          </div>
        </>
      )}
    </>
  )
}

export default DashboardWidgetDynamicListCard
