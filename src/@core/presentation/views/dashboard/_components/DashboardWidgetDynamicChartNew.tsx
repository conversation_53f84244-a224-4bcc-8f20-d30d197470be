import { AxiosError } from 'axios'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { useEffect, useRef, useState } from 'react'

import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicChartNewService from '@/utils/dashboard/DynamicChartNewService'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { useLog } from '@/@core/logging/logger'
import {
  ISendCallback,
  ISendCallbackOptions
} from '@/@core/logging/logger.types'
import loggerRequest from '@/@core/logging/loggerRequest'
import { IStructureDynamicChartNew, IWidgetProps } from '../page.types'

type IDataResponse = {
  data: []
  entity: string
  entity_name: string
  property: string
  timezone: number
}
type IRequest = {}

type ILocalData = {
  isLoading: boolean
  response: IDataResponse[]
}

type SystemToast = {
  addToast: (args: { message: string; type: 'error' | 'success' }) => void
}

const _key = 'DynamicChartNew'

const DashboardWidgetDynamicChartNew = ({
  dataStructure: dataStructureProps,
  inputData,
  triggerRequest,
  yKey
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isFetching = useRef(false)

  const log = useLog()
  const systemToast = useSystemToastStore()

  const dataStructure = dataStructureProps as IStructureDynamicChartNew

  const [localData, setData] = useState<ILocalData>({
    isLoading: true,
    response: []
  })

  const fetchData = async () => {
    isFetching.current = true
    const rangeDate = null

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const currentInputData = { ...inputData }

      const controller = new AbortController()
      const signal = controller.signal

      abortControllers.current.set(_key, controller)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(signal)

      const request = DynamicChartNewService.preparePromise(
        dataStructure.apiRoutes,
        currentInputData,
        rangeDate,
        instanceHttp
      )

      const results: any = Array.from(await Promise.all(request)).map(
        (response: unknown, responseIndex: number) => {
          const currentResponse = response as IRequest

          return DynamicChartNewService.parseResponse(currentResponse, {
            inputData
            // config:
          })
        }
      )

      const responses = new ReadyChartNewResponse(
        results,
        dataStructure,
        currentInputData,
        log,
        systemToast,
        yKey
      ).exec()

      setData((p) => ({ ...p, isLoading: false, response: responses }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicChartNew error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get(_key)?.abort()
  }, [])

  /** fetchData */
  useEffect(() => {
    if (isFetching.current) {
      abortControllers.current.get(_key)?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <HighchartsReact highcharts={Highcharts} options={localData.response} />
      )}
    </>
  )
}

export default DashboardWidgetDynamicChartNew

/** const TIMEZONE_DEFAULT = -3 */
class ReadyChartNewResponse {
  private timezone: number
  private log: any
  private systemToast: SystemToast
  private yKey?: string

  constructor(
    private apiResponse: any,
    private structure: any,
    private inputData: any,
    log: {
      send: (callback: ISendCallback, options?: ISendCallbackOptions) => void
    },
    systemToast: SystemToast,
    yKey?: string
  ) {
    this.log = log
    this.yKey = yKey
    this.systemToast = systemToast
  }

  exec() {
    this.timezone = this.inputData.timezone

    if (!Object.keys(this.apiResponse).length) {
      return this.structure
    }

    const isForSeries =
      'series' in this.apiResponse ||
      (this.apiResponse[0] && this.apiResponse[0].series)

    if (isForSeries) {
      return this.responseForSeries()
    }
    if (Array.isArray(this.apiResponse)) {
      return this.responseForDate()
    }
    return this.responseForDays()
  }

  /** ForSeries */
  private responseForSeries() {
    const structure = this.structure
    const apiResponse = this.apiResponse
    const responses: any[] = []

    let keys = 0
    const series: any = {}
    /** this.setTimezoneForSeriesResponse()  */
    /** this.setTimezoneForDaysResponse()  */

    if (!Array.isArray(structure.apiRoutes)) {
      structure.apiRoutes = [structure.apiRoutes]
    }

    structure.apiRoutes.forEach((route: any, key: number) => {
      // route.response = apiResponse[key] ? apiResponse[key] : apiResponse
      responses[key] = apiResponse[key] ? apiResponse[key] : apiResponse
    })

    structure.apiRoutes.forEach((route: any, key: number) => {
      const finded = structure.chartOptions.series.find((serie: any) => {
        return serie.key === route.seriesKey
      })

      const newKey = !!(finded && route?.seriesKey) ? route?.seriesKey : key

      if (!series[newKey]) {
        series[newKey] = []
      }

      if (route.config && route.config === 'separate_series') {
        responses[key].series.forEach((children: any) => {
          children.source = route
          series[newKey].push(children)
        })
      } else if (
        structure.chartOptions &&
        structure.chartOptions.series &&
        structure.chartOptions.series.length === 1
      ) {
        if (structure.apiRoutes.config === 'series') {
          structure.chartOptions.series[0].data = this.getSeries(apiResponse)
        } else if (structure.apiRoutes.config === 'summary') {
          structure.chartOptions.series[0].data = this.getSummary(apiResponse)
        } else if (structure.apiRoutes.config === 'separate_series') {
          apiResponse.series.forEach((serie: any, key: number) => {
            structure.chartOptions.series[key] = {
              data: this.formatSeries(serie)
            }
            structure.chartOptions.series[key].name = serie.entity_name
            structure.chartOptions.series[key].property = serie.property
          })
        } else {
          structure.chartOptions.series[0].data = this.getSummary(apiResponse)
        }
      }
    })

    Object.keys(series).forEach((key) => {
      let finded = structure.chartOptions.series.find(
        (item: any) => item.key === key
      )

      if (finded) {
        const keySerie: any = {
          source: {},
          data: []
        }
        series[key].forEach((item: any) => {
          keySerie.source = item.source
          keySerie.data = this.formatSeries(item)

          // const list = this.formatSeries(item)

          // list.forEach((child: any) => {
          //   keySerie.data.push(child)
          // })
        })

        finded = this.getSourceSeries(finded, keySerie.source, keySerie)

        finded.data = keySerie.data.sort((itemA: any, itemB: any) => {
          return itemA[0] - itemB[0]
        })
      } else {
        series[key].forEach((item: any) => {
          if (!structure.chartOptions.series[keys]) {
            structure.chartOptions.series[keys] = {
              type:
                structure.chartOptions.chart &&
                structure.chartOptions.chart.type
                  ? structure.chartOptions.chart.type
                  : 'line'
            }
          }

          structure.chartOptions.series[keys] = this.getSourceSeries(
            structure.chartOptions.series[keys],
            item.source,
            item
          )

          const list = this.formatSeries(item).sort(
            (itemA: any, itemB: any) => itemA[0] - itemB[0]
          )

          structure.chartOptions.series[keys].data = list

          keys = keys + 1
        })
      }
    })

    return {
      ...this.structure?.chartOptions,
      chart: {
        ...this.structure?.chartOptions.chart,
        zoomType: 'x',
        backgroundColor: 'transparent'
      },
      time: {
        /** timezoneOffset: typeof this.timezone === 'number' ? this.timezone : TIMEZONE_DEFAULT * -60 */
        timezoneOffset: this.timezone * -60
      },
      title: {
        ...this.structure.chartOptions.title,
        text: ' '
      },
      legend: {
        ...this.structure?.chartOptions?.legend,
        enabled: true,
        padding: 5
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: true,
        buttons: {
          contextButton: {
            menuItems: [
              'viewData',
              'viewFullscreen',
              'printChart',
              'separator',
              'downloadPNG',
              'downloadJPEG',
              'downloadPDF',
              'downloadSVG',
              'separator',
              'downloadCSV',
              'downloadXLS'
            ]
          }
        },
        ...this.structure?.chartOptions?.exporting
      },
      lang: {
        contextButtonTitle: 'Menu de exportação do gráfico',
        viewFullscreen: 'Ver em tela cheia',
        printChart: 'Imprimir gráfico',
        downloadPNG: 'Baixar em PNG',
        downloadJPEG: 'Baixar em JPEG',
        downloadPDF: 'Baixar em PDF',
        downloadSVG: 'Baixar em SVG',
        viewData: 'Ver dados da tabela',
        hideData: 'Esconder tabela',
        downloadCSV: 'Baixar CSV',
        downloadXLS: 'Baixar XLS',
        noData: 'Sem dados'
      }
    }
  }
  private getSourceSeries(finded: any, sourceSerie: any, item: any) {
    if (sourceSerie) {
      if ('yAxis' in sourceSerie) {
        finded.yAxis = sourceSerie.yAxis
      }
      if ('xAxis' in sourceSerie) {
        finded.xAxis = sourceSerie.xAxis
      }

      finded.name = finded.name ? finded.name : item.entity_name
      finded.property = item.property
    }
    return finded
  }
  private formatSeries(source: any) {
    const serieList: any = []

    source.data.forEach((serie: any) => {
      let { value } = serie

      if (typeof value === 'boolean') {
        value = value ? 1 : 0
      }

      if (serie.timestamp) {
        serieList.push([serie.timestamp * 1000, value])
      }
    })

    return serieList
  }
  private getSeries(apiResponse: any) {
    const series: any = []
    apiResponse = Array.isArray(apiResponse) ? apiResponse[0] : apiResponse
    apiResponse.series.forEach((item: any) => {
      item.data.forEach((serie: any) => {
        if (serie.timestamp) {
          series.push([serie.timestamp * 1000, serie.value])
        }
      })
    })
    return series
  }
  private getSummary(apiResponse: any) {
    const summary: any = []

    apiResponse = Array.isArray(apiResponse) ? apiResponse[0] : apiResponse

    apiResponse.summary.forEach((summaryItem: any) => {
      if (summaryItem.data) {
        summary.push([
          summaryItem.data.timestamp * 1000,
          summaryItem.data.value
        ])
      }
    })
    return summary
  }

  /** ForData */
  private responseForDate() {
    this.log.send(loggerRequest, {
      error: new Error('responseForDate not implemented'),
      title:
        'DashboardWidgetDynamicChartNew.responseForDate - src/@core/presentation/views/dashboard/_components/DashboardWidgetDynamicChartNew.tsx',
      content: {
        yKey: this.yKey,
        inputData: this.inputData
      }
    })

    this.systemToast.addToast({
      message: 'Ocorreu um erro ao processar os dados do gráfico',
      type: 'error'
    })
    return []
  }

  /** ForDays */
  private responseForDays() {
    this.log.send(loggerRequest, {
      error: new Error('responseForDays not implemented'),
      title:
        'DashboardWidgetDynamicChartNew.responseForDays - src/@core/presentation/views/dashboard/_components/DashboardWidgetDynamicChartNew.tsx',
      content: {
        yKey: this.yKey,
        inputData: this.inputData
      }
    })

    this.systemToast.addToast({
      message: 'Ocorreu um erro ao processar os dados do gráfico',
      type: 'error'
    })
    return []
  }

  /** UTILS */
  /**
   private setTimezoneForSeriesResponse() {
     const val = this.apiResponse ?? {}
     // Converte horas para minutos já multiplicando por -60
     this.timezone = (val?.[0]?.series?.[0]?.timezone ?? TIMEZONE_DEFAULT) * -60
   }
   private setTimezoneForDaysResponse() {
     const val = this.apiResponse ?? {}
     // Converte horas para minutos já multiplicando por -60
     this.timezone =
       (val[Object.keys(val)[0]]?.[0]?.timezone ?? TIMEZONE_DEFAULT) * -60
   }
   */
}
