import { AxiosError } from 'axios'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import NumericMeasurement from '@/@core/presentation/shared/NumericMeasurement'
import { Skeleton } from '@/@core/presentation/shared/ui/skeleton'
import DynamicIntakeService from '@/utils/dashboard/DynamicIntakeService'

import { IStructureDynamicIntake, IWidgetProps } from '../page.types'

type IDataResponse = {
  date: string
  value: number
  unit: string
  decimal: number | undefined
  dataType: string
}
type IRequest = {}
type ILocalData = {
  widgetData: IStructureDynamicIntake
  isLoading: boolean
  response: IDataResponse[]
}
let _key = 'DynamicIntake'
const DashboardWidgetDynamicIntake = ({
  dataStructure,
  inputData,
  triggerRequest
}: IWidgetProps) => {
  const abortControllers = useRef(new Map())
  const isFetching = useRef(false)

  const [localData, setData] = useState<ILocalData>({
    widgetData: dataStructure as IStructureDynamicIntake,
    isLoading: true,
    response: []
  })

  const fetchData = async () => {
    isFetching.current = true

    try {
      setData((p) => ({ ...p, isLoading: true }))

      const abortController = new AbortController()
      abortControllers.current.set(_key, abortController)

      const instanceHttp = new Http()
      instanceHttp.setToken(memory.cookie.get().auth.token)
      instanceHttp.setSignal(abortController.signal)

      const routes = [...localData.widgetData.routes]

      const requests = routes.map(({ apiRoute, config }) => {
        const currentInputData = {
          ...inputData,
          peak_time: config.peakTime
        }
        return DynamicIntakeService.preparePromise(
          apiRoute,
          currentInputData,
          instanceHttp
        )
      })

      const responses = Array.from(await Promise.all(requests)).map(
        (response: unknown, responseIndex: number) => {
          const currentResponse = response as IRequest

          const currentConfig = routes[responseIndex].config

          return DynamicIntakeService.parseResponse(currentResponse, {
            config: currentConfig,
            inputData
          })
        }
      )

      setData((p) => ({ ...p, isLoading: false, response: responses }))
    } catch (error) {
      const { code } = error as AxiosError

      if (code !== 'ERR_CANCELED' && process.env.NODE_ENV === 'development')
        console.log('... DynamicIntake error:', error)

      setData((p) => ({ ...p, isLoading: false }))
    } finally {
      isFetching.current = false
    }
  }

  /** abort request when unmounted */
  useEffect(() => {
    return () => abortControllers.current.get(_key)?.abort()
  }, [])

  /** fetchData */
  useEffect(() => {
    if (isFetching.current && abortControllers.current.size > 0) {
      abortControllers.current.get(_key)?.abort()
    }
    fetchData()
  }, [inputData.final_date, inputData.initial_date, triggerRequest])

  return (
    <>
      {localData.isLoading ? (
        <>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-36" />
        </>
      ) : (
        <div className="flex flex-col gap-y-4 dashboard-widget-content">
          <p className="dashboard-widget-title">{localData.widgetData.title}</p>
          <div className={cn('p-2', {})}>
            {localData.response.map((data, dataIndex) => (
              <div key={`${data.date}-${dataIndex}`}>
                <div>
                  <span className="text-comerc-grayLight-500 block text-xs dark:text-comerc-grayLight-400 h-2">
                    {data.date}
                  </span>
                </div>
                <NumericMeasurement
                  className="w-full inline-block"
                  classNameValue={data.dataType}
                  value={data.value}
                  unit={data.unit}
                  decimal={data.decimal}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  )
}

export default DashboardWidgetDynamicIntake
