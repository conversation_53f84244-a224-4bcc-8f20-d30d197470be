import { IDashboardTab } from '@/@core/domain/DashboardTabs'
import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import { createContext, ReactNode, useContext } from 'react'

type IContextPage = {
  /** MENU */
  dynamicEvent?: (p: { xKey: string; field: object }) => void
  handleToggleMenuMobile?: () => void
  handleInputSearch?: (p: string) => void
  handleInputAccount?: (p: {
    accountId: number
    inputAccount: { id: number; name: string }
  }) => Promise<
    | {
        reload: boolean
      }
    | undefined
  >
  clearMenuItems?: () => void
  selectMenuItem?: (p: IMenuDashboard, parent?: IMenuDashboard) => void
  /** PAGE */
  handleReloadWidgets?: () => void
  openCreateDashboardModal?: () => void
  openDeleteDashboardModal?: () => void
  selectTabItem?: (tab: IDashboardTab) => void
}
export const ContextPage = createContext<IContextPage>({})

export const ContextPageProvider = ({
  children,
  value
}: {
  children: ReactNode
  value: IContextPage
}) => {
  return <ContextPage.Provider value={value}>{children}</ContextPage.Provider>
}

export const useContextPage = () => useContext(ContextPage)
