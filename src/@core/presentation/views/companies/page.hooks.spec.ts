import { act, renderHook, waitFor } from '@testing-library/react'

import { companyMock1 } from '@/__mock__/content/api-companies.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  useLanguageCompaniesPage,
  useMethodCompaniesPage,
  useStateCompaniesPage
} from './page.hooks'

jest.mock('@/@core/infra/api/CompaniesApiV4/CompaniesApiV4')
const spyCompaniesApiV4 = jest.spyOn(
  require('@/@core/infra/api/CompaniesApiV4/CompaniesApiV4'),
  'companiesApiV4'
)

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

describe('@core/presentation/views/companies/page.hooks | useLanguageCompaniesPage', () => {
  it('should return the correct language page', () => {
    const { result } = renderHook(() => useLanguageCompaniesPage(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.page.title).toBe('Empresas')
  })
})

describe('@core/presentation/views/companies/page.hooks | useStateCompaniesPage', () => {
  it('should initialize with the correct state', () => {
    const { result } = renderHook(() => useStateCompaniesPage(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.items).toEqual([])
    expect(result.current.lastPage).toEqual(0)
    expect(result.current.total).toEqual(0)
  })

  it('should update the state correctly when calling set', () => {
    const { result } = renderHook(() => useStateCompaniesPage(), {
      wrapper: AppStoreProvider
    })

    waitFor(() => result.current.reset())

    act(() => {
      result.current.set({
        items: [
          {
            id: 1,
            accountName: 'accountName',
            name: 'name',
            parentName: 'parentName',
            stateName: 'stateName',
            type: ''
          }
        ],
        lastPage: 1,
        total: 1
      })
    })

    expect(result.current.items).toEqual([
      {
        id: 1,
        accountName: 'accountName',
        name: 'name',
        parentName: 'parentName',
        stateName: 'stateName',
        type: ''
      }
    ])
    expect(result.current.lastPage).toEqual(1)
    expect(result.current.total).toEqual(1)
  })

  it('should reset the state correctly when calling reset', async () => {
    const { result } = renderHook(() => useStateCompaniesPage(), {
      wrapper: AppStoreProvider
    })

    act(() => {
      result.current.set({
        items: [
          {
            id: 1,
            accountName: 'accountName',
            name: 'name',
            parentName: 'parentName',
            stateName: 'stateName',
            type: ''
          }
        ],
        lastPage: 1,
        total: 1
      })
    })

    await result.current.reset()

    expect(result.current.items).toEqual([])
    expect(result.current.lastPage).toEqual(0)
    expect(result.current.total).toEqual(0)
  })
})

describe('@core/presentation/views/companies/page.hooks | useMethodCompaniesPage', () => {
  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      pathname: '/companies',
      query: ''
    }))
  })

  it('should exec method getData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateCompaniesPage(),
        method: useMethodCompaniesPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* getData with error **/
    spyCompaniesApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData successful without data **/
    spyCompaniesApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData with success **/
    spyCompaniesApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [companyMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(1)
    expect(result.current.state.total).toBe(1)
    expect(result.current.state.lastPage).toBe(1)
  })

  it('should exec method handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateCompaniesPage(),
        method: useMethodCompaniesPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request with error **/
    spyCompaniesApiV4.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })

    /* request successful without data **/
    spyCompaniesApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 404
      }),
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [companyMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })

    /* request success **/
    spyCompaniesApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      }),
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [companyMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })
  })
})
