import { useCallback } from 'react'
import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'

import { memory } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

import { PageLanguage } from './language'
import { stateData } from './page.content'
import { IState } from './page.types'
import { parseCompanyData } from './page.utils'

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => set((state) => ({ ...state, ...initialData })),
  reset: () => set((state) => ({ ...state, ...stateData }))
}))

export const useStateCompaniesPage = () => {
  const state = statePage()
  return { ...state }
}

export const useMethodCompaniesPage = () => {
  const statePage = useStateCompaniesPage()
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()
  const languagePage = PageLanguage()

  const getData = useCallback(async () => {
    try {
      systemLoading.setData({ loading: true })

      statePage.reset()

      const { search } = memory.local.get().companies.listing

      const result = await companiesApiV4(http).get({
        ...search,
        externalId: search.externalId?.map(Number),
        accountId: search?.accountId?.[0]?.id
      })

      if (result.status === 204) {
        systemLoading.setData({ loading: false })
        return
      }

      statePage.set({
        items: result.data.items.map(parseCompanyData),
        lastPage: result.data.lastPage,
        total: result.data.total
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: `Erro no getData empresa - @core/presentation/views/companies/page.hooks.ts`
      })
    } finally {
      systemLoading.setData({ loading: false })
    }
  }, [])

  const handleDelete = async (id: number) => {
    let success = false
    try {
      systemLoading.setData({ loading: true })

      await companiesApiV4(http).delete(id)

      systemToast.addToast({
        message: languagePage.page.table.modalDelete.deleteSuccessMessage
      })

      await getData()

      success = true
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: `Erro ao deletar empresa - @core/presentation/views/companies/page.hooks.ts`
      })

      systemToast.addToast({
        message: languagePage.page.table.modalDelete.deleteErrorMessage,
        type: 'error'
      })
      success = false
    } finally {
      systemLoading.setData({ loading: false })
    }
    return success
  }

  return { getData, handleDelete }
}

export const useLanguageCompaniesPage = () => {
  const { lang } = useSystemLanguageStore().state

  const { pages, table } = languageByMode(lang)

  return {
    page: {
      ...pages.companies
    },
    table: {
      ...pages.companies.table,
      ...table
    }
  }
}
