import { useRouter } from 'next/router'
import { FC, useState } from 'react'

import { mapColorsCompanyType } from '@/@core/content/mapColorsCompanyType.content'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useCompaniesTypesStore from '@/@core/framework/store/hook/useListCompaniesTypesStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { accountsApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import {
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { handleKeyEnter } from '@/@core/utils/handleInputSearch'
import { mergeSortOrderData } from '@/@core/utils/handleSorteColumn'
import { useDebounceFunction } from '@/hooks/useDebouce'

import {
  useLanguageCompaniesPage,
  useMethodCompaniesPage,
  useStateCompaniesPage
} from '../page.hooks'
import { ICompaniesPage } from '../page.types'

export const CompaniesTable = () => {
  const router = useRouter()

  const statePage = useStateCompaniesPage()
  const methodPage = useMethodCompaniesPage()
  const languagePage = useLanguageCompaniesPage()
  const listCompaniesTypesStore = useCompaniesTypesStore()

  const permissions = useSystemStore().state.permissions
  const systemLoading = useSystemLoadingStore()

  const searchFields = memory.local.get().companies.listing.search

  const handleSortColumn = async (props: { key: 'id' | 'nome' }) => {
    const { sort, order } = mergeSortOrderData(props.key, searchFields)

    memory.local.set({
      companies: { listing: { search: { order, sort } } }
    })
    await methodPage.getData()
  }
  const handleInput = useDebounceFunction(() => {
    methodPage.getData()
  }, 250)

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languagePage.page.title}</Table.InfoTitle>

        <Table.InfoBadge className="lg:mr-auto">
          {statePage.total}
          <span className="hidden md:inline-block ml-1">
            {languagePage.table.totalRegisters}
          </span>
        </Table.InfoBadge>

        <TagInput.Root className="min-w-[225px]">
          <TagInput.Content
            name="inputType"
            placeholder={languagePage.table.search.fieldType}
            value={searchFields?.type ?? ''}
            onChange={(items) => {
              memory.local.set({
                companies: {
                  listing: {
                    search: { type: items?.[0]?.value ?? '', page: 1 }
                  }
                }
              })
              handleInput()
            }}
            options={[
              { value: '', label: 'Todos' },
              ...listCompaniesTypesStore.state.list.map((el) => ({
                value: String(el.value),
                label: el.name
              }))
            ]}
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>

        <TagInput.Root className="min-w-[225px]">
          <TagInput.ContentApi
            defaultValue={formatInputValues(searchFields?.accountId ?? [])}
            onChange={(values) => {
              memory.local.set({
                companies: {
                  listing: { search: { accountId: formatOutputValues(values) } }
                }
              })
              handleInput()
            }}
            featchData={(args) => accountsApiV3(http).get({ ...args })}
            placeholder={languagePage.table.search.fieldAccounts}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Content
            slotStart={
              <Icon
                icon="searchLg"
                className="icon-menu-primary"
                height="24"
                width="24"
                viewBox="0 0 20 20"
              />
            }
            placeholder={languagePage.table.search.fieldQuery}
            type="text"
            defaultValue={searchFields?.q ?? ''}
            onChange={(e) => {
              memory.local.set({
                companies: {
                  listing: { search: { q: e.target.value, page: 1 } }
                }
              })
            }}
            onKeyUp={(e) => handleKeyEnter(e.key, methodPage.getData)}
          />
        </Input.Root>

        <Table.InfoNewRegister
          onClick={() => router.push('/companies/new')}
          permission={permissions['register-company']?.create}
        />
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head onClick={() => handleSortColumn({ key: 'id' })}>
            {languagePage.table.columns.id}
            <Table.CellIcon
              field="id"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head onClick={() => handleSortColumn({ key: 'nome' })}>
            {languagePage.table.columns.name}
            <Table.CellIcon
              field="nome"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head>{languagePage.table.columns.account}</Table.Head>
          <Table.Head>{languagePage.table.columns.parent}</Table.Head>
          <Table.Head>{languagePage.table.columns.state}</Table.Head>
          <Table.Head>{languagePage.table.columns.type}</Table.Head>
          <Table.Head>{languagePage.table.columns.actions}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.items.map((company) => (
          <Table.Row key={company.id}>
            <Table.Cell>{company.id}</Table.Cell>
            <Table.Cell>{company.name}</Table.Cell>
            <Table.Cell>{company.accountName}</Table.Cell>
            <Table.Cell>{company.parentName}</Table.Cell>
            <Table.Cell>{company.stateName}</Table.Cell>
            <Table.Cell>
              <Badge.Root
                className={cn(
                  'cell-company-type',
                  mapColorsCompanyType[company.type ?? 'default']
                )}
              >
                <Badge.Content>{company.type ?? '-'}</Badge.Content>
              </Badge.Root>
            </Table.Cell>
            <Table.Cell width={80} role="td-actions">
              <Actions company={company} />
            </Table.Cell>
          </Table.Row>
        ))}

        <Table.RowLoading status={systemLoading.state.loading} colSpan={6} />
      </Table.Body>

      <Table.Mobile>
        {statePage.items.map((company) => (
          <TableMobile.Item key={company.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.id}
              </TableMobile.Cell>
              <TableMobile.Cell>{company.id}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.name}
              </TableMobile.Cell>
              <TableMobile.Cell>{company.name}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.account}
              </TableMobile.Cell>
              <TableMobile.Cell>{company.accountName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.parent}
              </TableMobile.Cell>
              <TableMobile.Cell>{company.parentName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.state}
              </TableMobile.Cell>
              <TableMobile.Cell>{company.stateName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.type}
              </TableMobile.Cell>
              <TableMobile.Cell>
                <Badge.Root
                  className={cn(
                    'cell-company-type min-w-20',
                    mapColorsCompanyType[company.type ?? 'default']
                  )}
                >
                  <Badge.Content>{company.type ?? '-'}</Badge.Content>
                </Badge.Root>
              </TableMobile.Cell>
            </TableMobile.Row>

            <TableMobile.Footer>
              <Actions company={company} />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={statePage.lastPage}
        currentPage={searchFields.page}
        handleChangePage={(page) => {
          memory.local.set({
            companies: {
              listing: { search: { page } }
            }
          })
          methodPage.getData()
        }}
      />
    </Table.Root>
  )
}

const Actions: FC<{ company: ICompaniesPage }> = ({ company }) => {
  const permissionStore = useSystemStore().state.permissions
  const router = useRouter()

  return (
    <div className="table-td-actions">
      <ModalDelete companyId={company.id} companyName={company.name} />

      {permissionStore?.['register-company']?.create && (
        <button
          className="table-td-action hover:cursor-pointer"
          onClick={() => router.push(`/companies/${company.id}`)}
        >
          <Icon
            icon="edit"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      )}
    </div>
  )
}

interface ModalDeleteProps {
  companyId: number
  companyName: string
}
const ModalDelete: FC<ModalDeleteProps> = ({ companyName, companyId }) => {
  const [openDialog, setOpenDialog] = useState(false)
  const languagePage = useLanguageCompaniesPage()
  const methodPage = useMethodCompaniesPage()

  const handleClickConfirm = async () => {
    const result = await methodPage.handleDelete(companyId)
    if (result) setOpenDialog(false)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]">
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>{languagePage.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>

        <Dialog.Description>
          {languagePage.table.modalDelete.textInfo}
          <span className="text-red-600"> {companyName}</span>
        </Dialog.Description>

        <Dialog.Footer className="flex justify-end gap-3">
          <Button
            variant="secondary-gray"
            type="button"
            onClick={() => setOpenDialog(false)}
          >
            {languagePage.table.modalDelete.textCancel}
          </Button>

          <Button
            variant="error-primary"
            type="button"
            onClick={handleClickConfirm}
          >
            {languagePage.table.modalDelete.textConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
