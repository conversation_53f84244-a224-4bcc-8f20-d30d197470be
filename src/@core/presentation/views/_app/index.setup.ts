import { useRouter } from 'next/router'
import { useEffect, useRef } from 'react'

import { observer } from '@/@core/domain/observer'
import {
  loadingObserver,
  redirectObserver,
  toastAddObserver
} from '@/@core/domain/observer/AppObserver'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemThemeStore from '@/@core/framework/store/hook/useSystemThemeStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { memory } from '@/@core/infra/memory'
import { redirectLoginCognito } from '@/@core/utils/redirectLoginCognito'

export const SetupObserver = () => {
  const router = useRouter()
  const systemLoading = useSystemLoadingStore()
  const toastStore = useSystemToastStore()

  useEffect(() => {
    observer.subscribe(
      loadingObserver((p) => systemLoading.setData({ loading: p }))
    )
    observer.subscribe(toastAddObserver((p) => toastStore.addToast(p)))
    observer.subscribe(redirectObserver((p) => router.push(p)))

    return () => {
      observer.unsubscribe(redirectObserver)
      observer.unsubscribe(loadingObserver)
    }
  }, [])

  return null
}

export const SetupkeyDown = () => {
  const isMounted = useRef<boolean>(false)
  const themeStore = useSystemThemeStore()
  const languageStore = useSystemLanguageStore()

  const keyDownTheme = () => {
    const oldTheme = memory.local.get()?.system?.theme

    const currentTheme = oldTheme === 'light' ? 'dark' : 'light'

    memory.local.set({
      system: { theme: currentTheme }
    })
    themeStore.setTheme(currentTheme) // talvez nao precisa

    document.body.classList.replace(oldTheme, currentTheme)
  }

  const keyDownLanguage = () => {
    let currentMode = memory.local.get()?.system?.language

    currentMode = currentMode === 'en' ? 'pt-BR' : 'en'

    memory.local.set({
      system: { language: currentMode }
    })
    languageStore.setLanguage(currentMode)
  }

  useEffect(() => {
    if (!isMounted.current) {
      const { theme, language } = memory.local.get().system

      document.body.classList.add(theme)

      themeStore.setTheme(theme)
      languageStore.setLanguage(language)
    }

    const keyDown = (ev: KeyboardEvent) => {
      if (ev.altKey && ev.code === 'KeyT') keyDownTheme()
      if (ev.altKey && ev.code === 'KeyL') keyDownLanguage()
    }

    document.body.addEventListener('keydown', keyDown)

    return () => {
      document.body.removeEventListener('keydown', keyDown)
    }
  }, [])

  return null
}

export const SetupVisibility = () => {
  const router = useRouter()

  const handler = () => {
    const isVisible = document.visibilityState === 'visible'
    const hasToken = !!memory.cookie.get().auth.token

    if (isVisible) return

    if (hasToken) return

    router.push(redirectLoginCognito())
  }

  useEffect(() => {
    document.addEventListener('visibilitychange', handler)

    return () => {
      document.removeEventListener('visibilitychange', handler)
    }
  }, [])

  return null
}
