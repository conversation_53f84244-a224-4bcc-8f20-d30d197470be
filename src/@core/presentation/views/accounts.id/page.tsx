import { useEffect } from 'react'

import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import { HeaderList } from '@/@core/presentation/shared/pages/HeaderList'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'
import { Tabs } from '@/@core/presentation/shared/ui/tabs'

import { TabData } from './TabData/Tab'
import { TabManagement } from './TabManagement/Tab'
import { TabModules } from './TabModules/Tab'
import { TabPersonalization } from './TabPersonalization/Tab'
import { TabSettings } from './TabSettings/Tab'

import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'
import { Icon } from '../../shared/ui/icons'
import { useLanguagePage, useStatePage } from './page.hooks'

export const Page = () => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  useBreadcrumb('accounts.id')
  useTitlePage(languagePage.page.title)

  useEffect(() => {
    return () => {
      statePage.reset()
    }
  }, [])

  return (
    <PageContent>
      <Breadcrumbs />
      <HeaderList.Root>
        <HeaderList.Content
          title={languagePage.page.title}
          className="mr-auto"
        />
      </HeaderList.Root>

      <Tabs.Root
        variant="primary"
        className="lg:flex items-start gap-2"
        value={statePage.tab.active}
        onValueChange={statePage.setTab}
      >
        <Tabs.List className="mb-3 lg:flex-col lg:pr-2 lg:mr-6 lg:min-w-[177px] lg:min-h-[350px] justify-start">
          <Tabs.Trigger className="flex gap-2" value="data">
            <Icon
              icon="clipboard"
              width="18"
              height="18"
              fill="none"
              className="icon-menu-primary"
              strokeWidth="2"
              viewBox="0 0 24 24"
            />
            {languagePage.page.tabs.data}
          </Tabs.Trigger>

          <Tabs.Trigger value="modules" disabled={!statePage.isEdit}>
            {languagePage.page.tabs.entities}
          </Tabs.Trigger>

          <Tabs.Trigger
            value="personalization"
            disabled={!statePage.isEdit}
            className="hidden"
          >
            {languagePage.page.tabs.personalization}
          </Tabs.Trigger>

          <Tabs.Trigger value="settings" disabled={!statePage.isEdit}>
            {languagePage.page.tabs.settings}
          </Tabs.Trigger>

          <Tabs.Trigger
            value="management"
            disabled={
              !statePage.isEdit || !statePage.tabData.account.management
            }
          >
            {languagePage.page.tabs.management}
          </Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="data" className="w-full">
          <TabData />
        </Tabs.Content>

        <Tabs.Content value="modules" className="w-full">
          <TabModules />
        </Tabs.Content>

        <Tabs.Content value="personalization" className="w-full">
          <TabPersonalization />
        </Tabs.Content>

        <Tabs.Content value="settings" className="w-full">
          <TabSettings />
        </Tabs.Content>

        <Tabs.Content value="management" className="w-full">
          <TabManagement />
        </Tabs.Content>
      </Tabs.Root>
    </PageContent>
  )
}
