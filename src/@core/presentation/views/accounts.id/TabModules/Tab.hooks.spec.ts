import { renderHook, waitFor } from '@testing-library/react'

import { accountMock1 } from '@/__mock__/content/api-accounts.content'
import {
  moduleMock1,
  moduleMock2,
  moduleMock3,
  moduleMock4,
  moduleMock5
} from '@/__mock__/content/api-modules.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStatePage } from '../page.hooks'
import { useMethodTabModules } from './Tab.hooks'
import { IDataSend } from './Tab.types'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ModulesApiV3')
const spyModulesApiV3 = jest.spyOn(
  require('@/@core/infra/api/ModulesApiV3'),
  'modulesApiV3'
)
jest.mock('@/@core/infra/api/AccountsApiV3')
const spyAccountsApiV3 = jest.spyOn(
  require('@/@core/infra/api/AccountsApiV3'),
  'accountsApiV3'
)

describe('@core/presentation/views/accounts.id/TabModules/Tab.hooks', () => {
  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      query: { id: 123 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabModules(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request error **/
    await waitFor(() => result.current.state.reset())

    spyModulesApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request successful without data **/
    await waitFor(() => result.current.state.reset())

    spyModulesApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.tabModules.modules).toHaveLength(0)

    /* request success **/
    await waitFor(() => result.current.state.reset())

    spyModulesApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [
            moduleMock1,
            moduleMock2,
            moduleMock3,
            moduleMock4,
            moduleMock5
          ]
        }
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    const modules = result.current.state.tabModules.modules

    expect(modules[0].id).toBe(moduleMock1.id)
    expect(modules[0].name).toBe(moduleMock1.name)
    expect(modules[0].checked).toBeFalsy()
    expect(modules).toHaveLength(2)
  })

  it('should check return the function onSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabModules(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )
    await waitFor(() =>
      result.current.state.setTabData({ account: { id: 1, modules: [] } })
    )

    const payload: IDataSend = {
      id: 1,
      name: 'name 1',
      accountStatusId: 1,
      moduleIds: [2, 3]
    }

    /* request error **/
    spyAccountsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.account.modules).toHaveLength(0)

    /* request CREATE success **/
    spyAccountsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValue({
        status: 201,
        data: accountMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.account.modules).toHaveLength(3)
  })
})
