import React, { FC } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { PageSection } from '@/@core/presentation/shared/pages'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Icon as IconOld, faCheck } from '@/@core/presentation/shared/ui/icon'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { changeColorsThemeByElement } from '@/@core/utils'
import { ThemeColorType } from '@/types/theme'

import { useLanguagePage, useStatePage } from '../../page.hooks'

export const SectionPersonalization: FC = () => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage().tabPersonalization

  const onChangeColorThemeDemo = (value: ThemeColorType) => {
    changeColorsThemeByElement(value, '.playgroundColor-theme')

    statePage.setTabPersonalization({
      colorTheme: value
    })
  }

  const onChangeColorTheme = () => {
    if (!statePage.editOwnAccount) return

    changeColorsThemeByElement(statePage.tabPersonalization.colorTheme, 'html')

    // memory.local.set({
    //   system: {
    //     themeColor: statePage.tabPersonalization.colorTheme
    //   }
    // })
  }

  React.useEffect(() => {
    changeColorsThemeByElement(
      statePage.tabPersonalization.colorTheme,
      '.playgroundColor-theme'
    )
  }, [])

  return (
    <div className="flex justify-center items-center">
      <div className="lg:grid grid-cols-12 w-full max-w-[1920px]">
        <div className="col-span-4">
          <PageSection.Root>
            <PageSection.Content title={languagePage.titleColorDefault} />
          </PageSection.Root>
          <div className="flex items-center gap-3 px-4 mb-2">
            <ButtonColor
              name="default"
              value="default"
              checked={'default' === statePage.tabPersonalization.colorTheme}
              className="demo-default"
              onChenge={onChangeColorThemeDemo}
            />
          </div>

          <PageSection.Root>
            <PageSection.Content title={languagePage.titleColorSuggestion} />
          </PageSection.Root>
          <div className="flex items-center gap-3 px-4 mb-4">
            <ButtonColor
              name="red"
              value="red"
              checked={'red' === statePage.tabPersonalization.colorTheme}
              className="demo-red"
              onChenge={onChangeColorThemeDemo}
            />

            <ButtonColor
              name="blue"
              value="blue"
              checked={'blue' === statePage.tabPersonalization.colorTheme}
              className="demo-blue"
              onChenge={onChangeColorThemeDemo}
            />
          </div>

          <hr className="mb-3" />

          <div>
            <Button
              variant="primary"
              data-testid="btn-apply-colors"
              type="button"
              onClick={onChangeColorTheme}
            >
              {languagePage.buttonApply}
            </Button>
          </div>
        </div>

        <div className="playgroundColor-theme col-span-8 p-3 flex flex-col gap-4">
          <DemoButton />
          <hr />
          <DemoForm />
        </div>
      </div>
    </div>
  )
}

const DemoForm: FC = () => {
  const languagePage = useLanguagePage().tabPersonalization

  return (
    <section>
      <h3 className="text-lg mb-2">{languagePage.demo.formLabel}</h3>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <article>
          <h4 className="text-sm text-zinc-400">
            {languagePage.demo.fieldTextLabel}
          </h4>
          <div className="grid grid-cols-2 items-end gap-3">
            <Input.Root>
              <Input.Label htmlFor={languagePage.demo.fieldText}>
                {languagePage.demo.fieldText}
              </Input.Label>
              <Input.Content
                id={languagePage.demo.fieldText}
                defaultValue="input demo"
                ref={(input) => {
                  if (input) {
                    input.focus()
                    input.value = 'input demo'
                  }
                }}
              />
            </Input.Root>

            <Input.Root>
              <Input.Label htmlFor={languagePage.demo.fieldText}>
                {languagePage.demo.fieldText}
              </Input.Label>
              <Input.Content
                id={languagePage.demo.fieldText}
                defaultValue="input demo"
                ref={(input) => {
                  if (input) {
                    input.value = 'input demo'
                  }
                }}
              />
            </Input.Root>
          </div>
        </article>

        <article>
          <h4 className="text-sm text-zinc-400">
            {languagePage.demo.fieldSelectLabel}
          </h4>
          <div className="grid grid-cols-2 items-end gap-3">
            <TagInput.Root className="min-w-[150px]">
              <TagInput.Label htmlFor={languagePage.demo.fieldSelect}>
                {languagePage.demo.fieldSelect}
              </TagInput.Label>
              <TagInput.Content
                name={languagePage.demo.fieldSelect}
                defaultValue={'1'}
                onChange={() => {}}
                options={[
                  { value: '1', label: 'Ativo' },
                  { value: '4', label: 'Desativado' }
                ]}
              />
            </TagInput.Root>

            <TagInput.Root className="min-w-[150px]">
              <TagInput.Label htmlFor={languagePage.demo.fieldSelect}>
                {languagePage.demo.fieldSelect}
              </TagInput.Label>
              <TagInput.Content
                name={languagePage.demo.fieldSelect}
                defaultValue={'1'}
                onChange={() => {}}
                options={[
                  { value: '1', label: 'Ativo' },
                  { value: '4', label: 'Desativado' }
                ]}
              />
            </TagInput.Root>
          </div>
        </article>

        <article className="grid grid-cols-2 items-end gap-3">
          <div>
            <h4 className="text-sm text-zinc-400">
              {languagePage.demo.fieldCheckboxLabel}
            </h4>
            <Checkbox.Root>
              <Checkbox.Content data-testid="demo-checkbox" defaultChecked />
              <Checkbox.Label>{languagePage.demo.fieldCheckbox}</Checkbox.Label>
            </Checkbox.Root>
          </div>

          <div>
            <h4 className="text-sm text-zinc-400">
              {languagePage.demo.fieldSwitchLabel}
            </h4>
            <Switch.Content
              data-testid="demo-switch"
              label={languagePage.demo.fieldSwitch}
              labelPosition="end"
              defaultChecked
            />
          </div>
        </article>
      </div>
    </section>
  )
}
const DemoButton: FC = () => {
  const languagePage = useLanguagePage().tabPersonalization

  return (
    <section>
      <h3 className="text-lg mb-2">{languagePage.demo.buttonLabel}</h3>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <article>
          <h4 className="text-sm text-zinc-400">normal</h4>
          <div className="flex flex-wrap gap-3 mb-2">
            <Button variant="primary">{languagePage.demo.buttonPrimary}</Button>

            <Button variant="secondary-gray">
              {languagePage.demo.buttonDefault}
            </Button>
          </div>
        </article>

        <article>
          <h4 className="text-sm text-zinc-400">{languagePage.demo.icon}</h4>
          <div className="flex flex-wrap gap-3 mb-2">
            <Button variant="primary">
              <IconOld icon={faCheck} className="text-[16px]" />
            </Button>
            <Button variant="secondary-gray">
              <IconOld icon={faCheck} className="text-[16px]" />
            </Button>
          </div>
        </article>

        <article>
          <h4 className="text-sm text-zinc-400">
            {languagePage.demo.iconLeft}
          </h4>
          <div className="flex flex-wrap gap-3 mb-2">
            <Button variant="primary">
              <Button.Icon>
                <IconOld icon={faCheck} className="text-[16px]" />
              </Button.Icon>
              {languagePage.demo.buttonPrimary}
            </Button>
            <Button variant="secondary-gray">
              <Button.Icon>
                <IconOld icon={faCheck} className="text-[16px]" />
              </Button.Icon>
              {languagePage.demo.buttonPrimary}
            </Button>
          </div>
        </article>

        <article>
          <h4 className="text-sm text-zinc-400">
            {languagePage.demo.iconRight}
          </h4>
          <div className="flex flex-wrap gap-3 mb-2">
            <Button variant="primary">
              {languagePage.demo.buttonPrimary}
              <Button.Icon>
                <IconOld icon={faCheck} className="text-[16px]" />
              </Button.Icon>
            </Button>
            <Button variant="secondary-gray">
              {languagePage.demo.buttonPrimary}
              <Button.Icon>
                <IconOld icon={faCheck} className="text-[16px]" />
              </Button.Icon>
            </Button>
          </div>
        </article>
      </div>
    </section>
  )
}
interface ColorCicleProps {
  name: string
  value: string
  checked: boolean
  className?: string
  classNamePointer?: string
  onChenge: (color: ThemeColorType) => void
}
const ButtonColor: FC<ColorCicleProps> = ({
  name,
  value,
  checked,
  className,
  classNamePointer,
  onChenge
}) => {
  return (
    <div className={cn('button-color', `--${value}`)}>
      <input
        id={name}
        className="hidden"
        type="radio"
        name="variant-color"
        value={value}
        checked={checked}
        onChange={(e) => onChenge(e.target.value as ThemeColorType)}
      />
      <label
        data-testid={`colorCicle-${name}`}
        htmlFor={name}
        className={cn(className, checked ? 'active' : '')}
      >
        <span className={cn(classNamePointer)}></span>
      </label>
    </div>
  )
}
