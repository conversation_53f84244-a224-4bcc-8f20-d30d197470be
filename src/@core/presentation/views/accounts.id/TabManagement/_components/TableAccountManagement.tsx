import { FC } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { memory } from '@/@core/infra/memory'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Table } from '@/@core/presentation/shared/ui/table'

import { useLanguagePage, useStatePage } from '../../page.hooks'
import { useMethodTabManagement } from '../Tab.hooks'
import { IAccountsManagementPage, IFormDataSend } from '../Tab.types'

interface TableAccountManagementProps {
  data: { items: IAccountsManagementPage[]; lastPage: number }
  handleEdit: (p: IAccountsManagementPage) => void
}
export const TableAccountManagement: FC<TableAccountManagementProps> = (
  props
) => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const methodTabManagement = useMethodTabManagement()

  const systemLoading = useSystemLoadingStore()

  const search = memory.local.get().accounts.record

  const handleDelete = (currentItem: IAccountsManagementPage) => {
    const accounts = statePage.tabManagement.table.items.filter(
      (item) => item.account.id !== currentItem.account.id
    )

    const payload: IFormDataSend = {
      accountManagementId: Number(statePage.tabData.account.id),
      accountIds: accounts.map((item) => item.account.id)
    }
    methodTabManagement.handleUpdate(payload)

    methodTabManagement.fetchData()
  }

  return (
    <Table.Root classNameWrapper="block">
      <Table.Info>
        <Table.InfoTitle>
          {languagePage.page.tabs.management}
        </Table.InfoTitle>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head>{languagePage.tabManagement.table.columns.name}</Table.Head>
          <Table.Head className="w-20">{languagePage.tabManagement.table.columns.actions}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {props.data.items.map((item) => (
          <Table.Row key={item.accountName}>
            <Table.Cell>{item.accountName}</Table.Cell>
            <Table.Cell width={80}>
              <div className="flex items-center gap-3">
                <button onClick={() => handleDelete(item)} type="button">
                  <Icon
                    icon="trash01"
                    className="icon-menu-primary"
                    height="20"
                    width="20"
                    viewBox="0 0 20 20"
                  />
                </button>
                <button onClick={() => props.handleEdit(item)} type="button">
                  <Icon
                    icon="edit"
                    className="icon-menu-primary"
                    height="20"
                    width="20"
                    viewBox="0 0 20 20"
                  />
                </button>
              </div>
            </Table.Cell>
          </Table.Row>
        ))}

        <Table.Row
          hide={systemLoading.state.loading || !!props.data.items.length}
        >
          <Table.Cell colSpan={2}>
            {languagePage.tabManagement.withoutData}
          </Table.Cell>
        </Table.Row>

        <Table.RowLoading
          status={systemLoading.state.loading}
          colSpan={2}
          className="min-h-min"
        />
      </Table.Body>

      <Table.Paginate
        hide={!props.data.items.length}
        status={systemLoading.state.loading}
        lastPage={props.data.lastPage}
        currentPage={search.tabManagement.page}
        handleChangePage={(page) => {
          memory.local.set({
            accounts: { record: { tabManagement: { page } } }

          })
          methodTabManagement.fetchData()
        }}
      />
    </Table.Root>
  )
}
