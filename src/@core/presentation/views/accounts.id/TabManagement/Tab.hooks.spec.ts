import { renderHook } from '@testing-library/react'
import { act } from 'react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { accountsManagementMock1 } from '@/__mock__/content/api-accounts-management.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStatePage } from '../page.hooks'
import { useMethodTabManagement } from './Tab.hooks'
import { IFormDataSend } from './Tab.types'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AccountsManagementApiV3')
const spyAccountsManagementApiV3 = jest.spyOn(
  require('@/@core/infra/api/AccountsManagementApiV3'),
  'accountsManagementApiV3'
)

describe('@core/presentation/views/accounts.id/TabManagement/Tab.hooks', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))

    jest.useFakeTimers()
  })

  it('should check method fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabManagement(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    act(() => result.current.state.reset())

    /* request error **/
    spyAccountsManagementApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.tabManagement.table.items).toHaveLength(0)

    /* request success **/
    spyAccountsManagementApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [accountsManagementMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })

    const { table } = result.current.state.tabManagement

    expect(table.items).toHaveLength(1)
    expect(table.lastPage).toBe(1)
  })

  it('should check method handleUpdate', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabManagement(),
        state: useStatePage(),
        toast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const payload: IFormDataSend = {
      accountManagementId: 1,
      accountIds: [1, 2]
    }

    /* request error **/
    await act(async () => result.current.state.reset())
    await act(async () => result.current.toast.reset())

    spyAccountsManagementApiV3.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.handleUpdate(payload)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar registro'
    )

    /* request error updating **/
    await act(async () => result.current.state.reset())
    await act(async () => result.current.toast.reset())

    spyAccountsManagementApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.handleUpdate(payload)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar registro'
    )

    // /* request success **/
    await act(async () => result.current.state.reset())
    await act(async () => result.current.toast.reset())

    spyAccountsManagementApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 201,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.handleUpdate(payload)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Registro atualizado com sucesso'
    )
  })
})
