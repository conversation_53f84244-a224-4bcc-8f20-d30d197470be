import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { accountsManagementApiV3 } from '@/@core/infra/api/AccountsManagementApiV3'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { toastRequestMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import { useLanguagePage, useStatePage } from '../page.hooks'
import { IFormDataSend } from './Tab.types'

export const useMethodTabManagement = () => {
  const log = useLog()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const search = memory.local.get().accounts.record

      const result = await accountsManagementApiV3(http).get({
        accountId: Number(statePage.tabData.account.id),
        limit: search.tabManagement.limit,
        page: search.tabManagement.page
      })

      statePage.setTabManagement({
        table: {
          items: result.data.items,
          lastPage: result.data.lastPage,
          total: result.data.total
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'companies.id/tabManagement/useMethodTabManagement/fetchData'
      })
      statePage.setTabManagement({
        table: {
          items: [],
          lastPage: 0,
          total: 0
        }
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const handleUpdate = async (payload: IFormDataSend) => {
    const requestMessage = languagePage.tabManagement.messages

    try {
      systemLoading.setLoading(true)

      const { status } = await accountsManagementApiV3(http).create(payload)

      const conditionalRequest = status === 201

      systemToast.addToast({
        message: toastRequestMessageSwitch(requestMessage, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return true
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'companies.id/tabManagement/useMethodTabManagement/handleUpdate'
      })

      systemToast.addToast({
        message: requestMessage.errorMessage,
        type: 'error'
      })

      return false
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { fetchData, handleUpdate }
}
