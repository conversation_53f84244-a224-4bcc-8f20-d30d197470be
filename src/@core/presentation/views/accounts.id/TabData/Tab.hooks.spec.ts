import { renderHook, waitFor } from '@testing-library/react'

import { accountMock1 } from '@/__mock__/content/api-accounts.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStatePage } from '../page.hooks'
import { useMethodTabData } from './Tab.hooks'
import { FormDataSend } from './Tab.types'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AccountsApiV3')
const spyAccountsApiV3 = jest.spyOn(
  require('@/@core/infra/api/AccountsApiV3'),
  'accountsApiV3'
)

describe('@core/presentation/views/accounts.id/TabData/Tab.hooks', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyAccountsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request successful without data **/
    spyAccountsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.fetchData()
    })
    expect(result.current.state.tabData.account?.id).toBeUndefined()
    expect(result.current.state.tabData.account?.name).toBeUndefined()
    expect(result.current.state.tabData.account?.statusId).toBeUndefined()

    /* request success **/
    spyAccountsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: accountMock1
      })
    }))
    await waitFor(() => {
      result.current.method.fetchData()
    })

    const { account } = result.current.state.tabData
    expect(account.id).toBe(accountMock1.id)
    expect(account.name).toBe(accountMock1.name)
    expect(account.statusId).toBe(accountMock1.statusId)
    expect(account.modules).toHaveLength(accountMock1.modules.length)
  })

  it('should check return the function onSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const payload: FormDataSend = {
      id: null,
      name: 'Tempest',
      accountStatusId: 1,
      moduleIds: [1, 2, 3],
      management: 1
    }

    /* request CREATE error **/
    await waitFor(() => result.current.state.reset())

    spyAccountsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.account.id).toBeUndefined()

    /* request CREATE success **/
    await waitFor(() => result.current.state.reset())

    spyAccountsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: accountMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.account.id).toBeUndefined()

    /** assigns value to key id */
    payload.id = 44

    /* request UPDATE error **/
    await waitFor(() => result.current.state.reset())

    spyAccountsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.account.id).toBeUndefined()

    /* request UPDATE success **/
    await waitFor(() => result.current.state.reset())

    spyAccountsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: accountMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.account.id).toBe(accountMock1.id)
  })
})
