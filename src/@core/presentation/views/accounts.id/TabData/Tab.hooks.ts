import { useRouter } from 'next/router'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { accountsApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

import { useLanguagePage, useStatePage } from '../page.hooks'

import { FormDataSend } from './Tab.types'

export const useMethodTabData = () => {
  const router = useRouter()
  const log = useLog()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const result = await accountsApiV3(http).getById(Number(router.query?.id))

      if (result.status === 204) {
        return
      }

      statePage.setTabData({
        account: {
          id: result.data.id,
          name: result.data.name,
          statusId: result.data.statusId,
          modules: result.data.modules,
          management: result.data.management
        }
      })
      // statePage.setTabPersonalization({
      //   colorTheme: memory.local.get().system.themeColor
      // })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'accounts.id/tabData/hooks/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const onSubmit = async (payload: FormDataSend) => {
    const currentId = payload.id!
    const isCreated = !payload.id

    try {
      systemLoading.setData({ loading: true })

      const { data } = isCreated
        ? await accountsApiV3(http).create(payload)
        : await accountsApiV3(http).update(currentId, payload)

      systemToast.addToast({
        message: isCreated
          ? languagePage.tabData.formMessages.createSuccessMessage
          : languagePage.tabData.formMessages.updateSuccessMessage
      })

      if (isCreated) {
        router.push(`/accounts/${data.id}`)
        return
      }

      statePage.setTabData({
        account: {
          id: data.id,
          name: data.name,
          modules: data.modules,
          statusId: data.statusId,
          management: data.management
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'accounts.id/tabData/hooks/onSubmit'
      })

      systemToast.addToast({
        message: isCreated
          ? languagePage.tabData.formMessages.createErrorMessage
          : languagePage.tabData.formMessages.updateErrorMessage
      })
    } finally {
      systemLoading.setData({ loading: false })
    }
  }

  return { fetchData, onSubmit }
}
