import { AxiosError } from 'axios'
import { useRouter } from 'next/router'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { companiesSalesforceV4 } from '@/@core/infra/api/CompaniesSalesforceApiv4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import { useLanguagePage, useStatePage } from '../page.hooks'
import { FormDataSend, ICompanySalesforcePage } from './Tab.types'
import { parseDataInput } from './Tab.utils'

export const useMethodTabSalesforce = () => {
  const log = useLog()

  const statePage = useStatePage()

  const systemLoading = useSystemLoadingStore()

  const fetchData = async () => {
    try {
      const search = memory.local.get().companies.record.tabSalesforce

      statePage.setTabSalesforce({
        table: {
          items: [],
          lastPage: 0
        }
      })

      const result = await companiesSalesforceV4(http).get({
        companyId: statePage.tabData.company.id!,
        limit: search.limit,
        page: search.page
      })

      if (result.status === 204) {
        systemLoading.setLoading(false)
        return
      }

      statePage.setTabSalesforce({
        table: {
          items: result.data.items.map(parseDataInput),
          lastPage: result.data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'companies.id/tabSalesforce/useMethodTabSalesforce/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { fetchData }
}

export const useMethodTabSalesforceForm = () => {
  const router = useRouter()
  const log = useLog()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const onSubmit = async (payload: FormDataSend) => {
    const currentId: number = payload.id!

    let valueCreate: Partial<ICompanySalesforcePage> | null = null
    let valueUpdate: Partial<ICompanySalesforcePage> | null = null
    let errors: Record<string, string> | null = null

    try {
      systemLoading.setLoading(true)

      const { status, data } = currentId
        ? await companiesSalesforceV4(http).update(currentId, payload)
        : await companiesSalesforceV4(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(
          languagePage.tabSalesforce.formMessages,
          currentId,
          conditionalRequest
        ),
        type: toastTypeSwitch(conditionalRequest)
      })

      currentId
        ? (valueUpdate = { ...data, id: currentId })
        : (valueCreate = data)
    } catch (err) {
      const error = err as AxiosError<Record<keyof FormData, [string]>>

      if (error?.response?.data) {
        errors = {}
        Object.entries(error?.response?.data).forEach(([key, [value]]) => {
          errors![key] = value
        })
      }

      log.send(loggerRequest, {
        error: err,
        title: 'companies.id/tabSalesforce/useMethodTabSalesforceForm/onSubmit'
      })
    } finally {
      systemLoading.setLoading(false)
    }

    return {
      errors,
      add: valueCreate,
      update: valueUpdate
    }
  }

  return { onSubmit }
}

export const useMethodTabSalesforceTable = () => {
  const log = useLog()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const handleDelete = async (
    companySalesforceId: number,
    callback: () => void
  ) => {
    const { messages } = languagePage.tabSalesforce.modalDelete
    try {
      systemLoading.setLoading(true)

      const { status } = await companiesSalesforceV4(http).delete(
        companySalesforceId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      if (!conditionalRequest) return

      callback()
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'companies.id/tabContacts/useMethodTabContactsTable/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })

      systemLoading.setLoading(false)
    }
  }

  return { handleDelete }
}
