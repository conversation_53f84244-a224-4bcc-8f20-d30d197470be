import { renderHook, waitFor } from '@testing-library/react'

import {
  companySalesforceMock1,
  companySalesforceMock2
} from '@/__mock__/content/api-company-salesforce.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStatePage } from '../page.hooks'
import {
  useMethodTabSalesforce,
  useMethodTabSalesforceForm,
  useMethodTabSalesforceTable
} from './Tab.hooks'
import { ICompanySalesforcePage } from './Tab.types'
import { parseDataInput } from './Tab.utils'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/CompaniesSalesforceApiv4')

const spyCompaniesSalesforceApiV4 = jest.spyOn(
  require('@/@core/infra/api/CompaniesSalesforceApiv4'),
  'companiesSalesforceV4'
)

describe('@core/presentation/views/companies.id/TabSalesforce/Tab.hooks', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function useMethodTabSalesforce', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabSalesforce(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request successful without data **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.tabSalesforce.table.items).toHaveLength(0)
    expect(result.current.state.tabSalesforce.table.lastPage).toBe(0)

    /* request success **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [companySalesforceMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(() => {
      result.current.method.fetchData()
    })
    expect(result.current.state.tabSalesforce.table.items).toHaveLength(1)
    expect(result.current.state.tabSalesforce.table.lastPage).toBe(1)
  })

  it('should check return the function useMethodTabSalesforceForm', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabSalesforceForm(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    interface IResultSubmit {
      errors: Record<string, string>
      add: Partial<ICompanySalesforcePage>
      update: Partial<ICompanySalesforcePage>
    }
    let resultSubmit: IResultSubmit = {
      errors: {},
      add: {},
      update: {}
    }

    const formData = {
      id: null,
      externalId: companySalesforceMock1.externalId,
      source: companySalesforceMock1.source,
      companyId: companySalesforceMock1.companyId
    }

    /* request CREATE error **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        response: {
          data: {
            name: ['campo obrigatório']
          }
        }
      })
    }))
    await waitFor(async () => {
      const res = await result.current.method.onSubmit(formData)
      resultSubmit.add.id = res.add?.id
    })
    expect(resultSubmit?.add.id).toBeUndefined()

    /* request CREATE success **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: companySalesforceMock1
      })
    }))

    await waitFor(async () => {
      const res = await result.current.method.onSubmit(formData)
      resultSubmit.add.id = res.add?.id
    })
    expect(resultSubmit?.add.id).toBe(1)

    /* request UPDATE error **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      const res = await result.current.method.onSubmit({ ...formData, id: 123 })
      resultSubmit.update.id = res.update?.id
    })
    expect(resultSubmit?.update.id).toBeUndefined()

    /* request UPDATE success **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 201,
        data: { ...companySalesforceMock1, id: 123 }
      })
    }))
    await waitFor(async () => {
      const res = await result.current.method.onSubmit({ ...formData, id: 123 })
      resultSubmit.update.id = res.update?.id
    })
    expect(resultSubmit?.update.id).toBe(123)
  })

  it('should check return the function useMethodTabSalesforceTable', async () => {
    const callback = jest.fn()

    const { result } = renderHook(
      () => ({
        method: useMethodTabSalesforceTable(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => {
      result.current.state.reset()

      result.current.state.setTabSalesforce({
        table: {
          lastPage: 1,
          items: [
            parseDataInput(companySalesforceMock1),
            parseDataInput(companySalesforceMock2)
          ]
        }
      })
    })

    /* request error **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1, callback)
    })
    expect(callback).toHaveBeenCalledTimes(0)

    /* request id not found **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 404
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleDelete(1, callback)
    })
    expect(callback).toHaveBeenCalledTimes(0)

    /* request success **/
    spyCompaniesSalesforceApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    expect(result.current.state.tabSalesforce.table.items).toHaveLength(2)

    await waitFor(async () => {
      await result.current.method.handleDelete(1, callback)
    })
    expect(callback).toHaveBeenCalledTimes(1)
  })
})
