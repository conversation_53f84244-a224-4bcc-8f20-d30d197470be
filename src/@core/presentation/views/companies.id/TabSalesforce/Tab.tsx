import { useRouter } from 'next/router'
import { useEffect, useRef } from 'react'

import { PageSection } from '@/@core/presentation/shared/pages/PageSection'
import { ListExternalCodesService } from '@/@core/services/listExternalCodesService'
import checkQueryIdIsNumber from '@/@core/utils/checkIsNumber'

import { useLanguagePage, useStatePage } from '../page.hooks'
import { useMethodTabSalesforce } from './Tab.hooks'
import { SectionSalesforce } from './_components/SectionSalesforce'

export const TabSalesforce = () => {
  const isMounted = useRef<boolean>(false)

  const router = useRouter()

  const listExternalCodesService = ListExternalCodesService()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()
  const methodTabSalesforce = useMethodTabSalesforce()

  useEffect(() => {
    statePage.setTabMounted('TabSalesforce')

    listExternalCodesService.handler()

    if (
      !isMounted.current &&
      !statePage.tab.mounted?.TabSalesforce &&
      checkQueryIdIsNumber(router.query.id)
    ) {
      methodTabSalesforce.fetchData()
    }

    return () => {
      isMounted.current = true
    }
  }, [])

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2">
            <PageSection.Content title={languagePage.page.tabs.salesForce} />
          </div>
        </div>
      </PageSection.Root>
      <SectionSalesforce />
    </>
  )
}
