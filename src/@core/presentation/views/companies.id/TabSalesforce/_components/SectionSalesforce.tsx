import { zodResolver } from '@hookform/resolvers/zod'
import { FC, forwardRef, useImperativeHandle, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useListExternalCodesStore from '@/@core/framework/store/hook/useListExternalCodesStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'

import { useLanguagePage, useStatePage } from '../../page.hooks'
import {
  useMethodTabSalesforce,
  useMethodTabSalesforceForm,
  useMethodTabSalesforceTable
} from '../Tab.hooks'
import { ICompanySalesforcePage } from '../Tab.types'
import { formDataOutput } from '../Tab.utils'

export const SectionSalesforce = () => {
  const statePage = useStatePage()

  const formRef = useRef<IFormSalesforceRef>(null)

  return (
    <>
      <FormSalesforce ref={formRef} />
      <TableSalesforce
        data={statePage.tabSalesforce.table}
        handleEdit={(p) => formRef.current?.handleEdit(p)}
      />
    </>
  )
}

interface IFormSalesforceRef {
  handleEdit: (p: ICompanySalesforcePage) => void
}
interface IFormSalesforceProps {}
const FormSalesforce = forwardRef<IFormSalesforceRef, IFormSalesforceProps>(
  (props, ref) => {
    const listExternalCodesStore = useListExternalCodesStore()
    const systemLoading = useSystemLoadingStore()
    const systemToast = useSystemToastStore()

    const statePage = useStatePage()
    const languagePage = useLanguagePage()
    const methodTabSalesforceForm = useMethodTabSalesforceForm()

    const formFields = useFormFields()

    useImperativeHandle(ref, () => ({
      handleEdit: (value) => {
        formFields.setValues(value)
      }
    }))

    const handleSubmit = async () => {
      const result = await methodTabSalesforceForm.onSubmit(
        formDataOutput({
          ...formFields.values,
          companyId: statePage.tabData.company.id!
        })
      )

      const { add, update, errors } = result as {
        errors: Record<string, string>
        add: ICompanySalesforcePage
        update: ICompanySalesforcePage
      }

      if (errors) {
        Object.values(errors).forEach((message) =>
          systemToast.addToast({ message, type: 'error' })
        )
        return
      }

      const newItems = add
        ? [...statePage.tabSalesforce.table.items, add]
        : [...statePage.tabSalesforce.table.items].map((item) =>
            item.id === update.id ? update : item
          )

      statePage.setTabSalesforce({
        table: {
          ...statePage.tabSalesforce.table,
          items: newItems
        }
      })

      formFields.setValues({})
    }

    return (
      <form
        className="mb-5 form-container"
        onSubmit={formFields.handleSubmit(handleSubmit)}
      >
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4 items-start">
          <Input.Root>
            <Input.Label
              htmlFor={languagePage.tabSalesforce.form.inputExternalId}
            >
              {languagePage.tabSalesforce.form.inputExternalId}
            </Input.Label>
            <Input.Content
              id={languagePage.tabSalesforce.form.inputExternalId}
              value={formFields.values.externalId}
              onChange={(e) => {
                formFields.setValue('externalId', e.target.value)
              }}
              helperText={formFields.errors.externalId?.message}
            />
          </Input.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="source">
              {languagePage.tabSalesforce.form.inputSource}
            </TagInput.Label>
            <TagInput.Content
              name="source"
              value={formFields.values.source}
              onChange={(items) =>
                formFields.setValue('source', items?.[0]?.value ?? '')
              }
              options={Object.keys(listExternalCodesStore.state.list).map(
                (externalCode) => ({
                  value: externalCode,
                  label: externalCode
                })
              )}
            />
          </TagInput.Root>
        </div>
        <div className="footer-form">
          <Button
            type="button"
            onClick={() => formFields.setValues()}
            variant="secondary-gray"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languagePage.tabSalesforce.cancel
              : languagePage.tabSalesforce.clean}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languagePage.tabSalesforce.save
              : languagePage.tabSalesforce.add}
          </Button>
        </div>
      </form>
    )
  }
)

const useFormFields = () => {
  const { requiredField } = useLanguagePage().tabSalesforce

  const fieldSchema = z
    .string({ required_error: requiredField })
    .min(1, { message: requiredField })

  const formSchema = z.object({
    id: z.number().nullable(),
    externalId: fieldSchema,
    source: fieldSchema
  })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (
    data: Partial<ICompanySalesforcePage>
  ): FormSchema => ({
    id: data.id ?? null,
    externalId: data.externalId ?? '',
    source: data.source ?? ''
  })

  const {
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const setValues = (data: Partial<ICompanySalesforcePage> = {}) => {
    const dataParsed = parseInitialData(data)
    setValue('id', dataParsed.id)
    setValue('externalId', dataParsed.externalId)
    setValue('source', dataParsed.source)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, errors, values, setError }
}

interface TableContactsProps {
  data: { items: ICompanySalesforcePage[]; lastPage: number }
  handleEdit: (p: ICompanySalesforcePage) => void
}
const TableSalesforce: FC<TableContactsProps> = (props) => {
  const languagePage = useLanguagePage()

  const methodTabSalesforce = useMethodTabSalesforce()

  const systemLoading = useSystemLoadingStore()

  const search = memory.local.get().companies.record.tabSalesforce

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languagePage.page.tabs.salesForce}</Table.InfoTitle>
      </Table.Info>
      <Table.Header>
        <Table.Row>
          <Table.Head>
            {languagePage.tabSalesforce.table.columns.externalId}
          </Table.Head>
          <Table.Head>
            {languagePage.tabSalesforce.table.columns.source}
          </Table.Head>
          <Table.Head>
            {languagePage.tabContacts.table.columns.actions}
          </Table.Head>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {!systemLoading.state.loading &&
          props.data.items.map((item) => (
            <Table.Row key={item.id}>
              <Table.Cell>{item.externalId}</Table.Cell>
              <Table.Cell>{item.source}</Table.Cell>
              <Table.Cell width={80}>
                <Actions
                  companySalesforce={item}
                  onClickEdit={() => props.handleEdit(item)}
                />
              </Table.Cell>
            </Table.Row>
          ))}

        <Table.RowLoading status={systemLoading.state.loading} colSpan={3} />
      </Table.Body>

      <Table.Mobile>
        {props.data.items.map((item) => (
          <TableMobile.Item key={item.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.tabSalesforce.table.columns.externalId}
              </TableMobile.Cell>
              <TableMobile.Cell>{item.externalId}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.tabSalesforce.table.columns.source}
              </TableMobile.Cell>
              <TableMobile.Cell>{item.source}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Footer className="flex justify-end">
              <Actions
                companySalesforce={item}
                onClickEdit={() => props.handleEdit(item)}
              />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={props.data.lastPage}
        currentPage={search.page!}
        handleChangePage={(page) => {
          memory.local.set({
            companies: {
              record: { tabContacts: { page } }
            }
          })
          methodTabSalesforce.fetchData()
        }}
      />
    </Table.Root>
  )
}
const Actions: FC<{
  onClickEdit: () => void
  companySalesforce: ICompanySalesforcePage
}> = ({ onClickEdit, ...rest }) => {
  return (
    <div className="flex items-center gap-3">
      <ModalDelete {...rest} />
      <button onClick={onClickEdit}>
        <Icon
          icon="edit"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>
    </div>
  )
}

interface ModalDeleteProps {
  companySalesforce: ICompanySalesforcePage
}

const ModalDelete: FC<ModalDeleteProps> = ({ companySalesforce }) => {
  const [openDialog, setOpenDialog] = useState(false)

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoadingStore = useSystemLoadingStore()

  const methodTabSalesforceTable = useMethodTabSalesforceTable()

  const handleDeleteSuccess = async () => {
    setOpenDialog(false)

    setTimeout(() => {
      statePage.setTabSalesforce({
        table: {
          ...statePage.tabSalesforce.table,
          items: statePage.tabSalesforce.table.items.filter(
            (e) => e.id !== companySalesforce.id
          )
        }
      })
    }, 100)

    setTimeout(() => {
      systemLoadingStore.setLoading(false)
    }, 200)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]" asChild>
        <button type="button">
          <Icon
            icon="trash01"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>
            {languagePage.tabSalesforce.modalDelete.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description hidden>
          {languagePage.tabSalesforce.modalDelete.title}
        </Dialog.Description>

        <p className="mb-2">
          {languagePage.tabSalesforce.modalDelete.textInfo}
          <span className="text-red-600"> {companySalesforce.externalId}</span>
        </p>

        <Dialog.Footer className="flex justify-end gap-3">
          <Dialog.Close disabled={systemLoadingStore.state.loading} asChild>
            <Button variant="secondary-gray" type="button">
              {languagePage.tabSalesforce.modalDelete.textCancel}
            </Button>
          </Dialog.Close>

          <Button
            variant="error-primary"
            type="button"
            onClick={() =>
              methodTabSalesforceTable.handleDelete(
                companySalesforce.id,
                handleDeleteSuccess
              )
            }
            disabled={systemLoadingStore.state.loading}
          >
            {languagePage.tabSalesforce.modalDelete.textConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
