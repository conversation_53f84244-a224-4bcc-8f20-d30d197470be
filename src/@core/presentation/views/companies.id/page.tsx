import { useEffect } from 'react'

import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import { memory } from '@/@core/infra/memory'
import { HeaderList } from '@/@core/presentation/shared/pages/HeaderList'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'
import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'
import { Tabs } from '@/@core/presentation/shared/ui/tabs'

import { TabContacts } from './TabContacts/Tab'
import { TabData } from './TabData/Tab'
import { TabSalesforce } from './TabSalesforce/Tab'

import { Icon } from '../../shared/ui/icons'
import { useLanguagePage, useStatePage } from './page.hooks'

export const Page = () => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  useTitlePage(languagePage.page.title)
  useBreadcrumb('companies.id')

  useEffect(() => {
    return () => {
      statePage.reset()
      memory.local.reset('companies')
    }
  }, [])

  return (
    <PageContent>
      <Breadcrumbs />
      <HeaderList.Root>
        <HeaderList.Content
          title={languagePage.page.title}
          className="mr-auto"
        />
      </HeaderList.Root>

      <Tabs.Root
        variant="primary"
        className="lg:flex items-start gap-2"
        value={statePage.tab.active}
        onValueChange={statePage.setTab}
      >
        <Tabs.List className="mb-3 lg:flex-col lg:pr-2 lg:mr-6 lg:min-w-[177px] lg:min-h-[350px] justify-start">
          <Tabs.Trigger className="flex gap-2" value="data">
            <Icon
              icon="clipboard"
              width="18"
              height="18"
              fill="none"
              className="icon-menu-primary"
              strokeWidth="2"
              viewBox="0 0 24 24"
            />
            {languagePage.page.tabs.data}
          </Tabs.Trigger>

          <Tabs.Trigger value="contacts" disabled={!statePage.isEdit}>
            {languagePage.page.tabs.contacts}
          </Tabs.Trigger>

          <Tabs.Trigger value="salesForce" disabled={!statePage.isEdit}>
            {languagePage.page.tabs.salesForce}
          </Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="data" className="w-full">
          <TabData />
        </Tabs.Content>

        <Tabs.Content value="contacts" className="w-full">
          <TabContacts />
        </Tabs.Content>

        <Tabs.Content value="salesForce" className="w-full">
          <TabSalesforce />
        </Tabs.Content>
      </Tabs.Root>
    </PageContent>
  )
}
