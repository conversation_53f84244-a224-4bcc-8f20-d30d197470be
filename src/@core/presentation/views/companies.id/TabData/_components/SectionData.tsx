import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { observer, redirectObserver } from '@/@core/domain/observer'
import useListCityStore from '@/@core/framework/store/hook/useListCityStore'
import useCompaniesTypesStore from '@/@core/framework/store/hook/useListCompaniesTypesStore'
import useListStateStore from '@/@core/framework/store/hook/useListStateStore'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { viaCepApi } from '@/@core/infra/api-external/viaCepAPI/viaCepApi'
import { httpExternal } from '@/@core/infra/http-external'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import {
  formatInputValues,
  formatOutputValues,
  SelectContentOption,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { ListCityService } from '@/@core/services/listCityService'

import { useLanguagePage, useStatePage } from '../../page.hooks'

import { timezoneOptions } from '@/@core/content/timezoneOptions.content'
import { accountsApiV3 } from '@/@core/infra/api'
import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'
import { useMethodTabData } from '../Tab.hooks'
import { FormData } from '../Tab.types'
import {
  applyMaskCNPJ,
  applyMaskZipCode,
  findCityListByCityName,
  findStateListByLabel,
  formatDataInput,
  formDataOutput
} from '../Tab.utils'

export const SectionData = () => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage()
  const methodTab = useMethodTabData()

  const listStateStore = useListStateStore()
  const listCityStore = useListCityStore()
  const listCompaniesTypesStore = useCompaniesTypesStore()
  const systemToastStore = useSystemToastStore()
  const systemLoading = useSystemLoadingStore()

  const listCityService = ListCityService()

  const formFields = useFormFields()

  const log = useLog()

  const [formattedCNPJ, setFormattedCNPJ] = useState('')
  const [formattedZipCode, setFormattedZipCode] = useState('')
  const [city, setCity] = useState('')

  useEffect(() => {
    formFields.setValues(formatDataInput(statePage.tabData.company))
  }, [statePage.tabData.company])

  useEffect(() => {
    if (formFields.values.stateId) {
      listCityService.handler(Number(formFields.values.stateId))
    }
  }, [formFields.values.stateId])

  useEffect(() => {
    setFormattedZipCode(applyMaskZipCode(formFields.values.zipCode))
  }, [formFields.values.zipCode])

  useEffect(() => {
    if (formFields.values.cnpj)
      setFormattedCNPJ(applyMaskCNPJ(formFields.values.cnpj))
  }, [formFields.values.cnpj])

  useEffect(() => {
    if (city) {
      const findCity = findCityListByCityName(listCityStore.state.list, city)

      if (!findCity) return

      formFields.setValue('cityId', String(findCity.id))
    }
  }, [city, listCityStore.state.list])

  const resetFieldCompany = useRef<{ reset: () => void }>(null)

  const handleCNPJChange = (value: string) => {
    setFormattedCNPJ(applyMaskCNPJ(value))

    formFields.setValue('cnpj', value.replace(/\D/g, ''))
  }
  const handleCEPChange = async (value: string) => {
    const zipCode = value.replace(/\D/g, '') || ''
    formFields.setValue('zipCode', zipCode)

    if (zipCode.length === 8) {
      try {
        systemLoading.setData({ loading: true })

        const { result } = await viaCepApi(httpExternal).get(zipCode)

        if (result.erro) {
          resetAddressFields()

          systemToastStore.addToast({
            message:
              languagePage.tabData.formMessages.errorWhenSearchingZipCode!,
            type: 'error'
          })
          return
        }

        const state = findStateListByLabel(listStateStore.state.list, result.uf)

        if (!state) {
          resetAddressFields()
          return
        }

        listCityService.handler(Number(state.id))

        setCity(result.localidade)

        formFields.setValue('address', result.logradouro)
        formFields.setValue('complement', result.complemento)
        formFields.setValue('district', result.bairro)
        formFields.setValue('stateId', String(state.id))
        formFields.setValue('codeIbge', Number(result.ibge))
      } catch (error) {
        log.send(loggerRequest, {
          error,
          title: `Submit Data - @core/presentation/views/companies.id/TabData/_components/SectionData.tsx`
        })
      } finally {
        systemLoading.setData({ loading: false })
      }
    }
  }
  const handleStateChange = async (value: unknown) => {
    await listCityService.handler(Number(value))

    formFields.setValue('stateId', String(value))

    resetAddressFields()
  }

  const handleTypeChange = (items: SelectContentOption[] | null) => {
    const selectedValue = items?.[0]?.value ?? ''
    formFields.setValue('type', selectedValue)
    formFields.setValue('parent', null)
    resetFieldCompany.current?.reset()
  }

  const resetAddressFields = () => {
    formFields.setValue('address', '')
    formFields.setValue('complement', '')
    formFields.setValue('district', '')
    formFields.setValue('cityId', '')
    formFields.setValue('codeIbge', null)
  }

  return (
    <form
      onSubmit={formFields.handleSubmit(() => {
        methodTab.onSubmit(formDataOutput(formFields.values))
      })}
      className="form-container"
    >
      <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-x-4 gap-y-8 items-baseline">
        <div className="col-span-full">
          <TagInput.Root className="max-w-[316px]">
            <TagInput.Label id="type">
              {languagePage.tabData.form.inputType}
            </TagInput.Label>
            <TagInput.Content
              name="inputType"
              value={formFields.values.type}
              onChange={(items) => handleTypeChange(items)}
              options={listCompaniesTypesStore.state.list.map((el) => ({
                value: String(el.value),
                label: el.name
              }))}
              disabled={systemLoading.state.loading}
              helperText={formFields.errors.type?.message}
            />
          </TagInput.Root>
        </div>
        <TagInput.Root>
          <TagInput.Label>
            {languagePage.tabData.form.inputAccount}
          </TagInput.Label>
          <TagInput.ContentApi
            data-testid="field-account"
            value={formatInputValues(formFields.values.account)}
            onChange={(values) =>
              formFields.setValue('account', formatOutputValues(values))
            }
            helperText={formFields.errors.account?.message}
            featchData={(p) => accountsApiV3(http).get(p)}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputSocialName}>
            {languagePage.tabData.form.inputSocialName}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputSocialName}
            defaultValue={formFields.values.corporateName}
            onChange={(e) => {
              formFields.setValue('corporateName', e.target.value)
            }}
            helperText={formFields.errors.corporateName?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputName}>
            {languagePage.tabData.form.inputName}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputName}
            defaultValue={formFields.values.name}
            onChange={(e) => {
              formFields.setValue('name', e.target.value)
            }}
            helperText={formFields.errors.name?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputCnpj}>
            {languagePage.tabData.form.inputCnpj}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputCnpj}
            placeholder="00.000.000/0000-00"
            value={formattedCNPJ}
            onChange={(e) => handleCNPJChange(e.target.value)}
            helperText={formFields.errors.cnpj?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputCnae}>
            {languagePage.tabData.form.inputCnae}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputCnae}
            defaultValue={formFields.values.cnae}
            onChange={(e) => {
              formFields.setValue('cnae', e.target.value)
            }}
            helperText={formFields.errors.cnae?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label
            htmlFor={languagePage.tabData.form.inputConsumerUnitCode}
          >
            {languagePage.tabData.form.inputConsumerUnitCode}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputConsumerUnitCode}
            defaultValue={formFields.values.unitCode}
            onChange={(e) => {
              formFields.setValue('unitCode', e.target.value)
            }}
            helperText={formFields.errors.unitCode?.message}
          />
        </Input.Root>

        <TagInput.Root className="mt-1">
          <TagInput.Label>
            {languagePage.tabData.form.inputCompany}
          </TagInput.Label>
          <TagInput.ContentApi
            key={formFields.values.type}
            ref={resetFieldCompany}
            disabled={formFields.values.type === 'company'}
            data-testid="field-company"
            value={formatInputValues(formFields.values.parent)}
            onChange={(values) =>
              formFields.setValue('parent', formatOutputValues(values))
            }
            featchData={(p) =>
              companiesApiV4(http).get({
                ...p,
                type: formFields.values.type === 'sector' ? 'unit' : 'company'
              })
            }
            helperText={formFields.errors.parent?.message}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputZipCode}>
            {languagePage.tabData.form.inputZipCode}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputZipCode}
            value={formattedZipCode}
            onChange={(e) => handleCEPChange(e.target.value)}
            helperText={formFields.errors.zipCode?.message}
          />
        </Input.Root>

        <TagInput.Root className="w-[100%]">
          <TagInput.Label id="stateId">
            {languagePage.tabData.form.inputState}
          </TagInput.Label>
          <TagInput.Content
            name="stateId"
            value={formFields.values.stateId}
            onChange={(items) => handleStateChange(items?.[0]?.value ?? '')}
            options={listStateStore.state.list.map((el) => ({
              value: String(el.id),
              label: el.label
            }))}
            helperText={formFields.errors.stateId?.message}
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>

        <TagInput.Root className="w-[100%]">
          <TagInput.Label id="cityId">
            {languagePage.tabData.form.inputCity}
          </TagInput.Label>
          <TagInput.Content
            name="cityId"
            value={formFields.values.cityId}
            onChange={(items) =>
              formFields.setValue('cityId', items?.[0]?.value ?? '')
            }
            options={listCityStore.state.list.map((el) => ({
              value: String(el.id),
              label: el.name
            }))}
            helperText={formFields.errors.cityId?.message}
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputNeighborhood}>
            {languagePage.tabData.form.inputNeighborhood}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputNeighborhood}
            value={formFields.values.district}
            onChange={(e) => {
              formFields.setValue('district', e.target.value)
            }}
            helperText={formFields.errors.district?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputStreet}>
            {languagePage.tabData.form.inputStreet}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputStreet}
            value={formFields.values.address}
            onChange={(e) => {
              formFields.setValue('address', e.target.value)
            }}
            helperText={formFields.errors.address?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputNumber}>
            {languagePage.tabData.form.inputNumber}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputNumber}
            value={formFields.values.number}
            onChange={(e) => {
              formFields.setValue('number', e.target.value)
            }}
            helperText={formFields.errors.number?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor={languagePage.tabData.form.inputComplement}>
            {languagePage.tabData.form.inputComplement}
          </Input.Label>
          <Input.Content
            id={languagePage.tabData.form.inputComplement}
            value={formFields.values.complement}
            onChange={(e) => {
              formFields.setValue('complement', e.target.value)
            }}
            helperText={formFields.errors.complement?.message}
          />
        </Input.Root>

        <TagInput.Root className="w-[100%]">
          <TagInput.Label id="timezone">
            {languagePage.tabData.form.inputTimezone}
          </TagInput.Label>
          <TagInput.Content
            name="timezone"
            value={formFields.values.timezone}
            onChange={(items) => {
              const selectedValue = items?.[0]?.value ?? '-3'
              formFields.setValue('timezone', selectedValue)
            }}
            options={timezoneOptions.map((el) => ({
              value: el.value,
              label: el.label
            }))}
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>

      <div className="footer-form">
        <Button
          variant="secondary-gray"
          data-testid="btn-cancel"
          type="button"
          onClick={() => observer.publish(redirectObserver('/companies'))}
        >
          {languagePage.tabData.cancel}
        </Button>

        <Button data-testid="btn-submit" type="submit" variant="primary">
          {languagePage.tabData.save}
        </Button>
      </div>
    </form>
  )
}

const useFormFields = () => {
  const { tabData } = useLanguagePage()

  const formSchema = z
    .object({
      id: z.union([z.number(), z.null()]),
      name: z.string(),
      unitCode: z.string(),
      cnpj: z.string(),
      corporateName: z.string(),
      address: z.string(),
      number: z.string(),
      complement: z.string(),
      district: z.string(),
      zipCode: z.string(),
      cnae: z.string(),
      parent: z.union([
        z.array(
          z.object({
            id: z.number(),
            name: z.string()
          })
        ),
        z.null()
      ]),
      cityId: z.string(),
      stateId: z.string(),
      account: z.union([
        z.array(
          z.object({
            id: z.number(),
            name: z.string()
          })
        ),
        z.null()
      ]),
      codeIbge: z.union([z.number(), z.null()]),
      timezone: z.string(),
      type: z.string()
    })
    .superRefine((values, ctx) => {
      if (!values.name)
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: tabData.requiredField,
          path: ['name']
        })

      if (!values.account)
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: tabData.requiredField,
          path: ['account']
        })

      if (!values.corporateName)
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: tabData.requiredField,
          path: ['corporateName']
        })

      // if (!values.type)
      //   ctx.addIssue({
      //     code: z.ZodIssueCode.custom,
      //     message: tabData.requiredField,
      //     path: ['type']
      //   })

      if (values.type === 'company') {
        const requiredUnitFields: (keyof typeof values)[] = [
          'zipCode',
          'stateId',
          'cityId',
          'district',
          'address',
          'number'
          // 'cnpj'
        ]

        requiredUnitFields.forEach((field) => {
          if (!values[field]) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: tabData.requiredField,
              path: [field]
            })
          }
        })
      }

      if (values.type === 'unit') {
        const requiredUnitFields: (keyof typeof values)[] = [
          'cnpj',
          'zipCode',
          'stateId',
          'cityId',
          'district',
          'address',
          'number',
          'parent'
        ]

        requiredUnitFields.forEach((field) => {
          if (!values[field]) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: tabData.requiredField,
              path: [field]
            })
          }
        })
      }
      if (values.type === 'sector')
        if (!values.parent)
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: tabData.requiredField,
            path: ['parent']
          })
    })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (data: Partial<FormData>): FormSchema => ({
    id: data.id ?? null,
    name: data.name ?? '',
    number: data.number ?? '',
    unitCode: data.unitCode ?? '',
    cnpj: data.cnpj ?? '',
    corporateName: data.corporateName ?? '',
    address: data.address ?? '',
    complement: data.complement ?? '',
    district: data.district ?? '',
    zipCode: data.zipCode ?? '',
    cnae: data.cnae ?? '',
    parent: data.parent ?? null,
    cityId: data.cityId ?? '',
    stateId: data.stateId ?? '',
    codeIbge: data.codeIbge ?? null,
    account: data.account ?? null,
    timezone: data.timezone ?? '-3',
    type: data.type ?? ''
  })

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const setValues = (data: Partial<FormData>) => {
    const dataParsed = parseInitialData(data)

    setValue('id', dataParsed.id)
    setValue('name', dataParsed.name)
    setValue('number', dataParsed.number)
    setValue('unitCode', dataParsed.unitCode)
    setValue('cnpj', dataParsed.cnpj)
    setValue('corporateName', dataParsed.corporateName)
    setValue('address', dataParsed.address)
    setValue('complement', dataParsed.complement)
    setValue('district', dataParsed.district)
    setValue('zipCode', dataParsed.zipCode)
    setValue('cnae', dataParsed.cnae)
    setValue('parent', dataParsed.parent)
    setValue('cityId', dataParsed.cityId)
    setValue('stateId', dataParsed.stateId)
    setValue('codeIbge', dataParsed.codeIbge)
    setValue('account', dataParsed.account)
    setValue('timezone', dataParsed.timezone)
    setValue('type', dataParsed.type)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors }
}
