import { renderHook, waitFor } from '@testing-library/react'

import { companyMock1 } from '@/__mock__/content/api-companies.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStatePage } from '../page.hooks'
import { useMethodTabData } from './Tab.hooks'
import { FormDataSend } from './Tab.types'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/CompaniesApiV4')
const spyCompaniesApiV4 = jest.spyOn(
  require('@/@core/infra/api/CompaniesApiV4'),
  'companiesApiV4'
)

describe('@core/presentation/views/companies.id/TabData/Tab.hooks', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyCompaniesApiV4.mockImplementation(() => ({
      getById: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request success **/
    spyCompaniesApiV4.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: companyMock1
      })
    }))
    await waitFor(() => {
      result.current.method.fetchData()
    })

    const { company } = result.current.state.tabData

    expect(company.id).toBe(companyMock1.id)
    expect(company.name).toBe(companyMock1.name)
    expect(company.parentId).toBe(companyMock1.parentId)
    expect(company.cityId).toBe(companyMock1.cityId)
  })

  it('should check return the function onSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const payload: FormDataSend = {
      id: null,
      name: 'NIPON PLATING - MITSUYASU',
      cnpj: '*********',
      corporateName: 'corporate_name',
      address: 'address',
      zipCode: '123456',
      unitCode: '',
      number: '123456',
      complement: 'complement',
      district: 'district',
      cityId: 4409,
      stateId: 24,
      cnae: '1234567',
      parentId: 1,
      accountId: 1,
      codeIbge: 1234567,
      type: 'company',
      timezone: -3
    }

    /* request CREATE error **/
    await waitFor(() => result.current.state.reset())

    spyCompaniesApiV4.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.company.id).toBeUndefined()

    /* request CREATE success **/
    await waitFor(() => result.current.state.reset())

    spyCompaniesApiV4.mockImplementationOnce(() => ({
      create: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: companyMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.company.id).toBeUndefined()

    /** assigns value to key id */
    payload.id = 172

    /* request UPDATE error **/
    await waitFor(() => result.current.state.reset())

    spyCompaniesApiV4.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.company.id).toBeUndefined()

    /* request UPDATE success **/
    await waitFor(() => result.current.state.reset())

    spyCompaniesApiV4.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: companyMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit(payload)
    })
    expect(result.current.state.tabData.company.id).toBe(companyMock1.id)
  })
})
