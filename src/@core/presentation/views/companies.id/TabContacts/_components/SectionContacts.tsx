import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { FC, forwardRef, useImperativeHandle, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useListCompanyCategoriesStore from '@/@core/framework/store/hook/useListCompanyCategoryStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'

import { useLanguagePage, useStatePage } from '../../page.hooks'

import {
  useMethodTabContacts,
  useMethodTabContactsForm,
  useMethodTabContactsTable
} from '../Tab.hooks'
import { ICompanyContactPage } from '../Tab.types'
import { applyMaskPhoneNumber, formDataOutput } from '../Tab.utils'

export const SectionContacts = () => {
  const statePage = useStatePage()

  const formRef = useRef<IFormContactRef>(null)

  return (
    <>
      <FormContact ref={formRef} />
      <TableContacts
        data={statePage.tabContacts.table}
        handleEdit={(p) => formRef.current?.handleEdit(p)}
      />
    </>
  )
}

interface IFormContactRef {
  handleEdit: (p: ICompanyContactPage) => void
}
interface IFormConactProps {}

const FormContact = forwardRef<IFormContactRef, IFormConactProps>(
  (props, ref) => {
    const listCompanyCategoriesStore = useListCompanyCategoriesStore()
    const systemLoading = useSystemLoadingStore()

    const statePage = useStatePage()
    const languagePage = useLanguagePage()
    const methodTabContactsForm = useMethodTabContactsForm()

    const formFields = useFormFields()

    useImperativeHandle(ref, () => ({
      handleEdit: (value) => {
        formFields.setValues(value)
      }
    }))

    const handleSubmit = async () => {
      const result = await methodTabContactsForm.onSubmit(
        formDataOutput({
          ...formFields.values,
          companyId: statePage.tabData.company.id!
        })
      )

      const { add, update } = result as {
        add: ICompanyContactPage
        update: ICompanyContactPage
      }

      const newItems = add
        ? [...statePage.tabContacts.table.items, add]
        : [...statePage.tabContacts.table.items].map((item) =>
            item.id === update.id ? update : item
          )

      statePage.setTabContacts({
        table: {
          ...statePage.tabContacts.table,
          items: newItems
        }
      })

      formFields.setValues({})
    }

    return (
      <form
        className="mb-5 form-container"
        onSubmit={formFields.handleSubmit(handleSubmit)}
      >
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 items-end">
          <Input.Root>
            <Input.Label htmlFor="name">
              {languagePage.tabContacts.form.inputName}
            </Input.Label>
            <Input.Content
              id="name"
              value={formFields.values.name}
              onChange={(e) => {
                formFields.setValue('name', e.target.value)
              }}
              helperText={formFields.errors.name?.message}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="email">
              {languagePage.tabContacts.form.inputEmail}
            </Input.Label>
            <Input.Content
              id="email"
              value={formFields.values.email}
              onChange={(e) => {
                formFields.setValue('email', e.target.value)
              }}
              helperText={formFields.errors.email?.message}
              type="email"
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="cellphone">
              {languagePage.tabContacts.form.inputCellphone}
            </Input.Label>
            <Input.Content
              id="cellphone"
              placeholder="(00) 00000-0000"
              value={formFields.values.cellphone}
              onChange={(e) => {
                formFields.setValue(
                  'cellphone',
                  applyMaskPhoneNumber(e.target.value)
                )
              }}
              helperText={formFields.errors.cellphone?.message}
            />
          </Input.Root>

          <TagInput.Root className="w-[100%]">
            <TagInput.Label id="companyContactCategoryId">
              {languagePage.tabContacts.form.inputCategory}
            </TagInput.Label>
            <TagInput.Content
              name="companyContactCategoryId"
              value={
                listCompanyCategoriesStore.state.list.length
                  ? formFields.values.companyContactCategoryId
                  : ''
              }
              onChange={(items) =>
                formFields.setValue(
                  'companyContactCategoryId',
                  items?.[0]?.value ?? ''
                )
              }
              options={listCompanyCategoriesStore.state.list.map((el) => ({
                value: String(el.id),
                label: el.name
              }))}
              disabled={systemLoading.state.loading}
            />
          </TagInput.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            onClick={() => formFields.setValues({})}
            variant="secondary-gray"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languagePage.tabContacts.cancel
              : languagePage.tabContacts.clean}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languagePage.tabContacts.save
              : languagePage.tabContacts.add}
          </Button>
        </div>
      </form>
    )
  }
)
const useFormFields = () => {
  const { requiredField } = useLanguagePage().tabContacts

  const fieldSchema = z
    .string({ required_error: requiredField })
    .min(1, { message: requiredField })

  const formSchema = z.object({
    id: z.number().nullable(),
    name: fieldSchema,
    email: fieldSchema,
    cellphone: fieldSchema,
    companyContactCategoryId: fieldSchema
  })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (
    data: Partial<ICompanyContactPage>
  ): FormSchema => ({
    id: data.id ?? null,
    name: data.name ?? '',
    email: data.email ?? '',
    cellphone: data.cellphone ?? '',
    companyContactCategoryId: data.companyContactCategory?.id
      ? String(data.companyContactCategory?.id)
      : ''
  })

  const {
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const setValues = (data: Partial<ICompanyContactPage> = {}) => {
    const dataParsed = parseInitialData(data)

    setValue('id', dataParsed.id)
    setValue('name', dataParsed.name)
    setValue('email', dataParsed.email)
    setValue('cellphone', dataParsed.cellphone)
    setValue('companyContactCategoryId', dataParsed.companyContactCategoryId)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors, setError }
}

interface TableContactsProps {
  data: { items: ICompanyContactPage[]; lastPage: number }
  handleEdit: (p: ICompanyContactPage) => void
}
const TableContacts: FC<TableContactsProps> = (props) => {
  const languagePage = useLanguagePage()

  const methodTabContacts = useMethodTabContacts()

  const systemLoading = useSystemLoadingStore()

  const search = memory.local.get().companies.record.tabContacts

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languagePage.page.tabs.contacts}</Table.InfoTitle>
      </Table.Info>
      <Table.Header>
        <Table.Row>
          <Table.Head>{languagePage.tabContacts.table.columns.name}</Table.Head>
          <Table.Head>
            {languagePage.tabContacts.table.columns.email}
          </Table.Head>
          <Table.Head>
            {languagePage.tabContacts.table.columns.cellphone}
          </Table.Head>
          <Table.Head>
            {languagePage.tabContacts.table.columns.category}
          </Table.Head>
          <Table.Head className="w-20">
            {languagePage.tabContacts.table.columns.actions}
          </Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {!systemLoading.state.loading &&
          props.data.items.map((item) => (
            <Table.Row key={item.id}>
              <Table.Cell>{item.name}</Table.Cell>
              <Table.Cell>{item.email}</Table.Cell>
              <Table.Cell>{item.cellphone}</Table.Cell>
              <Table.Cell>
                {item.companyContactCategory?.name ?? 'N/D'}
              </Table.Cell>
              <Table.Cell width={80}>
                <Actions
                  companyContact={item}
                  onClickEdit={() => props.handleEdit(item)}
                />
              </Table.Cell>
            </Table.Row>
          ))}

        <Table.RowLoading status={systemLoading.state.loading} colSpan={5} />
      </Table.Body>

      <Table.Mobile>
        {props.data.items.map((item) => (
          <TableMobile.Item key={item.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.tabContacts.table.columns.name}
              </TableMobile.Cell>
              <TableMobile.Cell>{item.name}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.tabContacts.table.columns.email}
              </TableMobile.Cell>
              <TableMobile.Cell>{item.email}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.tabContacts.table.columns.cellphone}
              </TableMobile.Cell>
              <TableMobile.Cell>{item.cellphone}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Footer className="flex justify-end">
              <Actions
                companyContact={item}
                onClickEdit={() => props.handleEdit(item)}
              />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={props.data.lastPage}
        currentPage={search.page!}
        handleChangePage={(page) => {
          memory.local.set({
            companies: {
              record: { tabContacts: { page } }
            }
          })
          methodTabContacts.fetchData()
        }}
      />
    </Table.Root>
  )
}
const Actions: FC<{
  onClickEdit: () => void
  companyContact: ICompanyContactPage
}> = ({ onClickEdit, ...rest }) => {
  return (
    <div className="flex items-center gap-3">
      <ModalDelete {...rest} />
      <button onClick={onClickEdit}>
        <Icon
          icon="edit"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>
    </div>
  )
}

interface ModalDeleteProps {
  companyContact: ICompanyContactPage
}
const ModalDelete: FC<ModalDeleteProps> = ({ companyContact }) => {
  const [openDialog, setOpenDialog] = useState(false)

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoadingStore = useSystemLoadingStore()

  const methodTabContactsTable = useMethodTabContactsTable()

  const handleDeleteSuccess = () => {
    setOpenDialog(false)

    setTimeout(() => {
      statePage.setTabContacts({
        table: {
          ...statePage.tabContacts.table,
          items: statePage.tabContacts.table.items.filter(
            (e) => e.id !== companyContact.id
          )
        }
      })
    }, 100)

    setTimeout(() => {
      systemLoadingStore.setLoading(false)
    }, 200)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]" asChild>
        <button type="button">
          <Icon
            icon="trash01"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>
            {languagePage.tabContacts.modalDelete.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description hidden>
          {languagePage.tabContacts.modalDelete.title}
        </Dialog.Description>

        <p className="mb-2">
          {languagePage.tabContacts.modalDelete.textInfo}
          <span className="text-red-600"> {companyContact.name}</span>
        </p>

        <Dialog.Footer className="flex justify-end gap-3">
          <Dialog.Close disabled={systemLoadingStore.state.loading} asChild>
            <Button variant="secondary-gray" type="button">
              {languagePage.tabContacts.modalDelete.textCancel}
            </Button>
          </Dialog.Close>

          <Button
            variant="error-primary"
            type="button"
            onClick={() =>
              methodTabContactsTable.handleDelete(
                companyContact.id,
                handleDeleteSuccess
              )
            }
            disabled={systemLoadingStore.state.loading}
          >
            {languagePage.tabContacts.modalDelete.textConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
