import { renderHook, waitFor } from '@testing-library/react'

import {
  companyContactMock1,
  companyContactMock2
} from '@/__mock__/content/api-company-contacts.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStatePage } from '../page.hooks'
import {
  useMethodTabContacts,
  useMethodTabContactsForm,
  useMethodTabContactsTable
} from './Tab.hooks'
import { ICompanyContactPage } from './Tab.types'
import { parseDataInput } from './Tab.utils'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/CompaniesContactsApiV4')
const spyCompaniesContactsApiV4 = jest.spyOn(
  require('@/@core/infra/api/CompaniesContactsApiV4'),
  'companiesContactsApiV4'
)

describe('@core/presentation/views/companies.id/TabContacts/Tab.hooks', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function useMethodTabContacts', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabContacts(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request success **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [companyContactMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(() => {
      result.current.method.fetchData()
    })

    const { table } = result.current.state.tabContacts

    expect(table.items).toHaveLength(1)
    expect(table.lastPage).toBe(1)
  })

  it('should check return the function useMethodTabContactsForm', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabContactsForm(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    interface IResultSubmit {
      add: Partial<ICompanyContactPage>
      update: Partial<ICompanyContactPage>
    }
    let resultSubmit: IResultSubmit = {
      add: {},
      update: {}
    }

    const formData = {
      id: null,
      companyContactCategoryId: Number(
        companyContactMock1.companyContactCategory
      ),
      name: companyContactMock1.name,
      email: companyContactMock1.email,
      cellphone: companyContactMock1.cellphone,
      companyId: companyContactMock1.companyId
    }

    /* request CREATE error **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        response: {
          data: {
            name: ['campo obrigatório']
          }
        }
      })
    }))
    await waitFor(async () => {
      const res = await result.current.method.onSubmit(formData)
      resultSubmit.add.id = res.add?.id
    })
    expect(resultSubmit?.add.id).toBeUndefined()

    /* request CREATE success **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: companyContactMock1
      })
    }))

    await waitFor(async () => {
      const res = await result.current.method.onSubmit(formData)
      resultSubmit.add.id = res.add?.id
    })
    expect(resultSubmit?.add.id).toBe(1)

    /* request UPDATE error **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      const res = await result.current.method.onSubmit({ ...formData, id: 123 })
      resultSubmit.update.id = res.update?.id
    })
    expect(resultSubmit?.update.id).toBeUndefined()

    /* request UPDATE success **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 201,
        data: { ...companyContactMock1, id: 123 }
      })
    }))
    await waitFor(async () => {
      const res = await result.current.method.onSubmit({ ...formData, id: 123 })
      resultSubmit.update.id = res.update?.id
    })
    expect(resultSubmit?.update.id).toBe(123)
  })

  it('should check return the function useMethodTabContactsTable', async () => {
    const callback = jest.fn()

    const { result } = renderHook(
      () => ({
        method: useMethodTabContactsTable(),
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => {
      result.current.state.reset()

      result.current.state.setTabContacts({
        table: {
          lastPage: 1,
          items: [
            parseDataInput(companyContactMock1),
            parseDataInput(companyContactMock2)
          ]
        }
      })
    })

    /* request error **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1, callback)
    })
    expect(callback).toHaveBeenCalledTimes(0)

    /* request id not found **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 404
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleDelete(1, callback)
    })
    expect(callback).toHaveBeenCalledTimes(0)

    /* request success **/
    spyCompaniesContactsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    expect(result.current.state.tabContacts.table.items).toHaveLength(2)

    await waitFor(async () => {
      await result.current.method.handleDelete(1, callback)
    })
    expect(callback).toHaveBeenCalledTimes(1)
  })
})
