import { AxiosError } from 'axios'
import { useRouter } from 'next/router'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { companiesContactsApiV4 } from '@/@core/infra/api/CompaniesContactsApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import { useLanguagePage, useStatePage } from '../page.hooks'

import { FormDataSend, ICompanyContactPage } from './Tab.types'
import { parseDataInput } from './Tab.utils'

export const useMethodTabContacts = () => {
  const router = useRouter()
  const log = useLog()

  const statePage = useStatePage()

  const systemLoading = useSystemLoadingStore()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const search = memory.local.get().companies.record.tabContacts

      const result = await companiesContactsApiV4(http).get({
        companyId: Number(router.query?.id),
        limit: search.limit,
        page: search.page
      })

      statePage.setTabContacts({
        table: {
          items: result.data.items.map(parseDataInput),
          lastPage: result.data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'companies.id/tabContacts/useMethodTabContacts/fetchData'
      })
      statePage.setTabContacts({
        table: {
          items: [],
          lastPage: 0
        }
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { fetchData }
}

export const useMethodTabContactsForm = () => {
  const log = useLog()

  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const onSubmit = async (payload: FormDataSend) => {
    const currentId: number = payload.id!

    let valueCreate: Partial<ICompanyContactPage> | null = null
    let valueUpdate: Partial<ICompanyContactPage> | null = null
    let errors: Record<string, string> | null = null

    try {
      systemLoading.setLoading(true)

      const { status, data } = currentId
        ? await companiesContactsApiV4(http).update(currentId, {
            ...payload,
            telephone: null
          })
        : await companiesContactsApiV4(http).create({
            ...payload,
            telephone: null
          })

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(
          languagePage.tabContacts.formMessages,
          currentId,
          conditionalRequest
        ),
        type: toastTypeSwitch(conditionalRequest)
      })

      currentId
        ? (valueUpdate = { ...data, id: currentId })
        : (valueCreate = data)
    } catch (err) {
      const error = err as AxiosError<Record<keyof FormData, [string]>>

      if (error?.response?.data) {
        errors = {}
        Object.entries(error?.response?.data).forEach(([key, [value]]) => {
          errors![key] = value
        })
      }

      log.send(loggerRequest, {
        error: err,
        title: 'companies.id/tabContacts/useMethodTabContactsForm/onSubmit'
      })
    } finally {
      systemLoading.setLoading(false)
    }

    return {
      errors,
      add: valueCreate,
      update: valueUpdate
    }
  }

  return { onSubmit }
}

export const useMethodTabContactsTable = () => {
  const log = useLog()

  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const handleDelete = async (
    currentContactId: number,
    callback: () => void
  ) => {
    const { messages } = languagePage.tabContacts.modalDelete

    try {
      systemLoading.setLoading(true)

      const { status } = await companiesContactsApiV4(http).delete(
        currentContactId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(
          languagePage.tabContacts.modalDelete.messages,
          conditionalRequest
        ),
        type: toastTypeSwitch(conditionalRequest)
      })

      if (!conditionalRequest) {
        systemLoading.setLoading(false)
        return
      }

      callback()
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'companies.id/tabContacts/useMethodTabContactsTable/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })

      systemLoading.setLoading(false)
    }
  }

  return { handleDelete }
}
