import { renderHook, waitFor } from '@testing-library/react'

import { observer } from '@/@core/domain/observer'
import { companyMock1 } from '@/__mock__/content/api-companies.content'
import { companyContactMock1 } from '@/__mock__/content/api-company-contacts.content'
import { companySalesforceMock1 } from '@/__mock__/content/api-company-salesforce.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useLanguagePage, useStatePage } from './page.hooks'
import { parseDataInput as parseDataInputContact } from './TabContacts/Tab.utils'
import { parseDataInput as parseDataInputSalesforce } from './TabSalesforce/Tab.utils'

describe('src/@core/presentation/views/companies.id/page | usePageLanguage', () => {
  it('check de page title', () => {
    const { result } = renderHook(() => useLanguagePage(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.page.title).toBe('Empresa')
  })
})

describe('src/@core/presentation/views/companies.id/page | useStatePage', () => {
  beforeEach(() => {
    observer.reset()
  })

  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        tabData: {
          company: { ...companyMock1 }
        }
      })
    })

    expect(result.current.state.tabData.company.id).toBe(2)

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.tabData.company.id).toBeUndefined()
  })

  it('should exec method setTab and setTabMounted', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => {
      result.current.state.setTab('data')
    })

    expect(result.current.state.tab.active).toBe('data')

    await waitFor(() => {
      result.current.state.setTabMounted('tabData')
    })

    expect(!!result.current.state.tab.mounted?.tabData).toBeTruthy()

    await waitFor(() => result.current.state.reset())
  })

  it('should exec method setTabData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.setTabData({
        company: {
          id: 123
        }
      })
    })

    expect(result.current.state.isEdit).toBeTruthy()
  })

  it('should exec method setTabContacts', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.setTabContacts({
        table: {
          items: [parseDataInputContact(companyContactMock1)],
          lastPage: 1
        }
      })
    })
    expect(result.current.state.tabContacts.table.items).toHaveLength(1)
    expect(result.current.state.tabContacts.table.lastPage).toBe(1)
  })

  it('should exec method setTabSalesforce', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.setTabSalesforce({
        table: {
          items: [parseDataInputSalesforce(companySalesforceMock1)],
          lastPage: 1
        }
      })
    })
    expect(result.current.state.tabSalesforce.table.items).toHaveLength(1)
    expect(result.current.state.tabSalesforce.table.lastPage).toBe(1)
  })
})
