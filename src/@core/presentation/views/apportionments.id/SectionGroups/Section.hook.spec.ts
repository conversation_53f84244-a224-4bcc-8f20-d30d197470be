import { waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsGroupMock1 } from '@/__mock__/content/api-apportionments-group.content'
import { renderHookWithRedux } from '@/utils/setupTest'

import { useStatePage } from '../page.hooks'
import { useLanguageSection, useMethodSection } from './Section.hook'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsGroupsApiV3')

const spyApportionmentsGroupsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsGroupsApiV3'),
  'apportionmentsGroupsApiV3'
)

describe('src/@core/presentation/views/apportionments.id/SectionGroups/Section.hook | useMethodSection', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHookWithRedux(() => ({
      state: useStatePage(),
      method: useMethodSection(),
      toast: useSystemToastStore()
    }))

    /* request error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        response: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.groups.items).toHaveLength(0)
    expect(result.current.toast.state.toasts).toHaveLength(1)

    /* request success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [apportionmentsGroupMock1]
        }
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.groups.items).toHaveLength(1)
    expect(result.current.toast.state.toasts).toHaveLength(0)
  })

  it('should check return the function handleDelete', async () => {
    const { result } = renderHookWithRedux(() => ({
      state: useStatePage(),
      method: useMethodSection(),
      toast: useSystemToastStore()
    }))

    /* request error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: { items: [] }
      }),
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.state.groups.items).toHaveLength(0)
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Error removing group'
    )

    /* request success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyApportionmentsGroupsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: { items: [] }
      }),
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.state.groups.items).toHaveLength(0)
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Group successfully removed'
    )
  })
})

describe('src/@core/presentation/views/apportionments.id/SectionGroups/Section.hook | useLanguageSection', () => {
  it('check de section title', () => {
    const { result } = renderHookWithRedux(() => useLanguageSection())

    expect(result.current.title).toBe('Groups')
  })
})
