import { useRouter } from 'next/router'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsGroupsApiV3 } from '@/@core/infra/api/ApportionmentsGroupsApiV3'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { toastRequestMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import { useStatePage } from '../page.hooks'

export const useMethodSection = () => {
  const router = useRouter()
  const log = useLog()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const statePage = useStatePage()

  const languageSection = useLanguageSection()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const search = memory.local.get().apportionments.record.sectionGroups

      const { data } = await apportionmentsGroupsApiV3(http).get({
        ...search,
        apportionmentId: Number(router.query.id)
      })

      statePage.set({
        groups: {
          items: data.items,
          lastPage: data.lastPage,
          total: data.total
        }
      })
    } catch (error) {
      systemToast.addToast({
        message: languageSection.errors.request,
        type: 'error'
      })
      log.send(loggerRequest, {
        error,
        title: 'apportionments.id/SectionGroups/useMethodSection/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const handleDelete = async (groupId: number) => {
    const { messages } = languageSection.table.modalDelete

    try {
      systemLoading.setLoading(true)

      const { status } = await apportionmentsGroupsApiV3(http).delete(groupId)

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      await fetchData()

      return true
    } catch (error) {
      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      log.send(loggerRequest, {
        error,
        title:
          'companies.id/tabIntegration/useMethodTabIntegrationTable/handleDelete'
      })
      return false
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { fetchData, handleDelete }
}

export const useLanguageSection = () => {
  const { lang } = useSystemLanguageStore().state
  const { pages, modalDelete, errors } = languageByMode(lang)

  const { sectionGroups } = pages.apportionmentsId

  return {
    ...sectionGroups,
    modalDelete,
    errors
  }
}
