import { FC, useRef } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { memory } from '@/@core/infra/memory'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { But<PERSON> } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { useDebounceFunction } from '@/hooks/useDebouce'

import { useStatePage } from '../page.hooks'
import { IApportionmentGroupPage } from '../page.types'
import { useLanguageSection, useMethodSection } from './Section.hook'

interface SectionGroupsProps {
  openGroupModal: (p: Partial<IApportionmentGroupPage>) => void
}
export const SectionGroups = ({ openGroupModal }: SectionGroupsProps) => {
  const inputQuery = useRef<HTMLInputElement | null>(null)

  const searchFields = memory.local.get().apportionments.record.sectionGroups

  const systemLoading = useSystemLoadingStore()
  const statePage = useStatePage()

  const languageSection = useLanguageSection()
  const methodSection = useMethodSection()

  const handleInputQuery = useDebounceFunction(() => {
    methodSection.fetchData().then(() => {
      inputQuery.current?.focus()
    })
  }, 250)

  return (
    <Table.Root className="mb-4" classNameWrapper="block">
      <Table.Info className="justify-between">
        <Table.InfoTitle className="text-primary sm:mr-auto">
          {languageSection.title}
        </Table.InfoTitle>

        <Input.Root>
          <Input.Content
            ref={inputQuery}
            placeholder="Pesquisar"
            defaultValue=""
            onChange={({ target }) => {
              memory.local.set({
                apportionments: {
                  record: { sectionGroups: { q: target.value, page: 1 } }
                }
              })
              handleInputQuery()
            }}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <Button onClick={() => openGroupModal({})}>
          <Icon
            icon="plus"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            className="icon-menu-primary"
          />
        </Button>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head>ID</Table.Head>
          <Table.Head>{languageSection.table.columns.name}</Table.Head>
          <Table.Head>{languageSection.table.columns.equipments}</Table.Head>
          <Table.Head>{languageSection.table.columns.actions}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.groups.items.map((group) => (
          <Table.Row key={group.id}>
            <Table.Cell>{group.id}</Table.Cell>
            <Table.Cell>{group.name}</Table.Cell>
            <Table.Cell>{group.equipments.length}</Table.Cell>
            <Table.Cell width={80} role="td-actions">
              <div className="flex items-center gap-3">
                <ModalDelete groupId={group.id} groupName={group.name} />
                <button
                  className="table-td-action hover:cursor-pointer"
                  onClick={() => openGroupModal(group)}
                >
                  <Icon
                    icon="edit"
                    className="icon-menu-primary"
                    height="20"
                    width="20"
                    viewBox="0 0 20 20"
                  />
                </button>
              </div>
            </Table.Cell>
          </Table.Row>
        ))}
        {/* <Table.RowLoading status={systemLoading.state.loading} colSpan={4} /> */}
      </Table.Body>

      <Table.Paginate
        currentPage={searchFields.page}
        lastPage={statePage.groups.lastPage}
        handleChangePage={(page) => {
          memory.local.set({
            apportionments: {
              record: { sectionGroups: { page } }
            }
          })
          methodSection.fetchData()
        }}
      />
    </Table.Root>
  )
}

interface ModalDeleteProps {
  groupId: number
  groupName: string
}
const ModalDelete: FC<ModalDeleteProps> = ({ groupId, groupName }) => {
  const modalRef = useRef<IModalRootRef>(null)

  const systemLoading = useSystemLoadingStore()

  const languageSection = useLanguageSection()
  const methodSection = useMethodSection()

  return (
    <>
      <button
        className="flex *:m-auto size-7"
        onClick={() => modalRef.current?.open()}
      >
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>

      <Modal.Root ref={modalRef} disabled={systemLoading.state.loading}>
        <Modal.Title>{languageSection.title}</Modal.Title>

        <Modal.Content>
          <p className="text-primary">
            <span>{languageSection.table.modalDelete.textInfo}</span>{' '}
            <b>{groupName}</b>
          </p>
        </Modal.Content>

        <Modal.Footer>
          <Button
            type="button"
            onClick={() => modalRef.current?.close()}
            disabled={systemLoading.state.loading}
          >
            {languageSection.table.modalDelete.textCancel}
          </Button>
          <Button
            type="button"
            onClick={() =>
              methodSection.handleDelete(groupId).then((status) => {
                status && modalRef.current?.close()
              })
            }
            disabled={systemLoading.state.loading}
            variant="error-primary"
          >
            {languageSection.modalDelete.btnConfirm(
              languageSection.table.modalDelete.title
            )}
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
