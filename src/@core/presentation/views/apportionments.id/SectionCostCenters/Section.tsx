import { useRef } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { memory } from '@/@core/infra/memory'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { useDebounceFunction } from '@/hooks/useDebouce'

import { useStatePage } from '../page.hooks'
import { IApportionmentCostCenterPage } from '../page.types'
import { useLanguageSection, useMethodSection } from './Section.hook'

interface SectionCostCentersProps {
  openCostCenterModal: (p: Partial<IApportionmentCostCenterPage>) => void
  openCostCenterMultipleModal: () => void
}
export const SectionCostCenters = ({
  openCostCenterModal,
  openCostCenterMultipleModal
}: SectionCostCentersProps) => {
  const inputQuery = useRef<HTMLInputElement | null>(null)

  const searchFields =
    memory.local.get().apportionments.record.sectionCostCenters

  const systemLoading = useSystemLoadingStore()
  const statePage = useStatePage()

  const languageSection = useLanguageSection()
  const methodSection = useMethodSection()

  const handleInputQuery = useDebounceFunction(() => {
    methodSection.fetchData().then(() => {
      inputQuery.current?.focus()
    })
  }, 250)

  return (
    <Table.Root className="mb-4" classNameWrapper="block">
      <Table.Info className="justify-between">
        <Table.InfoTitle className="text-primary sm:mr-auto">
          {languageSection.title}
        </Table.InfoTitle>

        <Input.Root>
          <Input.Content
            ref={inputQuery}
            placeholder="Pesquisar"
            defaultValue=""
            onChange={({ target }) => {
              memory.local.set({
                apportionments: {
                  record: { sectionCostCenters: { q: target.value } }
                }
              })
              handleInputQuery()
            }}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <Button onClick={() => openCostCenterModal({})}>
          <Icon
            icon="multiplePlus"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            className="icon-menu-primary"
          />
        </Button>

        <Button onClick={() => openCostCenterMultipleModal()}>
          <Icon
            icon="plus"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            className="icon-menu-primary"
          />
        </Button>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head>ID</Table.Head>
          <Table.Head>{languageSection.table.columns.name}</Table.Head>
          <Table.Head>{languageSection.table.columns.tariffs}</Table.Head>
          <Table.Head>{languageSection.table.columns.equipments}</Table.Head>
          <Table.Head>{languageSection.table.columns.actions}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.costCenters.items.map((costCenter) => (
          <Table.Row key={costCenter.id}>
            <Table.Cell>{costCenter.id}</Table.Cell>
            <Table.Cell>{costCenter.name}</Table.Cell>
            <Table.Cell>
              <Badge.Root variant="alert" className="w-min">
                <Badge.Content>
                  {costCenter.apportionmentTariffType.name}
                </Badge.Content>
              </Badge.Root>
            </Table.Cell>
            <Table.Cell>{costCenter.equipments.length}</Table.Cell>
            <Table.Cell width={80} role="td-actions">
              <div className="flex items-center gap-3">
                {/* <ModalDelete groupId={group.id} groupName={group.name} /> */}
                <button
                  className="table-td-action hover:cursor-pointer"
                  onClick={() => methodSection.downloadCostCenter()}
                >
                  <Icon
                    icon="trash01"
                    className="icon-menu-primary"
                    height="20"
                    width="20"
                    viewBox="0 0 20 20"
                  />
                </button>

                <button
                  className="table-td-action hover:cursor-pointer"
                  onClick={() => openCostCenterModal(costCenter)}
                >
                  <Icon
                    icon="edit"
                    className="icon-menu-primary"
                    height="20"
                    width="20"
                    viewBox="0 0 20 20"
                  />
                </button>
              </div>
            </Table.Cell>
          </Table.Row>
        ))}
        {/* <Table.RowLoading status={systemLoading.state.loading} colSpan={5} /> */}
      </Table.Body>

      <Table.Paginate
        currentPage={searchFields.page}
        lastPage={statePage.costCenters.lastPage}
        handleChangePage={(page) => {
          memory.local.set({
            apportionments: {
              record: { sectionCostCenters: { page } }
            }
          })
          methodSection.fetchData()
        }}
      />
    </Table.Root>
  )
}
