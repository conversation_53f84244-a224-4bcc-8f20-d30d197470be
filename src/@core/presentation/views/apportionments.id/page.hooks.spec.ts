import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentMock1 } from '@/__mock__/content/api-apportionment.content'
import {
  apportionmentPeriodMock1,
  apportionmentPeriodMock2
} from '@/__mock__/content/api-apportionments-periods.content'
import {
  apportionmentResultMock1,
  apportionmentResultMock2
} from '@/__mock__/content/api-apportionments-results.content'
import {
  costCenterResultMock1,
  costCenterResultMock2
} from '@/__mock__/content/api-cost-centers-results'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useLanguagePage, useMethodPage, useStatePage } from './page.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsApiV3')
const spyApportionmentsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsApiV3'),
  'apportionmentsApiV3'
)

jest.mock('@/@core/infra/api/ApportionmentsResultsV3')
const spyApportionmentsResultsV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsResultsV3'),
  'apportionmentsResultsV3'
)

jest.mock('@/@core/infra/api/ApportionmentsPeriodsApiV3')
const spyApportionmentsPeriodsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsPeriodsApiV3'),
  'apportionmentsPeriodsApiV3'
)

jest.mock('@/@core/infra/api/CostCentersResultsApiV3')
const spyCostCentersResultsApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCentersResultsApiV3'),
  'costCentersResultsApiV3'
)

describe('src/@core/presentation/views/apportionments.id/SectionGroups/Section.hook | useStatePage', () => {
  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    /** check empty value */
    expect(result.current.state.apportionment).toEqual({})

    /** set values */
    await waitFor(() => {
      result.current.state.set({
        apportionment: {
          id: 95
        }
      })
    })

    /** check filled value */
    expect(result.current.state.apportionment).not.toEqual({})
  })
})

describe('src/@core/presentation/views/apportionments.id/SectionGroups/Section.hook | useMethodPage', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        state: useStatePage(),
        method: useMethodPage()
      }),
      { wrapper: AppStoreProvider }
    )

    /* fetchData spyApportionmentsApiV3 with error **/
    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockRejectedValue({ status: 500, data: null })
    }))
    spyApportionmentsResultsV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({ status: 200, data: { items: [] } })
    }))
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.apportionment).toEqual({})
    expect(result.current.state.apportionmentResults).toHaveLength(0)
    expect(result.current.state.apportionmentPeriods).toHaveLength(0)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Ocorreu um erro, tente novamente mais tarde'
    )

    /* request spyApportionmentsApiV3 successful without data **/
    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    spyApportionmentsResultsV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({ status: 200, data: { items: [] } })
    }))
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.apportionment).toEqual({})
    expect(result.current.state.apportionmentResults).toHaveLength(0)
    expect(result.current.state.apportionmentPeriods).toHaveLength(0)

    /* fetchData others requests successful **/
    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: { ...apportionmentMock1 }
      })
    }))
    spyApportionmentsResultsV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    spyCostCentersResultsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [costCenterResultMock1, costCenterResultMock2],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.apportionment).not.toEqual({})
    expect(result.current.state.apportionmentResults).toHaveLength(0)
    expect(result.current.state.apportionmentPeriods).toHaveLength(0)

    /* fetchData successful **/
    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: { ...apportionmentMock1 }
      })
    }))
    spyApportionmentsResultsV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [apportionmentResultMock1, apportionmentResultMock2],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [apportionmentPeriodMock1, apportionmentPeriodMock2],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    spyCostCentersResultsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [costCenterResultMock1, costCenterResultMock2],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.apportionment).not.toEqual({})
    expect(result.current.state.apportionmentResults).toHaveLength(2)
    expect(result.current.state.apportionmentPeriods).toHaveLength(2)
  })

  it('should check return the function getResultHistoryByMonth', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        state: useStatePage(),
        method: useMethodPage()
      }),
      { wrapper: AppStoreProvider }
    )

    /* fetchData with error **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersResultsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({ status: 500, data: null })
    }))

    await waitFor(async () => {
      await result.current.method.getResultHistoryByMonth('2025-06-01')
    })

    expect(result.current.state.resultsHistory.items).toHaveLength(0)
    expect(result.current.state.resultsHistory.monthActive).toBe('')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Ocorreu um erro, tente novamente mais tarde'
    )

    /* fetchData with success **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersResultsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [costCenterResultMock1, costCenterResultMock2],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.getResultHistoryByMonth('2025-06-01')
    })

    expect(result.current.state.resultsHistory.items).toHaveLength(2)
    expect(result.current.state.resultsHistory.monthActive).toBe('2025-06-01')
  })
})

describe('src/@core/presentation/views/apportionments.id/SectionGroups/Section.hook | useLanguagePage', () => {
  it('check de page title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguagePage()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.page.title).toBe('Rateio')
  })
})
