import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsGroupsEquipmentsApiV3 } from '@/@core/infra/api/ApportionmentsGroupsEquipmentsApiV3'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import {
  ApportionmentCalculateModal,
  IApportionmentCalculateModalRef
} from '@/@core/presentation/shared/ApportionmentCalculateModal'
import { IApportionmentExportDataModalRef } from '@/@core/presentation/shared/ApportionmentExportDataModal/Modal.types'
import {
  ApportionmentGroupModal,
  IApportionmentGroupModalRef,
  parseValueGruopEquipment
} from '@/@core/presentation/shared/ApportionmentGroupModal'
import { IApportionmentModalRef } from '@/@core/presentation/shared/ApportionmentModal/Modal.types'
import {
  CompositionValueModal,
  ICompositionValueModalRef
} from '@/@core/presentation/shared/CompositionValueModal'
import {
  CostCenterModal,
  ICostCenterModalRef
} from '@/@core/presentation/shared/CostCenterModal'
import {
  CostCenterMultipleModal,
  ICostCenterMultipleModalRef
} from '@/@core/presentation/shared/CostCenterMultipleModal'
import { HeaderList } from '@/@core/presentation/shared/pages/HeaderList'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'
import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'
import { Button } from '@/@core/presentation/shared/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/@core/presentation/shared/ui/dropdown-menu'
import {
  faEllipsisVertical,
  Icon as IconOld
} from '@/@core/presentation/shared/ui/icon'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import checkQueryIdIsNumber from '@/@core/utils/checkIsNumber'
import { useRouter } from 'next/router'
import { useEffect, useRef } from 'react'
import { ApportionmentExportDataModal } from '../../shared/ApportionmentExportDataModal/Modal'
import { ApportionmentModal } from '../../shared/ApportionmentModal/Modal'
import { useLanguagePage, useMethodPage, useStatePage } from './page.hooks'
import {
  IApportionmentCostCenterPage,
  IApportionmentGroupPage
} from './page.types'
import { SectionApportionment } from './SectionAppotionment/Section'
import { SectionCostCenters } from './SectionCostCenters/Section'
import { useMethodSection as useMethodSectionCostCenter } from './SectionCostCenters/Section.hook'
import { SectionGroups } from './SectionGroups/Section'
import { useMethodSection as useMethodSectionGroups } from './SectionGroups/Section.hook'
import { SectionResultsHistory } from './SectionResultsHistory/Section'

const menuIcons = [
  <Icon icon="edit" className="icon-menu-primary" />,
  <Icon icon="plus" className="icon-menu-primary" />,
  <Icon icon="plus" className="icon-menu-primary" />,
  <Icon icon="plus" className="icon-menu-primary" />,
  <Icon icon="multiplePlus" className="icon-menu-primary" />,
  <Icon icon="refreshccw05" className="icon-menu-primary" />,
  <Icon icon="download-01" className="icon-menu-primary" />
]
export const Page = () => {
  const isMounted = useRef<boolean>(false)

  const router = useRouter()
  const systemToast = useSystemToastStore()
  const systemLoading = useSystemLoadingStore()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()
  const methodPage = useMethodPage()

  const methodSectionGroups = useMethodSectionGroups()
  const methodSectionCostCenter = useMethodSectionCostCenter()

  const currentTitle = statePage.apportionment?.name ?? ''

  useTitlePage(currentTitle)
  useBreadcrumb('apportionments.id')

  const apportionmentRef = useRef<IApportionmentModalRef>(null)
  const costCenterRef = useRef<ICostCenterModalRef>(null)
  const apportionmentGroupRef = useRef<IApportionmentGroupModalRef>(null)
  const compositionValueRef = useRef<ICompositionValueModalRef>(null)
  const costCenterMultipleRef = useRef<ICostCenterMultipleModalRef>(null)
  const apportionmentCalculateModalRef =
    useRef<IApportionmentCalculateModalRef>(null)
  const apportionmentExportDataModalRef =
    useRef<IApportionmentExportDataModalRef>(null)

  useEffect(() => {
    if (!isMounted.current && checkQueryIdIsNumber(router.query.id)) {
      isMounted.current = true
      methodPage.fetchData()
      // methodSectionResultHistory.fetchData()
      methodSectionCostCenter.fetchData()
      methodSectionGroups.fetchData()
      return
    }

    return () => {
      statePage.reset()
      memory.local.reset('apportionments')
    }
  }, [])

  const openGroupModal = async (payload: Partial<IApportionmentGroupPage>) => {
    const data = {
      id: payload?.id,
      name: payload?.name
    }
    apportionmentGroupRef.current?.setValues?.(data)
    apportionmentGroupRef.current?.open?.()

    if (!payload?.id) return

    try {
      systemLoading.setData({ modalLoading: true })

      const { status, data } = await apportionmentsGroupsEquipmentsApiV3(
        http
      ).get({
        apportionmentGroupId: payload?.id
      })

      const equipments =
        status === 200 ? data.items.map(parseValueGruopEquipment) : []

      apportionmentGroupRef.current?.setValues?.({ equipments })
    } catch (error) {
      systemToast.addToast({
        message: languagePage.modal.errors.request,
        type: 'error'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const openCostCenterModal = async (
    payload: Partial<IApportionmentCostCenterPage> = {}
  ) => {
    const data = {
      id: payload?.id,
      name: payload?.name,
      email: payload?.email,
      apportionmentId: payload?.apportionmentId,
      apportionmentTariffTypeId: payload?.apportionmentTariffType?.id,
      companyId: payload?.companyId
    }
    costCenterRef.current?.handler?.(data)
    costCenterRef.current?.open?.()
  }
  const openCostCenterMultipleModal = () => {
    costCenterMultipleRef.current?.setValues?.({})
    costCenterMultipleRef.current?.open()
  }
  const menuEvents = [
    () => {
      apportionmentRef.current?.handler?.(statePage.apportionment)
      apportionmentRef.current?.open?.()
    },
    () => {
      costCenterRef.current?.handler?.({})
      costCenterRef.current?.open?.()
    },
    () => {
      apportionmentGroupRef.current?.open?.()
    },
    () => {
      compositionValueRef.current?.open?.()
    },
    () => {
      costCenterMultipleRef.current?.open?.()
    },
    () => {
      apportionmentCalculateModalRef.current?.open?.()
    },
    () => {
      apportionmentExportDataModalRef.current?.open?.()
    }
  ]

  return (
    <PageContent>
      <Breadcrumbs />

      <HeaderList.Root>
        <HeaderList.Content title={currentTitle} className="mr-auto" />

        <Button
          className="hidden lg:flex gap-1"
          onClick={menuEvents[5]}
          disabled={systemLoading.state.loading}
        >
          {menuIcons[5]}
          {languagePage.page.dropdownItems[5]}
        </Button>

        <Button
          className="hidden lg:flex"
          onClick={menuEvents[6]}
          disabled={systemLoading.state.loading}
        >
          {menuIcons[6]}
          {languagePage.page.dropdownItems[6]}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger
            className="block size-[43px] border-[0.75px] border-primary rounded-[8px] cursor-pointer text-primary"
            disabled={systemLoading.state.loading}
          >
            <IconOld icon={faEllipsisVertical} />
          </DropdownMenuTrigger>

          <DropdownMenuContent align="end">
            {Array.from({ length: 7 }).map((...{ 1: index }) => (
              <DropdownMenuItem
                key={index}
                className={cn(
                  'flex items-center gap-2 px-2 cursor-pointer min-w-[250px] lg:min-w-[270px]'
                )}
                onClick={menuEvents[index]}
              >
                {menuIcons[index]}
                {languagePage.page.dropdownItems[index]}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </HeaderList.Root>

      <SectionApportionment />

      <SectionResultsHistory />

      <SectionCostCenters
        openCostCenterModal={openCostCenterModal}
        openCostCenterMultipleModal={openCostCenterMultipleModal}
      />

      <SectionGroups openGroupModal={openGroupModal} />

      <CostCenterMultipleModal
        ref={costCenterMultipleRef}
        apportionmentId={Number(statePage.apportionment?.id)}
        companyId={statePage.apportionment.company?.id}
        apportionmentTypeId={statePage.apportionment.apportionmentType?.id}
      />

      <ApportionmentModal
        ref={apportionmentRef}
        // handleTabPeriods
      />

      <CompositionValueModal
        ref={compositionValueRef}
        apportionmentId={Number(statePage.apportionment?.id)}
        apportionmentTypeId={Number(
          statePage.apportionment.apportionmentType?.id
        )}
      />

      <CostCenterModal
        ref={costCenterRef}
        apportionmentId={Number(statePage.apportionment?.id)}
        tabDataSubmitSuccess={methodSectionCostCenter.fetchData}
        handleCreatedTabData={methodSectionCostCenter.fetchData}
      />

      <ApportionmentGroupModal
        ref={apportionmentGroupRef}
        apportionmentId={Number(statePage.apportionment?.id)}
        companyId={Number(statePage.apportionment.company?.id)}
        handleSubmitSuccess={methodSectionGroups.fetchData}
      />

      <ApportionmentCalculateModal
        ref={apportionmentCalculateModalRef}
        hasPeriods={statePage.apportionmentPeriods.length > 0}
        periods={statePage.apportionmentPeriods.map(({ period }) => period)}
        apportionmentId={Number(statePage.apportionment?.id)}
      />

      <ApportionmentExportDataModal
        ref={apportionmentExportDataModalRef}
        hasPeriods={statePage.apportionmentPeriods.length > 0}
        periods={statePage.apportionmentPeriods.map(({ period }) => period)}
        apportionmentId={Number(statePage.apportionment?.id)}
      />
    </PageContent>
  )
}
