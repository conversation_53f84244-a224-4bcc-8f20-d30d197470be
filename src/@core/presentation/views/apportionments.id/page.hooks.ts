import dayjs from 'dayjs'
import { useRouter } from 'next/router'
import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import {
  apportionmentsApiV3,
  apportionmentsPeriodsApiV3,
  apportionmentsResultsV3
} from '@/@core/infra/api'
import { costCentersResultsApiV3 } from '@/@core/infra/api/CostCentersResultsApiV3'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

import { stateData } from './page.content'
import { ICostCenterResultPage, IState } from './page.types'
import {
  parseApportionmentData,
  parseApportionmentPeriodData,
  parseApportionmentResultData,
  parseCostCenterResultData,
  renderTableResultHistoryData
} from './page.utils'

let resultsHistory: {
  monthActive: string
  query: string
  items: ICostCenterResultPage[]
  itemsFiltered: ICostCenterResultPage[]
  page: number
  lastPage: number
} = {
  monthActive: '',
  query: '',
  items: [],
  itemsFiltered: [],
  page: 1,
  lastPage: 0
}

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => set((state) => ({ ...state, ...initialData })),
  reset: () => set((state) => ({ ...state, ...stateData }))
}))

export const useStatePage = () => {
  const state = statePage()

  const isEdit = !!state.apportionment?.id

  return { ...state, isEdit }
}

export const useMethodPage = () => {
  const router = useRouter()
  const log = useLog()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const fetchData = async () => {
    const _date = dayjs()

    try {
      systemLoading.setLoading(true)

      const resultGetId = await apportionmentsApiV3(http).getById(
        Number(router.query?.id)
      )

      if (resultGetId.status !== 200) return

      const resultResults = await apportionmentsResultsV3(http).get({
        apportionmentIds: [Number(router.query?.id)],
        initialDate: _date.subtract(11, 'month').format('YYYY-MM-DD'),
        finalDate: _date.format('YYYY-MM-DD')
      })

      const resultPeriods = await apportionmentsPeriodsApiV3(http).get({
        apportionmentId: Number(router.query?.id),
        sort: 'period'
      })

      let apportionment = parseApportionmentData(resultGetId.data)

      const apportionmentResults = resultResults.data.items.map(
        parseApportionmentResultData
      )

      const lastResult = apportionmentResults?.[0] ?? null

      apportionment = {
        ...apportionment,
        lastResult: lastResult
      }

      const apportionmentPeriods = resultPeriods.data.items.map(
        parseApportionmentPeriodData
      )

      if (lastResult) {
        const _date = dayjs(lastResult.date)

        const initialDate = _date.startOf('month').format('YYYY-MM-DD')
        const finalDate = _date.endOf('month').format('YYYY-MM-DD')

        await costCentersResultsApiV3(http)
          .get({
            apportionmentId: Number(router.query?.id),
            limit: 999,
            initialDate,
            finalDate
          })
          .then(({ data }) => {
            const items = data.items.map(parseCostCenterResultData)

            resultsHistory = {
              monthActive: initialDate,
              ...renderTableResultHistoryData({ items })
            }
          })
      }

      statePage.set({
        apportionment,
        apportionmentResults,
        apportionmentPeriods,
        resultsHistory
      })
    } catch (error) {
      systemToast.addToast({
        message: languagePage.errors.request,
        type: 'error'
      })
      log.send(loggerRequest, {
        error,
        title: 'apportionments.id/useMethodPage/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const getResultHistoryByMonth = async (month: string) => {
    try {
      systemLoading.setLoading(true)

      const _date = dayjs(month)

      const search =
        memory.local.get().apportionments.record.sectionResultsHistory

      const {
        data: { items }
      } = await costCentersResultsApiV3(http).get({
        ...search,
        apportionmentId: Number(router.query?.id),
        initialDate: _date.startOf('month').format('YYYY-MM-DD'),
        finalDate: _date.endOf('month').format('YYYY-MM-DD')
      })

      statePage.set({
        resultsHistory: {
          monthActive: month,
          ...renderTableResultHistoryData({ items })
        }
      })
    } catch (error) {
      systemToast.addToast({
        message: languagePage.errors.request,
        type: 'error'
      })
      log.send(loggerRequest, {
        error,
        title: 'apportionments.id/page/useMethodPage/getResultHistoryByMonth'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { fetchData, getResultHistoryByMonth }
}

export const useLanguagePage = () => {
  const { lang } = useSystemLanguageStore().state
  const { pages, errors } = languageByMode(lang)

  const {
    title,
    sectionApportionment,
    sectionCostCenter,
    sectionGroups,
    dropdownItems
  } = pages.apportionmentsId

  return {
    page: {
      title,
      dropdownItems,
      sectionApportionment,
      sectionCostCenter,
      sectionGroups
    },
    errors,
    modal: {
      errors
    }
  }
}
