import { zodResolver } from '@hookform/resolvers/zod'
import { FC, forwardRef, useImperativeHandle, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useListOperationalRuleStore from '@/@core/framework/store/hook/useListOperationalRuleStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import {
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { normalizeRule } from '@/@core/utils/normalizesOperationalRule'

import { useLanguagePage, useStatePage } from '../../page.hooks'
import { useMethodTabTelemetry } from '../Tab.hooks'
import { EquipmentVirtualFormData, IEquipmentVirtualPage } from '../Tab.types'
import { equipmentVirtualOutput, filterOperationalRules } from '../Tab.utils'

export const SectionComposition = () => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const formRef = useRef<IFormCompositionRef>(null)

  return (
    <div className="flex flex-col gap-y-4">
      <h1
        className={cn(
          'inline-block text-[25px] leading-[38px] font-acuminPro-Semibold text-comerc-grayLight-900',
          'dark:text-comerc-grayLight-50'
        )}
      >
        {languagePage.tabTelemetry.equipmentVirtual.title}
      </h1>
      <FormComposition ref={formRef} />
      <TableEquipmentComposition
        data={statePage.tabTelemetryEquipmentVirtual.table}
        handleEdit={(p) => formRef.current?.handleEdit(p)}
      />
    </div>
  )
}

interface IFormCompositionRef {
  handleEdit: (p: IEquipmentVirtualPage) => void
}

interface IFormCompositionProps {}

const FormComposition = forwardRef<IFormCompositionRef, IFormCompositionProps>(
  (props, ref) => {
    const systemLoading = useSystemLoadingStore()

    const statePage = useStatePage()
    const languagePage = useLanguagePage()
    const methodTabTelemetry = useMethodTabTelemetry()

    const listOperationalRulesStore = useListOperationalRuleStore()

    const formFields = useFormFields()

    useImperativeHandle(ref, () => ({
      handleEdit: (value) => {
        formFields.setValues({
          ...value,
          equipmentComposition: [value.equipmentComposition],
          rule: normalizeRule(value.rule)
        })
      }
    }))

    const handleSubmit = async () => {
      const result = await methodTabTelemetry.equipmentVirtualOnSubmit(
        equipmentVirtualOutput({
          ...formFields.values,
          equipmentId: statePage.tabData.equipment.id!,
          equipmentComposition: formFields.values.equipmentComposition
        })
      )

      const { add, update } = result as {
        add: IEquipmentVirtualPage
        update: IEquipmentVirtualPage
      }

      const newItems = add
        ? [...statePage.tabTelemetryEquipmentVirtual.table.items, add]
        : [...statePage.tabTelemetryEquipmentVirtual.table.items].map((item) =>
            item.id === update?.id ? update : item
          )

      statePage.setTabTelemetryEquipmentVirtual({
        table: {
          ...statePage.tabTelemetryEquipmentVirtual.table,
          items: newItems
        }
      })

      formFields.setValues({})
    }

    return (
      <form
        className="mb-5 form-container"
        onSubmit={formFields.handleSubmit(handleSubmit)}
      >
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4 items-baseline">
          <TagInput.Root className="mt-1">
            <TagInput.Label>
              {languagePage.tabTelemetry.equipmentVirtual.form.inputEquipment}
            </TagInput.Label>
            <TagInput.ContentApi
              disabled={systemLoading.state.loading || !!formFields.values.id}
              value={formatInputValues(formFields.values.equipmentComposition)}
              onChange={(values) =>
                formFields.setValue(
                  'equipmentComposition',
                  formatOutputValues(values)
                )
              }
              featchData={async (p) => {
                const result = await equipmentsApiV4(http).get({
                  ...p,
                  accountId: statePage.tabData.equipment.account?.id,
                  typeId: String(statePage.tabData.equipment.device?.typeId)
                })
                result.data.items = result.data.items.filter(
                  (item) => item.id !== statePage.tabData.equipment.id
                )
                return result
              }}
              helperText={formFields.errors.equipmentComposition?.message}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label>
              {languagePage.tabData.form.inputRuleOperational}
            </TagInput.Label>
            <TagInput.Content
              value={formFields.values.rule ? [formFields.values.rule] : []}
              onChange={(items) =>
                formFields.setValue('rule', items?.[0]?.value ?? '')
              }
              options={filterOperationalRules(
                listOperationalRulesStore.state.list
              ).map((el) => ({
                value: String(el.title),
                label: el.title
              }))}
              disabled={systemLoading.state.loading}
              helperText={formFields.errors.rule?.message}
            />
          </TagInput.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            onClick={() => formFields.setValues({})}
            variant="secondary-gray"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languagePage.tabData.cancel
              : languagePage.tabData.clean}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languagePage.tabData.save
              : languagePage.tabData.add}
          </Button>
        </div>
      </form>
    )
  }
)

const useFormFields = () => {
  const { requiredField } = useLanguagePage().tabData

  const formSchema = z.object({
    id: z.number().nullable(),
    equipmentComposition: z
      .array(
        z.object({
          id: z.number(),
          name: z.string()
        })
      )
      .min(1, { message: requiredField }),
    rule: z.string().min(1, { message: requiredField })
  })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (
    data: Partial<EquipmentVirtualFormData>
  ): FormSchema => ({
    id: data.id ?? null,
    equipmentComposition: data.equipmentComposition ?? [],
    rule: data.rule ?? ''
  })

  const {
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const setValues = (data: Partial<EquipmentVirtualFormData> = {}) => {
    const dataParsed = parseInitialData(data)

    setValue('id', dataParsed.id)
    setValue('equipmentComposition', dataParsed.equipmentComposition)
    setValue('rule', dataParsed.rule)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors, setError }
}

interface TableEquipmentCompositionProps {
  data: { items: IEquipmentVirtualPage[]; lastPage: number }
  handleEdit: (p: IEquipmentVirtualPage) => void
}

const TableEquipmentComposition: FC<TableEquipmentCompositionProps> = (
  props
) => {
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()

  const statePage = useStatePage()
  const search =
    memory.local.get().equipments.record.tabTelemetryEquipmentVirtual
  const methodTabTelemetry = useMethodTabTelemetry()

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>
          {languagePage.tabTelemetry.equipmentVirtual.title}
        </Table.InfoTitle>

        <Table.InfoBadge className="lg:mr-auto h-[22px]">
          {statePage.tabTelemetryEquipmentVirtual.table.total}
          <span className="hidden md:inline-block ml-1">
            {languagePage.tabTelemetry.equipmentVirtual.table.totalRegisters}
          </span>
        </Table.InfoBadge>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head>
            {languagePage.tabTelemetry.equipmentVirtual.table.columns.equipment}
          </Table.Head>
          <Table.Head>
            {
              languagePage.tabTelemetry.equipmentVirtual.table.columns
                .operationalRule
            }
          </Table.Head>
          <Table.Head className="w-20">
            {languagePage.tabTelemetry.equipmentVirtual.table.columns.actions}
          </Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {!systemLoading.state.loading &&
          props.data.items.map((item) => (
            <Table.Row key={item.id}>
              <Table.Cell>{item.equipmentComposition.name}</Table.Cell>
              <Table.Cell>{normalizeRule(item.rule)}</Table.Cell>
              <Table.Cell width={80}>
                <Actions
                  equipmentComposition={item}
                  onClickEdit={() => props.handleEdit(item)}
                />
              </Table.Cell>
            </Table.Row>
          ))}

        <Table.RowLoading status={systemLoading.state.loading} colSpan={5} />
      </Table.Body>

      <Table.Mobile>
        {props.data.items.map((item) => (
          <TableMobile.Item key={item.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.tabTelemetry.equipmentVirtual.table.columns.name}
              </TableMobile.Cell>
              <TableMobile.Cell>
                {item.equipmentComposition.name}
              </TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {
                  languagePage.tabTelemetry.equipmentVirtual.table.columns
                    .operationalRule
                }
              </TableMobile.Cell>
              <TableMobile.Cell>{normalizeRule(item.rule)}</TableMobile.Cell>
            </TableMobile.Row>

            <TableMobile.Footer className="flex justify-end">
              <Actions
                equipmentComposition={item}
                onClickEdit={() => props.handleEdit(item)}
              />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={props.data.lastPage}
        currentPage={search.page!}
        handleChangePage={(page) => {
          memory.local.set({
            equipments: {
              record: { tabTelemetryEquipmentVirtual: { page } }
            }
          })
          methodTabTelemetry.equipmentVirtualfetchData()
        }}
      />
    </Table.Root>
  )
}

const Actions: FC<{
  onClickEdit: () => void
  equipmentComposition: IEquipmentVirtualPage
}> = ({ onClickEdit, ...rest }) => {
  return (
    <div className="flex items-center gap-3">
      <ModalDelete {...rest} />
      <button onClick={onClickEdit}>
        <Icon
          icon="edit"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>
    </div>
  )
}

interface ModalDeleteProps {
  equipmentComposition: IEquipmentVirtualPage
}

const ModalDelete: FC<ModalDeleteProps> = ({ equipmentComposition }) => {
  const [openDialog, setOpenDialog] = useState(false)

  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoadingStore = useSystemLoadingStore()

  const methodTabEquipmentCompositionTable = useMethodTabTelemetry()

  const handleDeleteSuccess = () => {
    setOpenDialog(false)

    setTimeout(() => {
      statePage.setTabTelemetryEquipmentVirtual({
        table: {
          ...statePage.tabTelemetryEquipmentVirtual.table,
          items: statePage.tabTelemetryEquipmentVirtual.table.items.filter(
            (e) => e.id !== equipmentComposition.id
          )
        }
      })
    }, 100)

    setTimeout(() => {
      systemLoadingStore.setLoading(false)
    }, 200)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]" asChild>
        <button type="button">
          <Icon
            icon="trash01"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>
            {languagePage.tabTelemetry.equipmentVirtual.modalDelete.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description hidden>
          {languagePage.tabTelemetry.equipmentVirtual.modalDelete.title}
        </Dialog.Description>

        <p className="mb-2">
          {languagePage.tabTelemetry.equipmentVirtual.modalDelete.textInfo}
          <span className="text-red-600">
            {equipmentComposition.equipmentComposition.name}
          </span>
        </p>

        <Dialog.Footer className="flex justify-end gap-3">
          <Dialog.Close disabled={systemLoadingStore.state.loading} asChild>
            <Button variant="secondary-gray" type="button">
              {
                languagePage.tabTelemetry.equipmentVirtual.modalDelete
                  .textCancel
              }
            </Button>
          </Dialog.Close>

          <Button
            variant="error-primary"
            type="button"
            onClick={() =>
              methodTabEquipmentCompositionTable.equipmentVirtualhandleDelete(
                equipmentComposition.id,
                handleDeleteSuccess
              )
            }
            disabled={systemLoadingStore.state.loading}
          >
            {languagePage.tabTelemetry.equipmentVirtual.modalDelete.textConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
