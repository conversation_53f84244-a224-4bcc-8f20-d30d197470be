import dayjs from 'dayjs'
import {
  createDashboardOutput,
  equipmentVirtualDataInput,
  equipmentVirtualOutput,
  filterOperationalRules,
  formDataOutput,
  removalImpactsDeleteOutput,
  removalImpactsFetchDataOutput
} from './Tab.utils'

describe('Tab.utils', () => {
  describe('formDataOutput', () => {
    it('should return EquipmentTelemetrySend with default values', () => {
      const result = formDataOutput({ equipmentId: 1 })
      expect(result).toEqual({
        id: undefined,
        equipmentId: 1,
        stMonitoring: false,
        stAutoScde: false,
        stModulation: false,
        stVirtual: false,
        stAutoForecast: false
      })
    })
    it('should use provided values', () => {
      const result = formDataOutput({
        id: 2,
        equipmentId: 3,
        stMonitoring: true,
        stAutoScde: true,
        stModulation: true,
        stVirtual: true,
        stAutoForecast: true
      })
      expect(result).toEqual({
        id: 2,
        equipmentId: 3,
        stMonitoring: true,
        stAutoScde: true,
        stModulation: true,
        stVirtual: true,
        stAutoForecast: true
      })
    })
  })

  describe('removalImpactsFetchDataOutput', () => {
    it('should format dates and map properties', () => {
      const data = {
        equipmentId: 1,
        processed: true,
        reprocess: false,
        initialDate: '2023-01-01T10:00:00Z',
        finalDate: '2023-01-02T10:00:00Z',
        properties: [
          { id: 10, name: 'A' },
          { id: 20, name: 'B' }
        ],
        pincode: '1234'
      }
      const result = removalImpactsFetchDataOutput(data)
      expect(result).toEqual({
        equipmentId: 1,
        processed: 1,
        reprocess: 0,
        initialDate: dayjs(data.initialDate).format('YYYY-MM-DD HH:mm:00'),
        finalDate: dayjs(data.finalDate).format('YYYY-MM-DD HH:mm:59'),
        properties: [10, 20]
      })
    })
  })

  describe('removalImpactsDeleteOutput', () => {
    it('should include pincode', () => {
      const data = {
        equipmentId: 1,
        processed: false,
        reprocess: true,
        initialDate: '2023-01-01T10:00:00Z',
        finalDate: '2023-01-02T10:00:00Z',
        properties: [{ id: 10, name: 'A' }],
        pincode: '9999'
      }
      const result = removalImpactsDeleteOutput(data)
      expect(result).toMatchObject({
        pincode: '9999',
        equipmentId: 1
      })
    })
  })

  describe('createDashboardOutput', () => {
    it('should map CreateDashboardData to CreateDashboardPayloadSend', () => {
      const data = {
        entities: 5,
        entitiesType: 'type',
        template: [{ id: 7, name: 'T' }],
        replace: true
      }
      expect(createDashboardOutput(data)).toEqual({
        entities: [5],
        entitiesType: 'type',
        templateId: 7,
        replace: true
      })
    })
  })

  describe('equipmentVirtualDataInput', () => {
    it('should map IEquipmentVirtual to IEquipmentVirtualPage', () => {
      const data = {
        id: 1,
        equipmentVirtual: { id: 2, name: 'EV' },
        equipmentComposition: { id: 3, name: 'EC' },
        rule: 'rule'
      }
      expect(equipmentVirtualDataInput(data)).toEqual(data)
    })
  })

  describe('equipmentVirtualOutput', () => {
    it('should map form data to send data', () => {
      const data = {
        id: 1,
        equipmentId: 2,
        equipmentComposition: [{ id: 3, name: 'EC' }],
        rule: 'subtração'
      }
      expect(equipmentVirtualOutput(data)).toEqual({
        id: 1,
        equipmentCompositionId: 3,
        equipmentId: 2,
        rule: 'subtracao'
      })
    })
  })

  describe('filterOperationalRules', () => {
    it('should filter out rule with id 3', () => {
      const rules = [
        { id: 1, title: 'A' },
        { id: 3, title: 'B' },
        { id: 2, title: 'C' }
      ]
      expect(filterOperationalRules(rules)).toEqual([
        { id: 1, title: 'A' },
        { id: 2, title: 'C' }
      ])
    })
  })
})
