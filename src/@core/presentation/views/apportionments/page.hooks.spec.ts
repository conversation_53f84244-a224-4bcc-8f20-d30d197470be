import { act, renderHook, waitFor } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  useLanguagePage,
  useMethodPage,
  useStatePageApportionments
} from './page.hooks'

jest.mock('next/router', () => ({
  ...jest.requireActual('next/router'),
  useRouter: () => jest.fn()
}))

jest.mock('@/@core/infra/api/ApportionmentsApiV3')
const spyApportionmentsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsApiV3'),
  'apportionmentsApiV3'
)

const mockApportionment = {
  id: 1,
  name: 'Energy Apportionment',
  description: 'Apportionment for energy consumption',
  company: {
    id: 101,
    name: 'Energy Corp'
  },
  apportionmentType: {
    id: 201,
    name: 'Electricity'
  },
  apportionmentMeasureUnit: {
    id: 301,
    name: 'kWh'
  },
  apportionmentTariffType: {
    id: 401,
    name: 'Standard Tariff'
  },
  costCenterIds: [501, 502, 503],
  mixedConsumption: true
}

const mockApportionment2 = {
  id: 2,
  name: 'Energy Apportionment',
  description: 'Apportionment for energy consumption',
  company: {
    id: 101,
    name: 'Energy Corp'
  },
  apportionmentType: {
    id: 201,
    name: 'Electricity'
  },
  apportionmentMeasureUnit: {
    id: 301,
    name: 'kWh'
  },
  apportionmentTariffType: {
    id: 401,
    name: 'Standard Tariff'
  },
  costCenterIds: [501, 502, 503],
  mixedConsumption: true
}

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

spyUseRouter.mockImplementation(() => ({
  push: jest.fn(),
  query: {
    id: 123
  }
}))

describe('useLanguagePage', () => {
  it('should return the correct language page', () => {
    const { result } = renderHook(() => useLanguagePage(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.page.title).toBe('Rateios')
  })
})

describe('useStatePageApportionments', () => {
  it('should initialize with the correct state', () => {
    const { result } = renderHook(() => useStatePageApportionments(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.items).toEqual([])
    expect(result.current.lastPage).toEqual(0)
    expect(result.current.total).toEqual(0)
  })

  it('should update the state correctly when calling set', () => {
    const { result } = renderHook(() => useStatePageApportionments(), {
      wrapper: AppStoreProvider
    })

    waitFor(() => result.current.reset())

    act(() => {
      result.current.set({
        items: [
          {
            id: 1,
            name: 'Energy Apportionment',
            description: 'Apportionment for energy consumption',
            company: {
              id: 101,
              name: 'Energy Corp'
            },
            apportionmentType: {
              id: 201,
              name: 'Electricity'
            },
            apportionmentMeasureUnit: {
              id: 301,
              name: 'kWh'
            },
            apportionmentTariffType: {
              id: 401,
              name: 'Standard Tariff'
            },
            costCenterIds: [501, 502, 503],
            mixedConsumption: true
          }
        ],
        lastPage: 1,
        total: 1
      })
    })

    expect(result.current.items).toEqual([
      {
        id: 1,
        name: 'Energy Apportionment',
        description: 'Apportionment for energy consumption',
        company: {
          id: 101,
          name: 'Energy Corp'
        },
        apportionmentType: {
          id: 201,
          name: 'Electricity'
        },
        apportionmentMeasureUnit: {
          id: 301,
          name: 'kWh'
        },
        apportionmentTariffType: {
          id: 401,
          name: 'Standard Tariff'
        },
        costCenterIds: [501, 502, 503],
        mixedConsumption: true
      }
    ])
    expect(result.current.lastPage).toEqual(1)
    expect(result.current.total).toEqual(1)
  })

  it('should reset the state correctly when calling reset', async () => {
    const { result } = renderHook(() => useStatePageApportionments(), {
      wrapper: AppStoreProvider
    })

    act(() => {
      result.current.set({
        items: [
          {
            id: 1,
            name: 'Energy Apportionment',
            description: 'Apportionment for energy consumption',
            company: {
              id: 101,
              name: 'Energy Corp'
            },
            apportionmentType: {
              id: 201,
              name: 'Electricity'
            },
            apportionmentMeasureUnit: {
              id: 301,
              name: 'kWh'
            },
            apportionmentTariffType: {
              id: 401,
              name: 'Standard Tariff'
            },
            costCenterIds: [501, 502, 503],
            mixedConsumption: true
          }
        ],
        lastPage: 1,
        total: 1
      })
    })
    await result.current.reset()

    expect(result.current.items).toEqual([])
    expect(result.current.lastPage).toEqual(0)
    expect(result.current.total).toEqual(0)
  })
})

describe('useMethodPage', () => {
  it('should call the necessary functions and update the state correctly when calling getData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePageApportionments(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )
    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [mockApportionment],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(1)
    expect(result.current.state.total).toBe(1)
    expect(result.current.state.lastPage).toBe(1)
  })

  it('should handle errors correctly when calling getData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePageApportionments(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)
  })

  it('should return early when response status is 204', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePageApportionments(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    await waitFor(async () => {
      await result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)
  })

  it('should handle correctly when calling handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePageApportionments(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    await waitFor(() => {
      result.current.method.handleDelete(1)
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)
  })

  it('should remove the item from state when calling handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePageApportionments(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())
    act(() => {
      result.current.state.set({
        items: [mockApportionment, mockApportionment2],
        total: 2
      })
    })

    spyApportionmentsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.state.items).toEqual([mockApportionment2])
    expect(result.current.state.total).toBe(2)
  })

  it('should handle error correctly when calling handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePageApportionments(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    spyApportionmentsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 400
      })
    }))

    await waitFor(() => {
      result.current.method.handleDelete(1)
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)
  })
})
