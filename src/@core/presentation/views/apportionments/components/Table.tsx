import { useRouter } from 'next/router'
import { FC, useRef, useState } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import {
  ApportionmentModal,
  IApportionmentModalRef
} from '@/@core/presentation/shared/ApportionmentModal/Modal'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { handleKeyEnter } from '@/@core/utils/handleInputSearch'

import {
  useLanguagePage,
  useMethodPage,
  useStatePageApportionments
} from '../page.hooks'
import { IApportionmentPage } from '../page.types'

export const ApportionmentsTable = () => {
  const statePage = useStatePageApportionments()
  const methodPage = useMethodPage()
  const languagePage = useLanguagePage()
  const systemLoading = useSystemLoadingStore()
  const searchFields = memory.local.get().apportionments.listing.search
  const permissions = useSystemStore().state.permissions

  const apportionmentRef = useRef<IApportionmentModalRef>(null)

  return (
    <>
      <Table.Root>
        <Table.Info>
          <Table.InfoTitle>{languagePage.page.title}</Table.InfoTitle>
          <Table.InfoBadge className="sm:mr-auto">
            {statePage.total}
            <span className="hidden md:inline-block md:ml-1">
              {languagePage.table.totalRegisters}
            </span>
          </Table.InfoBadge>

          <Input.Root className="ml-auto">
            <Input.Content
              slotStart={
                <Icon
                  icon="searchLg"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              placeholder={languagePage.table.search.fieldQuery}
              type="text"
              defaultValue={searchFields?.q ?? ''}
              onChange={(e) => {
                memory.local.set({
                  apportionments: {
                    listing: { search: { q: e.target.value, page: 1 } }
                  }
                })
              }}
              onKeyUp={(e) => handleKeyEnter(e.key, methodPage.getData)}
            />
          </Input.Root>
          <Table.InfoNewRegister
            onClick={() => {
              apportionmentRef.current?.handler?.({})
              apportionmentRef.current?.open?.()
            }}
            permission={permissions.apportionments?.create}
          />
        </Table.Info>

        <Table.Header>
          <Table.Row>
            <Table.Head className="cursor-pointer">
              {languagePage.table.columns.name}
            </Table.Head>
            <Table.Head className="cursor-pointer">
              {languagePage.table.columns.company}
            </Table.Head>
            <Table.Head className="cursor-pointer">
              {languagePage.table.columns.type}
            </Table.Head>
            <Table.Head className="cursor-pointer">
              {languagePage.table.columns.tariff}
            </Table.Head>
            <Table.Head className="cursor-pointer">
              {languagePage.table.columns.cdcQuantity}
            </Table.Head>
            <Table.Head></Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {statePage.items.map((apportionment) => (
            <Table.Row key={apportionment.id}>
              <Table.Cell>{apportionment.name}</Table.Cell>
              <Table.Cell>{apportionment.company.name}</Table.Cell>
              <Table.Cell>{apportionment.apportionmentType.name}</Table.Cell>
              <Table.Cell className="w-36">
                <Badge.Root variant="alert">
                  <Badge.Content className="m-auto">
                    {apportionment.apportionmentTariffType.name}
                  </Badge.Content>
                </Badge.Root>
              </Table.Cell>
              <Table.Cell>{apportionment.costCenterIds.length}</Table.Cell>

              <Table.Cell width={80}>
                <Actions apportionment={apportionment} />
              </Table.Cell>
            </Table.Row>
          ))}
          <Table.RowLoading status={systemLoading.state.loading} colSpan={6} />
        </Table.Body>

        <Table.Mobile>
          {statePage.items.map((apportionment) => (
            <TableMobile.Item key={apportionment.id}>
              <TableMobile.Head />
              <TableMobile.Row>
                <TableMobile.Cell>
                  {languagePage.table.columns.name}
                </TableMobile.Cell>
                <TableMobile.Cell>{apportionment.name}</TableMobile.Cell>
              </TableMobile.Row>
              <TableMobile.Row>
                <TableMobile.Cell>
                  {languagePage.table.columns.company}
                </TableMobile.Cell>
                <TableMobile.Cell>
                  {apportionment.company.name}
                </TableMobile.Cell>
              </TableMobile.Row>
              <TableMobile.Row>
                <TableMobile.Cell>
                  {languagePage.table.columns.type}
                </TableMobile.Cell>
                <TableMobile.Cell>
                  {apportionment.apportionmentType.name}
                </TableMobile.Cell>
              </TableMobile.Row>
              <TableMobile.Row>
                <TableMobile.Cell>
                  {languagePage.table.columns.tariff}
                </TableMobile.Cell>
                <TableMobile.Cell>
                  <Badge.Root variant="alert">
                    <Badge.Content className="m-auto">
                      {apportionment.apportionmentTariffType.name}
                    </Badge.Content>
                  </Badge.Root>
                </TableMobile.Cell>
              </TableMobile.Row>
              <TableMobile.Row>
                <TableMobile.Cell>
                  {languagePage.table.columns.cdcQuantity}
                </TableMobile.Cell>
                <TableMobile.Cell>
                  {apportionment.costCenterIds.length}
                </TableMobile.Cell>
              </TableMobile.Row>

              <TableMobile.Footer>
                <Actions apportionment={apportionment} />
              </TableMobile.Footer>
            </TableMobile.Item>
          ))}
        </Table.Mobile>

        <Table.Paginate
          status={systemLoading.state.loading}
          currentPage={searchFields.page}
          lastPage={statePage.lastPage}
          handleChangePage={(page) => {
            memory.local.set({
              apportionments: {
                listing: { search: { page } }
              }
            })
            methodPage.getData()
          }}
        />
      </Table.Root>

      <ApportionmentModal ref={apportionmentRef} />
    </>
  )
}

const Actions: FC<{ apportionment: IApportionmentPage }> = ({
  apportionment
}) => {
  const router = useRouter()

  const permissions = useSystemStore().state.permissions

  return (
    <div className="table-td-actions">
      {permissions?.apportionments?.delete && (
        <ModalDelete
          apportionmentId={apportionment.id}
          apportionmentName={apportionment.name}
        />
      )}
      {permissions.apportionments?.create && (
        <button
          className="table-td-action hover:cursor-pointer"
          onClick={() => router.push(`/apportionments/${apportionment.id}`)}
        >
          <Icon
            icon="edit"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      )}
    </div>
  )
}

interface ModalDeleteProps {
  apportionmentId: number
  apportionmentName: string
}

const ModalDelete: FC<ModalDeleteProps> = ({
  apportionmentId,
  apportionmentName
}) => {
  const [openDialog, setOpenDialog] = useState(false)
  const languagePage = useLanguagePage()
  const methodPage = useMethodPage()

  const handleClick = async () => {
    const result = await methodPage.handleDelete(apportionmentId)
    if (result) setOpenDialog(false)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]">
        <Button.Icon>
          <Icon
            icon="trash01"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </Button.Icon>
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>{languagePage.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>

        <Dialog.Description>
          {languagePage.table.modalDelete.textInfo}{' '}
          <span className="text-red-600">{apportionmentName}</span>
        </Dialog.Description>

        <Dialog.Footer>
          <Button
            variant="secondary-gray"
            type="button"
            onClick={() => setOpenDialog(false)}
          >
            {languagePage.table.modalDelete.cancel}
          </Button>

          <Button type="button" variant="error-primary" onClick={handleClick}>
            {languagePage.table.modalDelete.confirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
