import { useCallback } from 'react'

import { AxiosError } from 'axios'
import { create } from 'zustand'

import { usePageDependencies } from '@/@core/framework/hooks/usePageDependencies/hook'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { apportionmentsApiV3 } from '@/@core/infra/api/ApportionmentsApiV3'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import loggerRequest from '@/@core/logging/loggerRequest'

import { stateData } from './page.content'
import { IState } from './page.types'
import { parseApportionmentData } from './page.utils'

const statePageApportionments = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => set((state) => ({ ...state, ...initialData })),
  reset: () => set((state) => ({ ...state, ...stateData }))
}))

export const useStatePageApportionments = () => {
  const state = statePageApportionments()
  return { ...state }
}

export const useMethodPage = () => {
  const { systemLoading, systemToast, log } = usePageDependencies()
  const statePage = useStatePageApportionments()
  const languagePage = useLanguagePage()

  const getData = useCallback(async () => {
    try {
      systemLoading.setData({ loading: true })

      statePage.reset()

      const { search } = memory.local.get().apportionments.listing

      const result = await apportionmentsApiV3(http).get({
        ...search
      })

      if (result.status === 204) {
        systemLoading.setData({ loading: false })
        return
      }

      statePage.set({
        items: result.data.items.map(parseApportionmentData),
        lastPage: result.data.lastPage,
        total: result.data.total
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: `Erro no getData rateio - @core/presentation/views/apportionments/page.hooks.ts`
      })
    } finally {
      systemLoading.setData({ loading: false })
    }
  }, [])

  const handleDelete = async (apportionmentId: number): Promise<boolean> => {
    try {
      systemLoading.setData({ loading: true })

      await apportionmentsApiV3(http).delete(apportionmentId)

      systemToast.addToast({
        message: languagePage.table.modalDelete.messages.successMessage
      })
      statePage.set({
        items: statePage.items.filter((item) => item.id !== apportionmentId)
      })
      return true
    } catch (error) {
      const { response } = error as AxiosError

      const responseMessage = (response?.data as string[])?.[0]
      systemToast.addToast({
        message:
          responseMessage ??
          languagePage.table.modalDelete.messages.errorMessage,
        type: 'error'
      })

      log.send(loggerRequest, {
        error,
        title: `Erro no handleDelete rateio - @core/presentation/views/apportionments/page.hooks.ts`
      })

      return false
    } finally {
      systemLoading.setData({ loading: false })
    }
  }
  return { getData, handleDelete }
}

export const useLanguagePage = () => {
  const { lang } = useSystemLanguageStore().state

  const { pages, table, btn, validationFields } = languageByMode(lang)

  return {
    page: {
      ...pages.apportionments
    },
    table: {
      ...pages.apportionments.table,
      ...table,
      modalDelete: {
        ...pages.apportionments.table.modalDelete,
        cancel: btn.cancel,
        confirm: btn.confirm,
        continue: btn.continue,
        requiredField: validationFields.requiredField
      }
    }
  }
}
