import { IReportGenerator } from '@/@core/domain/ReportGenerator'
import { DeepPartial } from '@/types/system'

export interface ITabReportGenerate {
  report: {
    id: number | null
    name: string
    updatedAt: string
    filter: object
  }
  reportGenereted: IReportGenerator | null
}

export type IFormValues = {
  entity_fields?: {
    entity?: string
  }
  entity_type_fields?: {
    type_measurement?: string
    account_type?: string
  }
  entity_data_fields?: {
    entity_data?: { id: number; name: string }[]
    entity_data_group?: string
  }
  period_fields?: {
    initial_date?: string
    final_date?: string
    syntax_date?: string
  }
  aggregate_fields?: {
    date_interval?: string
    date_interval_number?: string
    type?: string
  }
  type_data?: {
    consumption?: {
      value?: boolean
      fields?: {
        tariff_post?: string
        capacity?: string
        greatness?: string
        consumption?: string[]
      }
    }
    demand?: {
      value?: boolean
      fields?: {
        tariff_post?: string
        capacity?: string
        consumption?: string[]
      }
    }
    power_factor?: {
      value?: boolean
      fields?: {
        tariff_post?: string
        capacity?: string
      }
    }
    access_log?: {
      value?: boolean
      fields?: {}
    }
  }
}

export type IFormErrors = DeepPartial<{
  entity_fields?: {
    entity?: string
  }
  entity_type_fields?: {
    type_measurement?: string
    account_type?: string
  }
  entity_data_fields?: {
    entity_data?: string
    entity_data_group?: string
  }
  period_fields?: {
    initial_date?: string
    final_date?: string
    syntax_date?: string
  }
  aggregate_fields?: {
    date_interval?: string
    date_interval_number?: string
    type?: string
  }
  type_data?: {
    value?: string
    consumption?: {
      fields?: {
        tariff_post?: string
        capacity?: string
        greatness?: string
        consumption?: string
      }
    }
    demand?: {
      fields?: {
        tariff_post?: string
        capacity?: string
        consumption?: string
      }
    }
    power_factor?: {
      fields?: {
        tariff_post?: string
        capacity?: string
      }
    }
    access_log?: {
      fields?: {}
    }
  }
}>
