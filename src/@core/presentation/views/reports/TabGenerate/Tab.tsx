import { useRef } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { IModalRootRef } from '@/@core/presentation/shared/Modal'
import { Button } from '@/@core/presentation/shared/ui/button'

import { useStatePage } from '../page.hooks'
import FormReport from './_components/Form'
import SaveModal from './_components/SaveModal'
import TableReport from './_components/Table'
import { useLanguageTabGenerate } from './Tab.hooks'

export const TabGenerate = () => {
  const modalRef = useRef<IModalRootRef>(null)

  const statePage = useStatePage()

  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  return (
    <>
      <div className="lg:flex items-stretch gap-4">
        <div data-atribute="reportForm" className="flex-1">
          <FormReport />
        </div>
        <div
          data-atribute="reportTable"
          className="lg:flex-[1.75] pr-1 overflow-y-auto"
        >
          <TableReport />

          {!!statePage.tabGenerate.reportGenereted?.table.header.length && (
            <div className="flex justify-end p-2 duration-100">
              <Button
                onClick={() => modalRef.current?.open()}
                disabled={systemLoading.state.loading}
              >
                {!!statePage.tabGenerate.report.id
                  ? languageTab.form.btn.update
                  : languageTab.form.btn.save}
              </Button>
            </div>
          )}
        </div>
      </div>

      <SaveModal ref={modalRef} />
    </>
  )
}
