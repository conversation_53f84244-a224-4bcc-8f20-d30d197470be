import { getTypeError } from './page.utils'

describe('getTypeError', () => {
  it('returns userNotPartOfPlatform true when the error is "userNotPartOfPlatform"', () => {
    expect(getTypeError('userNotPartOfPlatform')).toEqual({
      userNotPartOfPlatform: true,
      isAuthError: false
    })
  })

  it('returns isAuthError true when error is "isAuthError"', () => {
    expect(getTypeError('isAuthError')).toEqual({
      userNotPartOfPlatform: false,
      isAuthError: true
    })
  })

  it('returns both false for other values', () => {
    expect(getTypeError('anything')).toEqual({
      userNotPartOfPlatform: false,
      isAuthError: false
    })
    expect(getTypeError(undefined)).toEqual({
      userNotPartOfPlatform: false,
      isAuthError: false
    })
    expect(getTypeError(['userNotPartOfPlatform'])).toEqual({
      userNotPartOfPlatform: false,
      isAuthError: false
    })
  })
})
