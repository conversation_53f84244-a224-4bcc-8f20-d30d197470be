import { cleanup, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { useLanguagePage } from './page.hooks'

cleanup()

describe('src/@core/presentation/views/access-denied/page | useLanguagePage', () => {
  it('check de page title isAuthError', () => {
    const { result } = renderHook(() => useLanguagePage(), {
      wrapper: AppStoreProvider
    })
    expect(result.current.page.isAuthError.title).toBe('Falha na Autenticação')
  })

  it('check de page title userNotPartOfPlatform', () => {
    const { result } = renderHook(() => useLanguagePage(), {
      wrapper: AppStoreProvider
    })
    expect(result.current.page.userNotPartOfPlatform.title).toBe(
      'Desculpe, acesso negado!'
    )
  })
})
