import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'

import { useLanguagePage } from './page.hooks'

import { Button } from '../../shared/ui/button'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { getTypeError } from './page.utils'

const PRODUCT_PAGE_URL =
  'https://www.comerc.com.br/eficiencia-energetica#telemetria'
const SUPPORT_EMAIL = 'mailto:<EMAIL>'

const AccessDenied: React.FC & { layout?: string } = () => {
  const router = useRouter()

  const languagePage = useLanguagePage()

  const { error } = router.query
  const typeError = getTypeError(error)

  const isUserNotPart = typeError.userNotPartOfPlatform
  const message = isUserNotPart
    ? languagePage.page.userNotPartOfPlatform
    : languagePage.page.isAuthError

  const retryLogin = () => {
    localStorage.clear()
    router.push('/')
  }

  return (
    <div className="relative overflow-hidden text-center px-8 py-12 min-h-screen flex items-start sm:items-center justify-center">
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none flex items-center justify-center z-[1]">
        <div className="access-denied-grid" />
      </div>

      <div className="relative z-10 max-w-3xl mx-auto">
        <div className="mb-4">
          <Image
            className="w-[112px] h-[112px] sm:w-48 sm:h-48 mx-auto"
            alt="Lock closed, access denied"
            width={100}
            height={100}
            src="/images/icons/icon_lock.svg"
          />
        </div>

        <div className="mb-6 sm:mb-24">
          <h1 className="text-comerc-grayLight-900 dark:text-comerc-grayLight-50 font-acuminPro-Semibold text-[36px] sm:text-[60px]">
            {message.title}
          </h1>
          <p className="font-acuminPro-Regular text-comerc-grayLight-600 dark:text-comerc-grayLight-400 text-[18px] sm:text-[20px]">
            {message.description}
          </p>
        </div>

        <div
          className={cn(
            'flex flex-col sm:flex-row justify-center items-center',
            'gap-[12px] sm:gap-2'
          )}
        >
          <Link
            href={PRODUCT_PAGE_URL}
            target="_blank"
            className={cn(
              'flex h-[48px] sm:h-full w-full sm:w-[300px] px-6 py-4 justify-center items-center rounded-lg',
              'font-acuminPro-Semibold text-[18px]',
              'text-gray-600 border border-gray-300 bg-white shadow-sm hover:bg-gray-200',
              'cursor-pointer',
              isUserNotPart ? 'flex' : 'hidden'
            )}
          >
            {languagePage.page.userNotPartOfPlatform.button}
          </Link>

          <Button
            className={cn(
              'flex h-[48px] sm:h-full w-full sm:w-[300px] px-6 py-4 justify-center items-center rounded-lg',
              'font-acuminPro-Semibold text-[18px] text-gray-600',
              'border border-gray-300 bg-white shadow-sm hover:bg-gray-200',
              !isUserNotPart ? 'flex' : 'hidden'
            )}
            onClick={retryLogin}
          >
            {languagePage.page.isAuthError.button}
          </Button>

          <Link
            href={SUPPORT_EMAIL}
            className={cn(
              'flex h-[48px] sm:h-full w-full sm:w-[300px] px-6 py-4 justify-center items-center rounded-lg',
              'font-acuminPro-Semibold text-[18px]',
              'bg-comerc-vibra-brandComerc-700 border-comerc-vibra-brandComerc-700 hover:bg-green-600 text-white'
            )}
          >
            {languagePage.page.linkSupportEmail}
          </Link>
        </div>
      </div>
    </div>
  )
}

AccessDenied.layout = 'accessDenied'
export default AccessDenied
