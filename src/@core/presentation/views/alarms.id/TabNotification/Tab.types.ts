export type INotification = {
  _id: number
  id: number | null
  channel: { id: number; name: string } | null
  frequency: { id: number; name: string } | null
  user: { id: number; name: string }
  configs: string[]
}
export type INotificationCreate = {
  id: number | null
  channel: { id: number; name: string } | null
  frequency: { id: number; name: string } | null
  user: { id: number; name: string }
  configs: string[]
}

export interface IFormDataValues {
  _id: number | null
  id: number | null
  channel: { id: number; name: string } | null
  frequency: { id: number; name: string } | null
  user: { id: number; name: string }[]
  configs: string[]
}
export interface IFormDataSend {
  alarmId: number
  id: number | null
  alarmNotificationChannelId: number
  userId: number
  frequencyId: number
  configs: string[]
}
