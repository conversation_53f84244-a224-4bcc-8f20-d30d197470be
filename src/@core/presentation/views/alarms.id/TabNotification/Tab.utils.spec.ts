import { alarmsNotificationsMock1 } from '@/__mock__/content/api-alarms-notifications.content'
import { IFormDataValues } from './Tab.types'
import {
  formDataOutput,
  formNotificationDataInput,
  formNotificationDataOutput,
  parseNotificationData
} from './Tab.utils'

describe('@core/presentation/views/alarms.id/TabNotification/Tab.utils', () => {
  beforeEach(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date('2024-05-01T10:00:00Z'))
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  it('parseNotificationData', () => {
    const inputData = { ...alarmsNotificationsMock1 }
    const outputData = {
      _id: 1714557600000,
      id: 1,
      channel: { id: 1, name: 'E-mail' },
      frequency: { id: 1, name: 'Tempo Real' },
      user: { id: 1, name: 'Name' },
      configs: ['<EMAIL>', '<EMAIL>']
    }
    const result = parseNotificationData(inputData)

    expect(result).toEqual(outputData)
  })

  it('formNotificationDataInput', () => {
    const inputData = parseNotificationData({ ...alarmsNotificationsMock1 })
    const outputData = {
      _id: 1714557600000,
      id: 1,
      channel: { id: 1, name: 'E-mail' },
      frequency: { id: 1, name: 'Tempo Real' },
      user: [{ id: 1, name: 'Name' }],
      configs: ['<EMAIL>', '<EMAIL>']
    }
    expect(formNotificationDataInput(inputData)).toEqual(outputData)
  })

  it('formNotificationDataOutput', () => {
    const inputData: IFormDataValues = {
      _id: 1714557600000,
      id: alarmsNotificationsMock1.id,
      channel: alarmsNotificationsMock1.channel,
      frequency: alarmsNotificationsMock1.frequency,
      user: [alarmsNotificationsMock1.user],
      configs: alarmsNotificationsMock1.configs
    }
    const outputData = {
      id: 1,
      channel: { id: 1, name: 'E-mail' },
      frequency: { id: 1, name: 'realtime' },
      user: { id: 1, name: 'Name' },
      configs: ['<EMAIL>', '<EMAIL>']
    }
    expect(formNotificationDataOutput(inputData)).toEqual(outputData)
  })

  it('formDataOutput', () => {
    const inputData: IFormDataValues & { alarmId: number } = {
      alarmId: 145,
      _id: 1714557600000,
      id: alarmsNotificationsMock1.id,
      channel: alarmsNotificationsMock1.channel,
      frequency: alarmsNotificationsMock1.frequency,
      user: [alarmsNotificationsMock1.user],
      configs: alarmsNotificationsMock1.configs
    }

    const outputData = {
      alarmId: 145,
      id: 1,
      alarmNotificationChannelId: 1,
      frequencyId: 1,
      userId: 1,
      configs: ['<EMAIL>', '<EMAIL>']
    }
    expect(formDataOutput(inputData)).toEqual(outputData)
  })
})
