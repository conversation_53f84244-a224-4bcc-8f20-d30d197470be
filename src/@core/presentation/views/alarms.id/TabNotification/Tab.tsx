import { useEffect, useRef } from 'react'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { PageSection } from '@/@core/presentation/shared/pages'
import { Button } from '@/@core/presentation/shared/ui/button'
import { ListAlarmsNotificationsChannelsService } from '@/@core/services/listAlarmsNotificationsChannelService'

import { useMethodsAlarmesIdPage, useStateAlarmesIdPage } from '../page.hooks'
import {
  useLanguageTabNotification,
  useMethodTabNotification
} from './Tab.hooks'
import SectionForm, { ISectionFormRef } from './_components/SectionForm'
import SectionTable from './_components/SectionTable'

export const TabNotification = () => {
  const isMounted = useRef<boolean>(false)
  const formRef = useRef<ISectionFormRef>(null)

  const listAlarmsNotificationsChannelsService =
    ListAlarmsNotificationsChannelsService()

  const system = useSystemStore()

  const statePage = useStateAlarmesIdPage()
  const methodPage = useMethodsAlarmesIdPage()

  const languageTab = useLanguageTabNotification()
  const methodTab = useMethodTabNotification()

  useEffect(() => {
    if (
      !isMounted.current &&
      !system.state.mountComponent?.['alarms.id-TabNotification']
    ) {
      const handler = () => {
        listAlarmsNotificationsChannelsService.handler()

        if (!statePage.isEdit) return

        methodTab.fetchData()
      }
      handler()

      system.setMountComponent('alarms.id-TabNotification')
    }

    return () => {
      isMounted.current = true
    }
  }, [])

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2 space-y-[8px]">
            <PageSection.Content title={languageTab.title} />
          </div>
        </div>
      </PageSection.Root>

      <SectionForm ref={formRef} />

      <SectionTable
        handleEdit={(notification) =>
          formRef.current?.setValues?.(notification)
        }
      />

      {!statePage.tabData.alarm?.id && (
        <Button
          onClick={() => methodPage.createNewAlarm()}
          type="button"
          variant="primary"
          className="ml-auto my-4"
        >
          {languageTab.btnCreateNewAlarm}
        </Button>
      )}
    </>
  )
}
