import IAlarmsNotifications from '@/@core/domain/AlarmsNotifications'
import { mapAlarmFrequecy } from '@/content/mapAlarmFrequecy.content'
import {
  IFormDataSend,
  IFormDataValues,
  INotification,
  INotificationCreate
} from './Tab.types'

export const parseNotificationData = (
  data: IAlarmsNotifications
): INotification => {
  const frequencyName = String(
    mapAlarmFrequecy.find((e) => e.id === data.frequency.id)?.name
  )

  return {
    _id: new Date().getTime(),
    id: data.id,
    channel: data.channel,
    frequency: {
      id: data.frequency.id,
      name: frequencyName
    },
    user: data.user,
    configs: data.configs
  }
}

export const formNotificationDataInput = (
  data: INotification
): IFormDataValues => {
  return {
    _id: data._id,
    id: data.id,
    channel: data.channel,
    frequency: data.frequency,
    user: [data.user],
    configs: data.configs
  }
}

export const formNotificationDataOutput = (
  data: IFormDataValues
): INotificationCreate => {
  return {
    id: data.id,
    channel: data.channel,
    frequency: data.frequency,
    user: data.user?.[0],
    configs: data.configs
  }
}

export const formDataOutput = (
  data: IFormDataValues & { alarmId: number }
): IFormDataSend => ({
  alarmId: data.alarmId,
  id: data.id,
  alarmNotificationChannelId: Number(data.channel?.id),
  frequencyId: Number(data.frequency?.id),
  userId: Number(data.user.map(({ id }) => id)),
  configs: data.configs
})
