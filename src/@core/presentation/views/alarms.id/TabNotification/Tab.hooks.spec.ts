import { act, renderHook } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsNotificationsMock1 } from '@/__mock__/content/api-alarms-notifications.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStateAlarmesIdPage } from '../page.hooks'
import {
  useLanguageTabNotification,
  useMethodTabNotification
} from './Tab.hooks'
import { IFormDataSend, INotificationCreate } from './Tab.types'
import { parseNotificationData } from './Tab.utils'

jest.mock('next/router', () => ({
  ...jest.requireActual('next/router'),
  useRouter: () => jest.fn()
}))

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsNotificationsApiV4')
const spyAlarmsNotificationsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsNotificationsApiV4'),
  'alarmsNotificationsApiV4'
)

describe('@core/presentation/views/alarms.id/TabNotification/Tab.hooks | useLanguageTabNotification', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabNotification()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.title).toEqual('Notificação')
  })
})

describe('@core/presentation/views/alarms.id/TabNotification/Tab.hooks | useMethodTabNotification', () => {
  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))

    jest.useFakeTimers()
    jest.setSystemTime(new Date('2024-05-01T10:00:00Z'))
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  it('should check method fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabNotification(),
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await act(async () => result.current.state.reset())

    /* request with error **/
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.tabNotification.table.items).toHaveLength(0)

    /* request successful without data **/
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: {
          items: [],
          total: 0,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.tabNotification.table.items).toHaveLength(0)

    /* request successful with data **/
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsNotificationsMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await act(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.tabNotification.table.items).toHaveLength(1)
  })

  it('should check method handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabNotification(),
        state: useStateAlarmesIdPage(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    const formData: IFormDataSend = {
      id: null,
      alarmNotificationChannelId: alarmsNotificationsMock1.id,
      alarmId: alarmsNotificationsMock1.alarmId,
      userId: alarmsNotificationsMock1.userId,
      frequencyId: alarmsNotificationsMock1.frequencyId,
      configs: alarmsNotificationsMock1.configs
    }

    /* set initial state **/
    await act(async () => {
      result.current.state.set({
        tabNotification: {
          table: {
            items: [
              parseNotificationData({ ...alarmsNotificationsMock1, id: 11 }),
              parseNotificationData({ ...alarmsNotificationsMock1, id: 22 })
            ],
            lastPage: 1
          }
        }
      })
    })

    /* request CREATE error **/
    await act(async () => {
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        response: { data: { userId: ['campo obrigatório'] } }
      })
    }))
    await act(async () => {
      result.current.method.handleSubmit(formData)
    })
    expect(result.current.state.tabNotification.table.items).toHaveLength(2)
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao criar notificação'
    )

    /* request CREATE with error payload **/
    await act(async () => {
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 400,
        data: {}
      })
    }))
    await act(async () => {
      result.current.method.handleSubmit(formData)
    })

    /* request CREATE success **/
    await act(async () => {
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: { ...alarmsNotificationsMock1, id: 33 }
      })
    }))
    await act(async () => {
      result.current.method.handleSubmit(formData)
    })
    expect(result.current.state.tabNotification.table.items).toHaveLength(3)
    expect(result.current.toast.state.toasts[0].type).toBe('success')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Notificação criado com sucesso'
    )

    /* request UPDATE error **/
    await act(async () => {
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await act(async () => {
      await result.current.method.handleSubmit({ ...formData, id: 33 })
    })
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar notificação'
    )

    /* request UPDATE success **/
    await act(async () => {
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: { ...alarmsNotificationsMock1, id: 33 }
      })
    }))
    await act(async () => {
      await result.current.method.handleSubmit({ ...formData, id: 33 })
    })
    expect(result.current.toast.state.toasts[0].type).toBe('success')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Notificação atualizado com sucesso'
    )
  })

  it('should check method handleDelete', async () => {
    const callback = jest.fn()

    const { result } = renderHook(
      () => ({
        method: useMethodTabNotification(),
        state: useStateAlarmesIdPage(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    await act(async () => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await act(async () => {
      await result.current.method.handleDelete(123, callback)
    })
    expect(callback).toHaveBeenCalledTimes(0)
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover a notificação'
    )

    /* request not authenticated **/
    await act(async () => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 403
      })
    }))
    await act(async () => {
      await result.current.method.handleDelete(123, callback)
    })
    expect(callback).toHaveBeenCalledTimes(0)
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover a notificação'
    )

    /* request seccess **/
    await act(async () => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))
    await act(async () => {
      await result.current.method.handleDelete(123, callback)
    })
    expect(callback).toHaveBeenCalledTimes(1)
    expect(result.current.toast.state.toasts[0].type).toBe('success')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Notificação removida com sucesso'
    )
  })

  it('should register and manage notification', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabNotification(),
        state: useStateAlarmesIdPage(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    let _id: number

    const payload: INotificationCreate = {
      id: null,
      channel: { id: 1, name: 'name channel' },
      frequency: { id: 2, name: 'name frequency' },
      user: { id: 3, name: 'name user' },
      configs: []
    }

    await act(async () => {
      jest.setSystemTime(new Date('2024-05-01T10:00:00Z'))
      _id = new Date().getTime()
      result.current.method.addNotification(payload)
    })

    await act(async () => {
      jest.setSystemTime(new Date('2024-05-01T12:00:00Z'))
      result.current.method.addNotification(payload)
    })

    expect(result.current.state.tabNotification.table.items).toHaveLength(2)
    expect(
      result.current.state.tabNotification.table.items[0].channel?.name
    ).toBe('name channel')

    payload.channel!.name = 'name channel2'

    await act(async () => {
      result.current.method.updateNotification(Number(_id), payload)
    })
    expect(
      result.current.state.tabNotification.table.items[0].channel?.name
    ).toBe('name channel2')

    await act(async () => {
      result.current.method.deleteNotification(Number(_id))
    })
    expect(result.current.state.tabNotification.table.items).toHaveLength(1)
  })
})
