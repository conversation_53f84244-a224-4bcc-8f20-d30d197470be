import { useRef } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { memory } from '@/@core/infra/memory'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { But<PERSON> } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'

import { useStateAlarmesIdPage } from '../../page.hooks'
import {
  useLanguageTabNotification,
  useMethodTabNotification
} from '../Tab.hooks'
import { INotification } from '../Tab.types'

interface SectionTableProps {
  handleEdit: (p: INotification) => void
}
const SectionTable = (props: SectionTableProps) => {
  const systemLoading = useSystemLoadingStore()

  const statePage = useStateAlarmesIdPage()

  const languageTab = useLanguageTabNotification()
  const methodTab = useMethodTabNotification()

  const search = memory.local.get().alarms.record.tabNotifications

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languageTab.title}</Table.InfoTitle>
      </Table.Info>
      <Table.Header>
        <Table.Row>
          <Table.Head>{languageTab.table.columns.channel}</Table.Head>
          <Table.Head>{languageTab.table.columns.frequency}</Table.Head>
          <Table.Head>{languageTab.table.columns.user}</Table.Head>
          <Table.Head>{languageTab.table.columns.actions}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {!systemLoading.state.loading &&
          statePage.tabNotification.table.items.map((notification) => (
            <Table.Row key={notification.id}>
              <Table.Cell>{notification.channel?.name}</Table.Cell>
              <Table.Cell>{notification.frequency?.name}</Table.Cell>
              <Table.Cell>{notification.user.name}</Table.Cell>
              <Table.Cell width={80} role="td-actions">
                <Actions
                  notification={notification}
                  onClickEdit={() => {
                    props.handleEdit(notification)
                  }}
                />
              </Table.Cell>
            </Table.Row>
          ))}
        <Table.RowLoading status={systemLoading.state.loading} colSpan={4} />
      </Table.Body>

      <Table.Mobile>
        {statePage.tabNotification.table.items.map((notification) => (
          <TableMobile.Item key={notification.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languageTab.table.columns.channel}
              </TableMobile.Cell>
              <TableMobile.Cell>{notification.channel?.name}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languageTab.table.columns.frequency}
              </TableMobile.Cell>
              <TableMobile.Cell>
                {notification.frequency?.name}
              </TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>
                {languageTab.table.columns.user}
              </TableMobile.Cell>
              <TableMobile.Cell>{notification.user.name}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Footer className="flex justify-end">
              <Actions
                notification={notification}
                onClickEdit={() => props.handleEdit(notification)}
              />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={statePage.tabNotification.table.lastPage}
        currentPage={search.page}
        handleChangePage={(page) => {
          memory.local.set({
            alarms: {
              record: { tabNotifications: { page } }
            }
          })
          methodTab.fetchData()
        }}
      />
    </Table.Root>
  )
}

const Actions = ({
  onClickEdit,
  ...rest
}: {
  onClickEdit: () => void
  notification: INotification
}) => {
  return (
    <div className="flex items-center gap-3">
      <ModalDelete {...rest} />
      <button onClick={onClickEdit}>
        <Icon
          icon="edit"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>
    </div>
  )
}

const ModalDelete = ({ notification }: { notification: INotification }) => {
  const modalRef = useRef<IModalRootRef>(null)

  const systemLoadingStore = useSystemLoadingStore()

  const statePage = useStateAlarmesIdPage()

  const methodTab = useMethodTabNotification()
  const languageTab = useLanguageTabNotification()

  const handleDeleteSuccess = () => {
    modalRef.current?.close()

    setTimeout(() => {
      const items = [...statePage.tabNotification.table.items].filter(
        (e) => e.id !== notification.id
      )

      statePage.set({
        tabNotification: {
          table: {
            ...statePage.tabNotification.table,
            items
          }
        }
      })
    }, 100)

    setTimeout(() => {
      systemLoadingStore.setLoading(false)
    }, 200)
  }

  return (
    <>
      <button
        className="flex *:m-auto size-7"
        onClick={() =>
          statePage.isEdit
            ? modalRef.current?.open()
            : methodTab.deleteNotification(notification._id)
        }
      >
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>

      <Modal.Root ref={modalRef}>
        <Modal.Title>{languageTab.table.modalDelete.title}</Modal.Title>

        <Modal.Content>
          <p className="mb-2">
            {languageTab.table.modalDelete.textInfo}
            <span className="text-red-600"> {notification.user.name}</span>
          </p>
        </Modal.Content>

        <Modal.Footer>
          <Button
            type="button"
            variant="secondary-gray"
            onClick={() => modalRef.current?.close()}
            disabled={systemLoadingStore.state.loading}
          >
            {languageTab.table.modalDelete.textCancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={() =>
              methodTab.handleDelete(
                Number(notification.id),
                handleDeleteSuccess
              )
            }
            disabled={systemLoadingStore.state.loading}
          >
            {languageTab.table.modalDelete.textConfirm}
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}

export default SectionTable
