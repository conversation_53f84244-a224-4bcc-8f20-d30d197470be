import { zodResolver } from '@hookform/resolvers/zod'
import { forwardRef, useImperativeHandle } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useListAlarmsNotificationsChannelsStore from '@/@core/framework/store/hook/useListAlarmsNotificationsChannel'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { usersApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { Button } from '@/@core/presentation/shared/ui/button'
import {
  formatInputValue,
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { featchDataItemUser } from '@/@core/utils/featchDataItem'
import { checkValueEmail } from '@/@core/utils/regex'
import { mapAlarmFrequecy } from '@/content/mapAlarmFrequecy.content'

import { useStateAlarmesIdPage } from '../../page.hooks'
import {
  useLanguageTabNotification,
  useMethodTabNotification
} from '../Tab.hooks'
import { IFormDataValues, INotification } from '../Tab.types'
import {
  formDataOutput,
  formNotificationDataInput,
  formNotificationDataOutput
} from '../Tab.utils'

export interface ISectionFormRef {
  setValues?: (data: INotification) => void
}
interface ISectionFormProps { }

const SectionForm = forwardRef<ISectionFormRef, ISectionFormProps>(
  (props, ref) => {
    const listAlarmsNotificationsChannelsStore =
      useListAlarmsNotificationsChannelsStore()

    const systemLoading = useSystemLoadingStore()

    const statePage = useStateAlarmesIdPage()

    const methodTab = useMethodTabNotification()
    const languageTab = useLanguageTabNotification()

    const formFields = useFormFields()

    const onSubmit = async () => {
      const notification = formNotificationDataOutput(formFields.values)

      if (!statePage.isEdit) {
        formFields.values._id
          ? methodTab.updateNotification(formFields.values._id, notification)
          : methodTab.addNotification(notification)

        formFields.reset()
        return
      }

      const { status } = await methodTab.handleSubmit(
        formDataOutput({
          ...formFields.values,
          alarmId: Number(statePage.tabData.alarm.id)
        })
      )

      status && formFields.reset()
    }

    useImperativeHandle(ref, () => ({
      setValues: (value) => {
        formFields.setValues(formNotificationDataInput(value))
      }
    }))

    return (
      <form
        className="form-container mb-4"
        onSubmit={formFields.handleSubmit(onSubmit)}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <TagInput.Root>
            <TagInput.Label htmlFor="channel">
              {languageTab.form.input.channel}
            </TagInput.Label>
            <TagInput.Content
              key={formFields.values.channel?.id}
              name="channel"
              value={String(formFields.values.channel?.id ?? '')}
              onChange={(values) => {
                const [item] = formatOutputValues(values)
                formFields.setValue('channel', item)
              }}
              options={listAlarmsNotificationsChannelsStore.state.list.map(
                formatInputValue
              )}
              disabled={systemLoading.state.loading}
              helperText={formFields.errors.channel?.message}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="frequency">
              {languageTab.form.input.frequency}
            </TagInput.Label>
            <TagInput.Content
              key={formFields.values.frequency?.id}
              name="frequency"
              value={String(formFields.values.frequency?.id ?? '')}
              onChange={(values) => {
                const [item] = formatOutputValues(values)
                formFields.setValue('frequency', item)
              }}
              options={mapAlarmFrequecy.map(formatInputValue)}
              disabled={systemLoading.state.loading}
              helperText={formFields.errors.frequency?.message}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="user">
              {languageTab.form.input.user}
            </TagInput.Label>
            <TagInput.ContentApi
              key={formFields.values.user.length}
              name="user"
              value={formatInputValues(formFields.values.user)}
              onChange={(values) => {
                console.log(values)
                const items = formatOutputValues(values)
                formFields.setValue('user', items)
              }}
              featchData={usersApiV3(http).get}
              featchDataItem={featchDataItemUser}
              helperText={formFields.errors.user?.message}
              disabled={systemLoading.state.loading}
            />
          </TagInput.Root>
        </div>

        <div className="my-4">
          <TagInput.Root>
            <TagInput.Label htmlFor="configs">
              {languageTab.form.input.email}
            </TagInput.Label>
            <TagInput.ContentCreatable
              name="configs"
              value={formFields.values.configs}
              onChange={(values) => formFields.setValue('configs', values)}
              placeholder="Digite e pressione Enter para adicionar"
              helperText={formFields.errors.configs?.message}
              disabled={systemLoading.state.loading}
              isMulti={true}
              onInputFilter={checkValueEmail}
            />
          </TagInput.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            onClick={() => formFields.reset()}
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languageTab.form.btn.cancel
              : languageTab.form.btn.clean}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {formFields.values.id
              ? languageTab.form.btn.save
              : languageTab.form.btn.add}
          </Button>
        </div>
      </form>
    )
  }
)

const useFormFields = () => {
  const {
    form: { requiredField }
  } = useLanguageTabNotification()

  const statePage = useStateAlarmesIdPage()

  const formSchema = z
    .object({
      _id: z.number().nullable(),
      id: z.number().nullable(),
      channel: z
        .object({
          id: z.number(),
          name: z.string()
        })
        .nullable(),
      frequency: z
        .object({
          id: z.number(),
          name: z.string()
        })
        .nullable(),
      user: z
        .array(
          z.object({
            id: z.number(),
            name: z.string()
          })
        )
        .min(1, { message: requiredField }),
      configs: z.array(z.string()).min(1, { message: requiredField })
    })
    .superRefine((data, ctx) => {
      if (!data.channel || data.channel.name.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['channel'],
          message: requiredField
        })
      }
      if (!data.frequency || data.frequency.name.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['frequency'],
          message: requiredField
        })
      }
      if (!data.user || !Array.isArray(data.user) || data.user.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['user'],
          message: requiredField
        })
      }
      const currentUserId = data.user?.[0]?.id

      const alreadyExists = currentUserId
        ? statePage.tabNotification.table.items.some((item) => {
          return currentUserId === item.user.id && item._id !== data._id
        })
        : false

      if (alreadyExists) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['user'],
          message: 'Já existe uma notificação para este usuário.'
        })
      }
    })

  type IFormSchema = z.infer<typeof formSchema>

  const parseInitialData = (data: Partial<IFormDataValues>): IFormSchema => ({
    _id: data?._id ?? null,
    id: data?.id ?? null,
    channel: data?.channel ?? null,
    frequency: data?.frequency ?? null,
    user: data?.user ?? [],
    configs: data?.configs ?? []
  })

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<IFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const defineValues = (dataParsed: Partial<IFormSchema>) =>
    Object.entries(dataParsed).forEach(([key, value]) => {
      setValue(key as keyof IFormSchema, value)
    })

  const setValues = (data: Partial<IFormDataValues> = {}) => {
    defineValues(parseInitialData(data))
  }

  const reset = () => {
    defineValues(parseInitialData({}))
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors, reset }
}

export default SectionForm
