import { http } from '@/@core/infra/http'
import { AxiosError } from 'axios'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsNotificationsApiV4 } from '@/@core/infra/api'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import { useStateAlarmesIdPage } from '../page.hooks'
import { IFormDataSend, INotification, INotificationCreate } from './Tab.types'
import { parseNotificationData } from './Tab.utils'

export const useMethodTabNotification = () => {
  const log = useLog()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const statePage = useStateAlarmesIdPage()

  const languageTab = useLanguageTabNotification()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const search = memory.local.get().alarms.record.tabNotifications

      const result = await alarmsNotificationsApiV4(http).get({
        alarmId: Number(statePage.tabData.alarm.id),
        limit: search.limit,
        page: search.page
      })

      statePage.set({
        tabNotification: {
          table: {
            items: result.data.items.map(parseNotificationData),
            lastPage: result.data.lastPage
          }
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabNotification/useMethodTabNotification/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }
  const handleSubmit = async ({ id, ...payload }: IFormDataSend) => {
    const currentId = id

    try {
      systemLoading.setLoading(true)

      const { status, data } = currentId
        ? await alarmsNotificationsApiV4(http).update(currentId, payload)
        : await alarmsNotificationsApiV4(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(
          languageTab.form.messages,
          currentId,
          conditionalRequest
        ),
        type: toastTypeSwitch(conditionalRequest)
      })

      if (!conditionalRequest) return { status: false }

      const newItem = parseNotificationData(data)

      let items = [...statePage.tabNotification.table.items]

      items = currentId
        ? items.map((item) => (item.id === currentId ? newItem : item))
        : [...items, newItem]

      statePage.set({
        tabNotification: {
          table: {
            ...statePage.tabNotification.table,
            items
          }
        }
      })

      return { status: true }
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/TabNotification/useMethodTabNotification/handleSubmit'
      })

      const { response } = error as AxiosError
      const responseMessage = (response?.data as string[])?.[0]

      systemToast.addToast({
        message:
          responseMessage ??
          toastMessageSwitch(languageTab.form.messages, currentId),
        type: 'error'
      })

      return { status: true }
    } finally {
      systemLoading.setLoading(false)
    }
  }
  const handleDelete = async (notificationId: number, callback: Function) => {
    const { messages } = languageTab.table.modalDelete

    try {
      systemLoading.setLoading(true)

      const { status } = await alarmsNotificationsApiV4(http).delete(
        notificationId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      if (!conditionalRequest) return systemLoading.setLoading(false)

      callback()
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title:
          '@core/presentation/views/alarms.id/TabNotification/Tab.hooks/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })

      systemLoading.setLoading(false)
    }
  }

  /** notification */
  const addNotification = (notification: INotificationCreate) => {
    const newNotification: INotification = {
      _id: new Date().getTime(),
      id: null,
      channel: notification.channel,
      frequency: notification.frequency,
      user: notification.user,
      configs: notification.configs
    }
    statePage.set({
      tabNotification: {
        table: {
          ...statePage.tabNotification.table,
          items: [...statePage.tabNotification.table.items, newNotification]
        }
      }
    })
  }
  const updateNotification = (
    _id: number,
    currentNotification: INotificationCreate
  ) => {
    statePage.set({
      tabNotification: {
        table: {
          ...statePage.tabNotification.table,
          items: [...statePage.tabNotification.table.items].map(
            (notification) => {
              return notification._id === _id
                ? {
                  ...notification,
                  ...currentNotification
                }
                : notification
            }
          )
        }
      }
    })
  }
  const deleteNotification = (_id: number) => {
    statePage.set({
      tabNotification: {
        table: {
          ...statePage.tabNotification.table,
          items: [...statePage.tabNotification.table.items].filter(
            (notification) => notification._id !== _id
          )
        }
      }
    })
  }

  return {
    addNotification,
    updateNotification,
    deleteNotification,
    fetchData,
    handleSubmit,
    handleDelete
  }
}

export const useLanguageTabNotification = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title, table, btnCreateNewAlarm } =
    pages.alarmsId.tabNotification

  return {
    title,
    form: {
      input: {
        channel: form.input.channel,
        frequency: form.input.frequency,
        user: form.input.user,
        email: form.input.email
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    },
    table: {
      columns: {
        channel: table.columns.channel,
        frequency: table.columns.frequency,
        user: table.columns.user,
        actions: table.columns.actions
      },
      modalDelete: table.modalDelete
    },
    btnCreateNewAlarm
  }
}
