import { useEffect } from 'react'

import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { useStateSection } from '@/@core/presentation/shared/AlarmRuleStage/Section.hook'
import { HeaderList } from '@/@core/presentation/shared/pages/HeaderList'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'
import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'
import { Tabs } from '@/@core/presentation/shared/ui/tabs'

import { useLanguageAlarmesIdPage, useStateAlarmesIdPage } from './page.hooks'
import { TabData } from './TabData/Tab'
import { TabHistory } from './TabHistorical/Tab'
import { TabMonitoring } from './TabMonitoring/Tab'
import { TabNotification } from './TabNotification/Tab'
import { TabRules } from './TabRules/Tab'

export const Page = () => {
  const system = useSystemStore()
  const systemLoading = useSystemLoadingStore()

  const statePage = useStateAlarmesIdPage()
  const languagePage = useLanguageAlarmesIdPage()

  const stateSection = useStateSection()

  useTitlePage(languagePage.title)
  useBreadcrumb('alarms.id')

  useEffect(() => {
    return () => {
      statePage.reset()
      stateSection.reset()

      memory.local.reset('alarms')

      system.setUnmountComponent([
        'alarms.id-TabData',
        'alarms.id-TabRules',
        'alarms.id-TabNotification',
        'alarms.id-TabHistorical',
        `component-SectionRuleStage[1]`,
        `component-SectionRuleStage[2]`,
        `alarms.id-TabMonitoring`
      ])
    }
  }, [])

  return (
    <PageContent>
      <Breadcrumbs />

      <HeaderList.Root>
        <HeaderList.Content title={languagePage.title} className="mr-auto" />
      </HeaderList.Root>

      <Tabs.Root
        variant="primary"
        className="lg:flex items-start gap-2"
        value={statePage.tab.active}
        onValueChange={statePage.setTab}
      >
        <Tabs.List className="mb-3 lg:flex-col lg:pr-2 lg:mr-6 lg:min-w-[177px] lg:min-h-[350px] justify-start">
          <Tabs.Trigger
            value="data"
            disabled={
              systemLoading.state.loading ||
              (!statePage.isEdit &&
                !system.state.mountComponent?.['alarms.id-TabData'])
            }
          >
            {languagePage.tabs.data}
          </Tabs.Trigger>

          <Tabs.Trigger
            value="rules"
            disabled={
              systemLoading.state.loading ||
              (!statePage.isEdit &&
                !system.state.mountComponent?.['alarms.id-TabRules'])
            }
          >
            {languagePage.tabs.rules}
          </Tabs.Trigger>

          <Tabs.Trigger
            value="monitoring"
            disabled={
              systemLoading.state.loading ||
              (!statePage.isEdit &&
                !system.state.mountComponent?.['alarms.id-TabMonitoring'])
            }
          >
            {languagePage.tabs.monitoring}
          </Tabs.Trigger>

          <Tabs.Trigger
            value="notification"
            disabled={
              systemLoading.state.loading ||
              (!statePage.isEdit &&
                !system.state.mountComponent?.['alarms.id-TabNotification'])
            }
          >
            {languagePage.tabs.notification}
          </Tabs.Trigger>

          <Tabs.Trigger
            className={cn('w-full justify-start')}
            value="historical"
            disabled={
              systemLoading.state.loading ||
              (!statePage.isEdit &&
                !system.state.mountComponent?.['alarms.id-TabHistory'])
            }
          >
            {languagePage.tabs.history}
          </Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="data" className="w-full">
          <TabData />
        </Tabs.Content>

        <Tabs.Content value="rules" className="w-full">
          <TabRules />
        </Tabs.Content>

        <Tabs.Content value="monitoring" className="w-full">
          <TabMonitoring />
        </Tabs.Content>

        <Tabs.Content value="notification" className="w-full">
          <TabNotification />
        </Tabs.Content>

        <Tabs.Content value="historical" className="w-full">
          <TabHistory />
        </Tabs.Content>
      </Tabs.Root>
    </PageContent>
  )
}
