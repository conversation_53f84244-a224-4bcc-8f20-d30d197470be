import { alarmsConditionalsMock1 } from '@/__mock__/content/api-alarms-conditionals.content'
import { alarmsNotificationsMock1 } from '@/__mock__/content/api-alarms-notifications.content'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { parseConditionalData } from '../../shared/AlarmRuleStage/Section.utils'
import {
  parseNotificationOutput,
  parseRuleStageOutput,
  parseTabDataOutput,
  parseTabRuleOutput
} from './page.utils'
import { IAlarmData } from './TabData/Tab.types'
import { IAlarmMonitoringData } from './TabMonitoring/Tab.types'
import { parseNotificationData } from './TabNotification/Tab.utils'
import { IRuleData } from './TabRules/Tab.types'

describe('@core/presentation/views/alarms.id/page.utils', () => {
  const alarmData: Partial<IAlarmData> = { ...alarmMock1 }
  it('parseTabDataOutput', () => {
    const dataInput: Partial<IAlarmMonitoringData> = {
      timeConfirmation: 1,
      initialHour: '00:00',
      finalHour: '23:59',
      daysWeek: ['1', '2', '3', '4', '5', '6'],
      daysRetention: '1'
    }
    const dataOutput = {
      id: 56,
      name: 'Fator potência horário',
      description: '',
      accountId: 9,
      categoryId: 3,
      finalHour: '23:59',
      initialHour: '00:00',
      status: true,
      timeConfirmation: 1,
      daysWeek: [1, 2, 3, 4, 5, 6],
      daysRetention: 1
    }

    dataInput.daysWeek = undefined
    dataOutput.daysWeek = []
    expect(parseTabDataOutput(alarmData, dataInput)).toEqual(dataOutput)

    dataInput.daysWeek = ['1', '2', '3', '4', '5', '6']
    dataOutput.daysWeek = [1, 2, 3, 4, 5, 6]
    expect(parseTabDataOutput(alarmData, dataInput)).toEqual(dataOutput)
  })

  it('parseTabRuleOutput', () => {
    const dataInputCompany: IRuleData = {
      alarmId: 145,
      target: [{ id: 2, name: 'Empresa' }],
      targetType: { id: 2, name: 'Emeprsa 2', slug: 'companies' }
    }
    const dataOutputCompany = {
      alarmId: 145,
      companies: [2],
      equipments: [],
      targets: [],
      type: 'companies'
    }
    expect(parseTabRuleOutput(dataInputCompany)).toEqual(dataOutputCompany)

    const dataInputEquipment: IRuleData = {
      alarmId: 145,
      target: [{ id: 2, name: 'Empresa' }],
      targetType: { id: 2, name: 'Equipamento 2', slug: 'equipments' }
    }
    const dataOutputEquipment = {
      alarmId: 145,
      companies: [],
      equipments: [2],
      targets: [],
      type: 'equipments'
    }
    expect(parseTabRuleOutput(dataInputEquipment)).toEqual(dataOutputEquipment)

    const dataInputTemplate: IRuleData = {
      alarmId: 145,
      target: [{ id: 2, name: 'Empresa' }],
      targetType: { id: 2, name: 'Template', slug: 'template' }
    }
    const dataOutputTemplate = {
      alarmId: 145,
      companies: [],
      equipments: [],
      targets: [2],
      type: 'template'
    }
    expect(parseTabRuleOutput(dataInputTemplate)).toEqual(dataOutputTemplate)
  })
  it('parseRuleStageOutput', () => {
    const alarmId = 1
    const alarmStageId = 1
    let conditional = parseConditionalData(alarmsConditionalsMock1)

    expect(
      parseRuleStageOutput({ alarmId, alarmStageId, conditional })
    ).toEqual({
      alarmId: 1,
      alarmRuleId: 7,
      alarmStageId: 1,
      operatorLogicId: 3,
      processedId: 8,
      propertyId: 11,
      value: '44'
    })

    conditional = parseConditionalData({
      ...alarmsConditionalsMock1,
      processed: null,
      property: null
    })

    expect(
      parseRuleStageOutput({ alarmId, alarmStageId, conditional })
    ).toEqual({
      alarmId: 1,
      alarmRuleId: 7,
      alarmStageId: 1,
      operatorLogicId: 3,
      processedId: null,
      propertyId: null,
      value: '44'
    })
  })
  it('parseNotificationOutput', () => {
    const alarmId = 1
    const notification = parseNotificationData(alarmsNotificationsMock1)

    expect(parseNotificationOutput({ alarmId, notification })).toEqual({
      alarmId: 1,
      id: 1,
      alarmNotificationChannelId: 1,
      frequencyId: 1,
      userId: 1,
      configs: ['<EMAIL>', '<EMAIL>']
    })
  })
})
