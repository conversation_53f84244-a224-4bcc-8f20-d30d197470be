import { useRouter } from 'next/router'
import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import {
  alarmsApiV4,
  alarmsConditionalsApiV4,
  alarmsMultiTargetsApiV4
} from '@/@core/infra/api'
import { alarmsNotificationsApiV4 } from '@/@core/infra/api/AlarmsNotificationsApiV4'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { useStateSection } from '@/@core/presentation/shared/AlarmRuleStage/Section.hook'

import { stateData } from './page.content'
import { IState } from './page.types'
import {
  parseNotificationOutput,
  parseRuleStageOutput,
  parseTabDataOutput,
  parseTabRuleOutput
} from './page.utils'

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => {
    set((state) => ({ ...state, ...initialData }))
  },
  reset: () => {
    set((state) => ({ ...state, ...stateData }))
  },
  setTab: (active: string) => {
    set((state) => ({
      ...state,
      tab: { ...state.tab, active }
    }))
  },
  setTabNotification: (tabNotification) => {
    set((state) => ({
      ...state,
      tabNotification: { ...state.tabNotification, ...tabNotification }
    }))
  },
  setTabHistorical: (tabHistorical) => {
    set((state) => ({
      ...state,
      tabHistorical: { ...state.tabNotification, ...tabHistorical }
    }))
  }
}))

export const useStateAlarmesIdPage = () => {
  const state = statePage()

  const isEdit = !!state.tabData.alarm?.id

  return { ...state, isEdit }
}

export const useMethodsAlarmesIdPage = () => {
  const router = useRouter()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const statePage = useStateAlarmesIdPage()
  const languagePage = useLanguageAlarmesIdPage()

  const stateSection = useStateSection()

  const handleCreateAlarm = async () => {
    const payload = parseTabDataOutput(
      statePage.tabData.alarm,
      statePage.tabMonitoring.monitoring
    )

    return await alarmsApiV4(http)
      .create(payload)
      .then(({ status, data }) => (status === 201 ? data : null))
  }
  const handleCreateMultiTargets = async (alarmId: number) => {
    const { target, targetType } = statePage.tabRules

    const payload = parseTabRuleOutput({ target, targetType, alarmId })

    await alarmsMultiTargetsApiV4(http).create(payload)
  }
  const handleCreateRuleStage = async (
    alarmId: number,
    alarmStageId: 1 | 2
  ) => {
    const mapConditionals = {
      1: stateSection.itemsConditionalNormalization,
      2: stateSection.itemsConditionalTriggering
    }

    const promises = mapConditionals?.[alarmStageId].map((conditional) => {
      const payload = parseRuleStageOutput({
        alarmId,
        alarmStageId,
        conditional
      })
      return alarmsConditionalsApiV4(http).create(payload)
    })

    await Promise.all(promises)
  }
  const handleCreateNotifications = async (alarmId: number) => {
    const { items: notifications } = statePage.tabNotification.table

    const promises = notifications.map((notification) => {
      const payload = parseNotificationOutput({ alarmId, notification })
      return alarmsNotificationsApiV4(http).create(payload)
    })

    await Promise.all(promises)
  }

  const createNewAlarm = async () => {
    try {
      systemLoading.setLoading(true)

      // 1. Cria o alarme inicial
      const newAlarm = await handleCreateAlarm()

      if (!newAlarm) throw Error('Erro ao criar alarme')

      const { id: alarmId } = newAlarm

      /**  2. Cria target */
      await handleCreateMultiTargets(alarmId)

      /**  3. Cria Disparo */
      await handleCreateRuleStage(alarmId, 1)

      /**  4. Cria Normalização */
      await handleCreateRuleStage(alarmId, 2)

      /**  5. Cria notificação */
      await handleCreateNotifications(alarmId)

      systemToast.addToast({
        message: languagePage.messages.createSuccessMessage,
        type: 'success'
      })

      router.push(`/alarms/${alarmId}`)
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: '@core/presentation/views/alarms.id/page.hooks/createAlarm'
      })

      systemToast.addToast({
        message: languagePage.messages.createErrorMessage,
        type: 'error'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { createNewAlarm }
}

export const useLanguageAlarmesIdPage = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages } = languageByMode(lang)

  const {
    tabs,
    title,
    tabData: {
      form: { messages }
    }
  } = pages.alarmsId

  return {
    title,
    tabs,
    messages
  }
}
