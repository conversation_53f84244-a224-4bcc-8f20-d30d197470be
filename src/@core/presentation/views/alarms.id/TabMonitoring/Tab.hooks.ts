import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { toastMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import { useStateAlarmesIdPage } from '../page.hooks'
import { parseAlarmData } from '../TabData/Tab.utils'
import { IFormData } from './Tab.types'
import {
  formDataCreateOutput,
  formDataOutput,
  parseAlarmMonitoringData
} from './Tab.utils'

export const useMethodTabMonitoring = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const statePage = useStateAlarmesIdPage()

  const languageTab = useLanguageTabMonitoring()

  const onSubmit = async (values: IFormData) => {
    if (!statePage.isEdit) {
      statePage.set({
        tabMonitoring: {
          monitoring: formDataCreateOutput(values)
        }
      })
      statePage.setTab('notification')
      return
    }

    try {
      systemLoading.setData({ pageLoading: true })

      const payload = formDataOutput(statePage.tabData.alarm, values)

      const { status, data } = await alarmsApiV4(http).update(
        Number(payload.id),
        payload
      )

      const conditionalRequest = status === 200

      systemToast.addToast({
        message: toastMessageSwitch(
          languageTab.form.messages,
          payload.id,
          conditionalRequest
        ),
        type: toastTypeSwitch(conditionalRequest)
      })

      if (!conditionalRequest) return

      statePage.set({
        tabData: { alarm: parseAlarmData(data) },
        tabMonitoring: { monitoring: parseAlarmMonitoringData(data) }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabMonitoring/useMethodTabMonitoring/onSubmit'
      })

      systemToast.addToast({
        message: languageTab.form.messages.updateErrorMessage,
        type: 'error'
      })
    } finally {
      systemLoading.setData({ pageLoading: false })
    }
  }
  return { onSubmit }
}

export const useLanguageTabMonitoring = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabMonitoring
  const {
    form: { messages }
  } = pages.alarmsId.tabData

  return {
    title,
    form: {
      input: {
        days: form.input.days,
        initialHour: form.input.initialHour,
        fimHour: form.input.fimHour,
        preActivation: form.input.preActivation,
        daysRetention: form.input.daysRetention,
        daysWeekWarning: form.input.daysWeekWarning
      },
      daysWeek: form.daysWeek,
      messages,
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    }
  }
}
