import IAlarm from '@/@core/domain/Alarm'
import { IAlarmData } from '../TabData/Tab.types'
import { IAlarmMonitoringData, IFormData, IFormDataSend } from './Tab.types'

export const parseAlarmMonitoringData = (
  data: IAlarm
): IAlarmMonitoringData => {
  return {
    id: data.id,
    timeConfirmation: data.timeConfirmation,
    initialHour: data.initialHour,
    finalHour: data.finalHour,
    daysWeek: data.daysWeek.map(String),
    daysRetention: String(data.daysRetention)
  }
}
export const formDataInput = (data: IAlarmMonitoringData): IFormData => {
  return {
    timeConfirmation: data.timeConfirmation,
    initialHour: data.initialHour,
    finalHour: data.finalHour,
    daysWeek: data.daysWeek,
    daysRetention: data.daysRetention
  }
}
export const formDataOutput = (
  alarmData: Partial<IAlarmData>,
  formValues: IFormData
): IFormDataSend => {
  return {
    /** alarmData */
    id: alarmData.id!,
    name: alarmData.name!,
    description: alarmData.description!,
    status: !!alarmData.status!,
    accountId: alarmData.account?.id,
    categoryId: alarmData.categoryId,
    /** formValues */
    timeConfirmation: Number(formValues.timeConfirmation),
    initialHour: formValues.initialHour,
    finalHour: formValues.finalHour,
    daysWeek: formValues.daysWeek.map(Number),
    daysRetention: Number(formValues.daysRetention)
  }
}

export const formDataCreateOutput = (
  values: Partial<IFormData>
): Partial<IAlarmMonitoringData> => {
  return {
    timeConfirmation: values?.timeConfirmation,
    initialHour: values?.initialHour,
    finalHour: values?.finalHour,
    daysWeek: values?.daysWeek?.map(String) ?? [],
    daysRetention: String(values?.daysRetention)
  }
}
