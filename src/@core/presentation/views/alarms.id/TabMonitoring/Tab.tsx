import { useEffect } from 'react'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { PageSection } from '@/@core/presentation/shared/pages'

import { useLanguageTabMonitoring } from './Tab.hooks'
import { SectionMonitoring } from './_components/SectionMonitoring'

export const TabMonitoring = () => {
  const system = useSystemStore()
  const languageTab = useLanguageTabMonitoring()

  useEffect(() => {
    if (!system.state.mountComponent?.['alarms.id-TabMonitoring']) {
      system.setMountComponent('alarms.id-TabMonitoring')
    }
  }, [])

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2 space-y-[8px]">
            <PageSection.Content title={languageTab.title} />
          </div>
        </div>
      </PageSection.Root>
      <SectionMonitoring />
    </>
  )
}
