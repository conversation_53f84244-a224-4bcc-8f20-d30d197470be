import IAlarm from '@/@core/domain/Alarm'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { IAlarmData } from '../TabData/Tab.types'
import { IAlarmMonitoringData, IFormData, IFormDataSend } from './Tab.types'
import {
  formDataCreateOutput,
  formDataInput,
  formDataOutput,
  parseAlarmMonitoringData
} from './Tab.utils'

describe('src/@core/presentation/views/alarms.id/TabMonitoring/Tab.utils', () => {
  const alarmData: IAlarmData = { ...alarmMock1 }

  it('should check return the function parseAlarmMonitoringData', () => {
    const formValues: IAlarm = { ...alarmMock1 }
    const dataOutput: IAlarmMonitoringData = {
      id: 56,
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: ['0', '1', '2', '3', '4', '5', '6']
    }
    expect(parseAlarmMonitoringData(formValues)).toEqual(dataOutput)
  })

  it('should check return the function formDataInput', () => {
    const formValues: IAlarmMonitoringData = {
      id: 56,
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: ['0', '1', '2', '3', '4', '5', '6']
    }
    const dataOutput: IFormData = {
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: ['0', '1', '2', '3', '4', '5', '6']
    }
    expect(formDataInput(formValues)).toEqual(dataOutput)
  })

  it('should check return the function formDataOutput', () => {
    const formValues: IFormData = {
      timeConfirmation: 0,
      initialHour: '12:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: ['0', '1', '2', '3', '4', '5', '6']
    }
    const dataOutput: IFormDataSend = {
      /** alarmData */
      id: 56,
      name: alarmData.name,
      description: alarmData.description,
      status: alarmData.status,
      accountId: alarmData.account?.id,
      categoryId: alarmData.categoryId,
      /** formValues */
      timeConfirmation: 0,
      initialHour: '12:00',
      finalHour: '23:59',
      daysRetention: 1,
      daysWeek: [0, 1, 2, 3, 4, 5, 6]
    }
    expect(formDataOutput(alarmData, formValues)).toEqual(dataOutput)
  })

  it('should check return the function formDataCreateOutput', () => {
    /** payload with full values  */
    const inputFullData: Partial<IFormData> = {
      timeConfirmation: 0,
      initialHour: '12:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: ['0', '1', '2', '3', '4', '5', '6']
    }
    const outputFullData: Partial<IAlarmMonitoringData> = {
      timeConfirmation: 0,
      initialHour: '12:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: ['0', '1', '2', '3', '4', '5', '6']
    }
    const fullResultOutput = formDataCreateOutput(inputFullData)
    expect(fullResultOutput).toEqual(outputFullData)

    /** payload with partials values  */
    const inputPartialData: Partial<IFormData> = {
      timeConfirmation: 0,
      initialHour: '12:00',
      finalHour: '23:59',
      daysRetention: '1'
    }
    const outputPartialData: Partial<IAlarmMonitoringData> = {
      timeConfirmation: 0,
      initialHour: '12:00',
      finalHour: '23:59',
      daysRetention: '1',
      daysWeek: []
    }
    const partialResultOutput = formDataCreateOutput(inputPartialData)
    expect(partialResultOutput).toEqual(outputPartialData)
  })
})
