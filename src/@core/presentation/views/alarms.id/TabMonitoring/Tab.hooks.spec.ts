import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStateAlarmesIdPage } from '../page.hooks'
import { useLanguageTabMonitoring, useMethodTabMonitoring } from './Tab.hooks'
import { IFormData } from './Tab.types'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4')

const spyAlarmsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4'),
  'alarmsApiV4'
)

describe('src/@core/presentation/views/alarms.id/TabMonitoring/Tab.hooks | useMethodTabMonitoring', () => {
  const payload: IFormData = {
    timeConfirmation: 10,
    initialHour: '08:00',
    finalHour: '18:00',
    daysWeek: ['1', '2', '3'],
    daysRetention: '5'
  }

  beforeEach(() => {
    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: { id: 123 }
    }))
  })

  it('should check return the function onSubmit when creating', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodTabMonitoring(),
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => {
      result.current.state.reset()
    })

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts).toHaveLength(0)
    expect(result.current.state.tabMonitoring.monitoring).toEqual({
      timeConfirmation: 10,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: ['1', '2', '3'],
      daysRetention: '5'
    })
  })

  it('should check return the function onSubmit when editing', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodTabMonitoring(),
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request not authenticated **/
    await waitFor(() => {
      result.current.toast.reset()
      result.current.state.reset()
      result.current.state.set({ tabData: { alarm: { id: 56 } } })
    })

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 403,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar alarme'
    )

    /* request error **/
    await waitFor(() => {
      result.current.toast.reset()
      result.current.state.reset()
      result.current.state.set({ tabData: { alarm: { id: 56 } } })
    })

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar alarme'
    )

    /* request success **/
    await waitFor(() => {
      result.current.toast.reset()
      result.current.state.reset()
      result.current.state.set({ tabData: { alarm: { id: 56 } } })
    })

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: { ...alarmMock1 }
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts[0].type).toBe('success')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Alarme atualizado com sucesso'
    )
  })
})

describe('src/@core/presentation/views/alarms.id/TabMonitoring/Tab.hooks | useLanguageTabMonitoring', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabMonitoring()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.form.input).toEqual({
      days: 'Todos',
      daysRetention: 'Dias de retenção do log',
      daysWeekWarning: 'Selecione um dia da semana no mínimo.',
      fimHour: 'Horário de fim',
      initialHour: 'Horário de início',
      preActivation: 'Pré acionamento'
    })
  })
})
