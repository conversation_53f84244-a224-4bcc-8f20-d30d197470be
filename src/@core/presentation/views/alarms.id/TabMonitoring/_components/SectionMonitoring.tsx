import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/router'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'

import { useStateAlarmesIdPage } from '../../page.hooks'
import { useLanguageTabMonitoring, useMethodTabMonitoring } from '../Tab.hooks'
import { IAlarmMonitoringData } from '../Tab.types'

export const SectionMonitoring = () => {
  const router = useRouter()

  const system = useSystemStore()
  const systemLoading = useSystemLoadingStore()

  const statePage = useStateAlarmesIdPage()
  const languageTab = useLanguageTabMonitoring()
  const methodTab = useMethodTabMonitoring()

  const formFields = useFormFields()

  useEffect(() => {
    formFields.setValues(statePage.tabMonitoring.monitoring)
  }, [])

  const daysOfWeek = useMemo(() => {
    return [
      { label: languageTab.form.daysWeek.sunday, value: '0' },
      { label: languageTab.form.daysWeek.monday, value: '1' },
      { label: languageTab.form.daysWeek.tuesday, value: '2' },
      { label: languageTab.form.daysWeek.wednesday, value: '3' },
      { label: languageTab.form.daysWeek.thursday, value: '4' },
      { label: languageTab.form.daysWeek.friday, value: '5' },
      { label: languageTab.form.daysWeek.saturday, value: '6' }
    ]
  }, [languageTab])

  const allSelected = useMemo(() => {
    return formFields.values.daysWeek.length === daysOfWeek.length
  }, [formFields.values.daysWeek])

  const showDaysWeekWarning = useMemo(() => {
    return formFields.values.daysWeek.length === 0
  }, [formFields.values.daysWeek])

  return (
    <form
      className="form-container"
      onSubmit={formFields.handleSubmit(() =>
        methodTab.onSubmit(formFields.values)
      )}
    >
      <div
        className={cn(
          'flex items-center gap-2 rounded-xl px-3',
          showDaysWeekWarning
            ? 'border my-3 py-3 opacity-100'
            : 'overflow-y-hidden h-0 opacity-0'
        )}
      >
        <Badge.Root variant="alert">
          <Badge.Content>
            {languageTab.form.input.daysWeekWarning}
          </Badge.Content>
        </Badge.Root>
      </div>

      <div className="flex flex-wrap items-center gap-4 mb-4">
        <div className="border-r p-4">
          <Switch.Content
            label={languageTab.form.daysWeek.all}
            labelPosition="start"
            checked={allSelected}
            onChange={(value) => {
              if (value)
                formFields.setValue(
                  'daysWeek',
                  daysOfWeek.map((d) => d.value)
                )
              if (!value) formFields.setValue('daysWeek', [])
            }}
            disabled={systemLoading.state.loading}
          />
        </div>

        {daysOfWeek.map((day) => (
          <Switch.Content
            key={day.value}
            label={day.label}
            labelPosition="start"
            checked={formFields.values.daysWeek.includes(day.value)}
            onChange={(value) => {
              formFields.setValue(
                'daysWeek',
                value
                  ? [...formFields.values.daysWeek, day.value]
                  : formFields.values.daysWeek.filter((d) => d !== day.value)
              )
            }}
            disabled={systemLoading.state.loading}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
        <Input.Root>
          <Input.Label htmlFor="initialHour">
            {languageTab.form.input.initialHour}
          </Input.Label>
          <Input.ContentTime
            className="w-full"
            value={formFields.values.initialHour ?? ''}
            onChange={(value) => {
              formFields.setValue(
                'initialHour',
                value ? value.format('HH:mm') : ''
              )
            }}
            views={['hours', 'minutes']}
            helperErrorText={formFields.errors.initialHour?.message}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="fimHour">
            {languageTab.form.input.fimHour}
          </Input.Label>
          <Input.ContentTime
            className="w-full"
            value={formFields.values.finalHour ?? ''}
            onChange={(value) => {
              formFields.setValue(
                'finalHour',
                value ? value.format('HH:mm') : ''
              )
            }}
            views={['hours', 'minutes']}
            helperErrorText={formFields.errors.finalHour?.message}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="preActivation">
            {languageTab.form.input.preActivation}
          </Input.Label>
          <Input.Content
            type="number"
            min={0}
            value={formFields.values.timeConfirmation}
            onChange={(e) => {
              formFields.setValue('timeConfirmation', Number(e.target.value))
            }}
            helperText={formFields.errors.timeConfirmation?.message}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="daysRetention">
            {languageTab.form.input.daysRetention}
          </Input.Label>
          <Input.Content
            type="number"
            min={0}
            value={formFields.values.daysRetention ?? ''}
            onChange={(e) => {
              formFields.setValue('daysRetention', e.target.value)
            }}
            helperText={formFields.errors.daysRetention?.message}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>
      </div>

      <div className="footer-form">
        <Button
          type="button"
          onClick={() => router.push('/alarms')}
          disabled={systemLoading.state.loading}
        >
          {languageTab.form.btn.cancel}
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={systemLoading.state.loading}
        >
          {languageTab.form.btn.save}
        </Button>
      </div>
    </form>
  )
}

const useFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabMonitoring()

  const formSchema = z
    .object({
      timeConfirmation: z.number(),
      initialHour: z.string(),
      finalHour: z.string(),
      daysWeek: z.array(z.string()),
      daysRetention: z.string()
    })
    .superRefine((values, ctx) => {
      if (!values.initialHour || values.initialHour.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: message,
          path: ['initialHour']
        })
      }
      if (!values.finalHour || values.finalHour.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: message,
          path: ['finalHour']
        })
      }
      if (values.daysWeek.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: message,
          path: ['daysWeek']
        })
      }
    })

  type IFormSchema = z.infer<typeof formSchema>

  const parseInitialData = (
    data: Partial<IAlarmMonitoringData>
  ): IFormSchema => {
    return {
      timeConfirmation: data.timeConfirmation ?? 0,
      initialHour: data.initialHour ?? '00:00',
      finalHour: data.finalHour ?? '23:00',
      daysWeek: data.daysWeek?.map?.(String) ?? [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6'
      ],
      daysRetention: data.daysRetention ?? ''
    }
  }

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<IFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const defineValues = (dataParsed: Partial<IFormSchema>) =>
    Object.entries(dataParsed).forEach(([key, value]) => {
      setValue(key as keyof IFormSchema, value)
    })

  const setValues = (data: Partial<IAlarmMonitoringData> = {}) => {
    defineValues(parseInitialData(data))
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors }
}
