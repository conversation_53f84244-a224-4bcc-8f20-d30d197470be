export type IAlarmMonitoringData = {
  id: number | null
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: string[]
  daysRetention: string
}

export type IFormData = {
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: string[]
  daysRetention: string
}

export type IFormDataSend = {
  id: number | null
  name: string
  description: string
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: number[]
  daysRetention: number
  status: boolean
  accountId?: number
  categoryId?: number
}
