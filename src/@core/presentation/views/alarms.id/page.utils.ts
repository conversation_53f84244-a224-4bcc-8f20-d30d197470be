import { IConditional } from '../../shared/AlarmRuleStage/Section.types'
import { IAlarmData, IFormDataSend } from './TabData/Tab.types'
import { IAlarmMonitoringData } from './TabMonitoring/Tab.types'
import { INotification } from './TabNotification/Tab.types'
import { IRuleData } from './TabRules/Tab.types'

export const parseTabDataOutput = (
  alarmData: Partial<IAlarmData>,
  monitoringData: Partial<IAlarmMonitoringData>
): IFormDataSend => {
  return {
    id: Number(alarmData.id),
    name: String(alarmData.name),
    description: String(alarmData.description),
    status: !!alarmData.status,
    accountId: Number(alarmData.account?.id),
    categoryId: Number(alarmData.categoryId),
    timeConfirmation: Number(monitoringData.timeConfirmation),
    initialHour: String(monitoringData.initialHour),
    finalHour: String(monitoringData.finalHour),
    daysWeek: monitoringData.daysWeek?.map(Number) ?? [],
    daysRetention: Number(monitoringData.daysRetention)
  }
}
export const parseTabRuleOutput = (data: IRuleData) => {
  const {
    alarmId,
    targetType: { slug },
    target
  } = data

  const targetId = Number(target?.[0]?.id)

  return {
    alarmId: alarmId,
    type: slug,
    targets: slug === 'template' ? [targetId] : [],
    equipments: slug === 'equipments' ? [targetId] : [],
    companies: slug === 'companies' ? [targetId] : []
  }
}
export const parseRuleStageOutput = ({
  alarmId,
  alarmStageId,
  conditional
}: {
  alarmId: number
  alarmStageId: number
  conditional: IConditional
}) => {
  const { rule, property, processed, operator, value } = conditional

  return {
    alarmId,
    alarmStageId,
    alarmRuleId: Number(rule?.id),
    propertyId: property?.id ? Number(property?.id) : null,
    processedId: processed?.id ? Number(processed?.id) : null,
    operatorLogicId: Number(operator?.id),
    value: value
  }
}
export const parseNotificationOutput = ({
  alarmId,
  notification
}: {
  alarmId: number
  notification: INotification
}) => {
  const { id, channel, frequency, user, configs } = notification

  return {
    alarmId,
    id,
    alarmNotificationChannelId: Number(channel?.id),
    userId: Number(user.id),
    frequencyId: Number(frequency?.id),
    configs: configs
  }
}
