import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { alarmsMultiTargetsApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { ListAlarmsRulesService } from '@/@core/services/listAlarmsRulesService'

import { useStateAlarmesIdPage } from '../page.hooks'
import { parseAlarmsMultiTargetsData } from './Tab.utils'

export const useMethodsTabRules = () => {
  const systemLoading = useSystemLoadingStore()
  const log = useLog()

  const listAlarmsRulesService = ListAlarmsRulesService()

  const statePage = useStateAlarmesIdPage()

  const fetchData = async ({ alarmId }: { alarmId: number }) => {
    try {
      systemLoading.setLoading(true)

      const { status, data } = await alarmsMultiTargetsApiV4(http).get({
        alarmId,
        sort: 'id',
        order: 'desc'
      })

      if (status !== 200) return

      const { dataEntityId, target, targetType } =
        parseAlarmsMultiTargetsData(data)

      statePage.set({
        tabRules: { dataEntityId, target, targetType }
      })

      await listAlarmsRulesService.handler({ dataEntityId })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: '@core/presentation/views/alarms.id/TabRules/Tab.hooks/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  return { fetchData }
}

export const useLanguageTabRules = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean, continue: btnContinue } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabRules

  return {
    title,
    form: {
      target: form.target,
      shot: form.shot,
      normalized: form.normalized,
      requiredField,
      btn: { cancel, save, confirm, add, clean, continue: btnContinue }
    }
  }
}
