import { useEffect, useRef } from 'react'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import AlarmRuleStage, {
  ISectionRef
} from '@/@core/presentation/shared/AlarmRuleStage/Section'
import { useLanguageSection } from '@/@core/presentation/shared/AlarmRuleStage/Section.hook'
import { ListAlarmsOperatorLogicService } from '@/@core/services/listAlarmsOperatorLogicService'
import { ListAlarmsTargetsTypesService } from '@/@core/services/listAlarmsTargetsTypesService'

import { useStateAlarmesIdPage } from '../page.hooks'
import SectionRuleTarget from './_components/SectionRuleTarget'
import { useMethodsTabRules } from './Tab.hooks'

export const TabRules = () => {
  const isMounted = useRef<boolean>(false)
  const sectionTriggeringRef = useRef<ISectionRef>({})
  const sectionNormalizationRef = useRef<ISectionRef>({})

  const system = useSystemStore()
  const listAlarmsOperatorLogicService = ListAlarmsOperatorLogicService()
  const listAlarmsTargetsTypesService = ListAlarmsTargetsTypesService()

  const statePage = useStateAlarmesIdPage()
  const methodsTab = useMethodsTabRules()

  const languageSection = useLanguageSection()

  useEffect(() => {
    if (
      !isMounted.current &&
      !system.state.mountComponent?.['alarms.id-TabRules']
    ) {
      const handler = () => {
        listAlarmsOperatorLogicService.handler()
        listAlarmsTargetsTypesService.handler()

        if (!statePage.isEdit) return

        const { id: alarmId } = statePage.tabData.alarm

        methodsTab.fetchData({ alarmId: Number(alarmId) })
      }
      handler()

      system.setMountComponent('alarms.id-TabRules')
    }

    return () => {
      isMounted.current = true
    }
  }, [])

  return (
    <>
      <SectionRuleTarget
        validateConditionals={() => {
          return [
            sectionTriggeringRef.current?.validateConditionals?.(),
            sectionNormalizationRef.current?.validateConditionals?.()
          ].every(Boolean)
        }}
      />

      <AlarmRuleStage
        ref={sectionTriggeringRef}
        title={languageSection.triggering.title}
        titleList={languageSection.triggering.titleList}
        alarmStageId={1}
        dataEntityId={statePage.tabRules.dataEntityId}
        alarmId={statePage.tabData.alarm?.id}
        isEdit={statePage.isEdit}
        disabled={!statePage.tabRules.dataEntityId}
      />

      <AlarmRuleStage
        ref={sectionNormalizationRef}
        title={languageSection.normalization.title}
        titleList={languageSection.normalization.titleList}
        alarmStageId={2}
        dataEntityId={statePage.tabRules.dataEntityId}
        alarmId={statePage.tabData.alarm?.id}
        isEdit={statePage.isEdit}
        disabled={!statePage.tabRules.dataEntityId}
      />
    </>
  )
}
