import { IAlarmsMultiTargets } from '@/@core/domain/AlarmsMultiTargets'

export const parseAlarmsMultiTargetsData = (
  data: IAlarmsMultiTargets | null
) => {
  const { companies, equipments, targets } = data ?? {
    companies: [],
    equipments: [],
    targets: []
  }
  const [currentTarget] = targets
  const targetType = currentTarget?.targetType

  let target: { id: number; name: string }[] = [
    { id: Number(currentTarget), name: String(currentTarget?.name) }
  ]

  if (companies.length) {
    target = companies
  }
  if (equipments.length) {
    target = equipments
  }

  return {
    target,
    targetType: {
      id: Number(targetType?.id),
      name: String(targetType?.name),
      slug: String(targetType?.slug)
    },
    dataEntityId: Number(targetType?.dataEntity?.id)
  }
}
