import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useMemo, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useListAlarmsTargetsTypes from '@/@core/framework/store/hook/useListAlarmsTargetsTypes'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { alarmsTargetsApiV4 } from '@/@core/infra/api'
import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import {
  formatInputValue,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { ListAlarmsRulesService } from '@/@core/services/listAlarmsRulesService'

import { useStateAlarmesIdPage } from '../../page.hooks'
import { useLanguageTabRules } from '../Tab.hooks'
import { ITargetTypeOption } from '../Tab.types'

const CONST_CATEGORY_USER_ID = 3

export default function SectionRuleTarget({
  validateConditionals
}: {
  validateConditionals?: () => void
}) {
  const isMounted = useRef<boolean>(false)

  const system = useSystemStore()
  const systemLoading = useSystemLoadingStore()

  const listAlarmsTargetsTypes = useListAlarmsTargetsTypes()
  const listAlarmsRulesService = ListAlarmsRulesService()

  const statePage = useStateAlarmesIdPage()
  const formFields = useFormFields()

  const languageTab = useLanguageTabRules()

  const handlerInputTargetType = async (item: unknown) => {
    const targetType = item as ITargetTypeOption

    formFields.setValues({
      dataEntityId: Number(targetType?.dataEntity?.id),
      targetType: [
        {
          id: Number(targetType?.id),
          name: String(targetType?.name),
          slug: String(targetType?.slug)
        }
      ],
      target: []
    })

    if (
      !!targetType?.dataEntity?.id &&
      !!formFields.values.dataEntityId &&
      targetType?.dataEntity?.id === formFields.values.dataEntityId
    ) {
      return
    }

    systemLoading.setLoading(true)

    memory.local.reset('alarmsRulesList')

    await listAlarmsRulesService.handler({
      dataEntityId: targetType?.dataEntity?.id
    })

    systemLoading.setLoading(false)
  }
  const onSubmit = async () => {
    statePage.set({
      tabRules: {
        target: formFields.values.target,
        targetType: {
          id: formFields.values.targetType[0].id,
          name: formFields.values.targetType[0].name,
          slug: formFields.values.targetType[0].slug
        },
        dataEntityId: Number(formFields.values.dataEntityId)
      }
    })

    if (!statePage.tabRules.target.length) return

    if (validateConditionals?.())
      statePage.set({
        tab: { active: 'monitoring' }
      })
  }

  const params = useMemo(() => {
    const { categoryId } = statePage.tabData.alarm
    const isCategoryUserId = categoryId === CONST_CATEGORY_USER_ID

    return isCategoryUserId
      ? {
        order: 'desc',
        sort: 'id',
        account_id: statePage.tabData.alarm.account?.id
      }
      : {
        order: 'desc',
        sort: 'id'
      }
  }, [statePage.tabData.alarm.categoryId])

  useEffect(() => {
    const { target, targetType } = statePage.tabRules

    const handler = () => {
      if (!target && !targetType) return

      formFields.setValues({
        target: target,
        targetType: [
          {
            id: targetType.id,
            name: targetType.name,
            slug: targetType.slug
          }
        ]
      })
    }
    handler()

    return () => {
      isMounted.current = true
    }
  }, [statePage.isEdit, statePage.tabRules])

  return (
    <form
      className="form-container mb-6"
      onSubmit={formFields.handleSubmit(onSubmit)}
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
        <TagInput.Root>
          <TagInput.Label htmlFor="targetType">
            {languageTab.form.target.input.targetType}
          </TagInput.Label>
          <TagInput.Content
            name="target"
            value={formFields.values.targetType?.[0]?.id?.toString()}
            onChange={async (values) => {
              const item = formatOutputValues(values)?.[0]
              handlerInputTargetType(item)
            }}
            options={listAlarmsTargetsTypes.state.list.map(formatInputValue)}
            disabled={systemLoading.state.loading || statePage.isEdit}
            helperText={formFields.errors.targetType?.message}
          />
        </TagInput.Root>

        <TagInput.Root
          className={cn(
            'duration-150',
            !!formFields.values.targetType?.[0]?.id ? '' : 'opacity-0 hidden'
          )}
        >
          <TagInput.Label htmlFor="target">
            {languageTab.form.target.input.target}
          </TagInput.Label>

          {/* EMPRESA */}
          {formFields.values.targetType?.[0]?.slug === 'companies' && (
            <TagInput.ContentApi
              name="target"
              value={formFields.values.target.map(formatInputValue)}
              onChange={(values) => {
                const item = formatOutputValues(values)?.[0]
                formFields.setValues({ target: [item] })
              }}
              featchData={(p) =>
                companiesApiV4(http).get({
                  ...p,
                  ...params,
                  order: 'desc',
                  sort: 'id'
                })
              }
              optionDisabled={formFields.values.target.map((e) =>
                e.id.toString()
              )}
              placeholder={languageTab.form.target.input.placeholderCompany}
              disabled={systemLoading.state.loading || statePage.isEdit}
              helperText={formFields.errors.target?.message}
            />
          )}

          {/* EQUIPAMENTO */}
          {formFields.values.targetType?.[0]?.slug === 'equipments' && (
            <TagInput.ContentApi
              name="target"
              value={formFields.values.target.map(formatInputValue)}
              onChange={(values) => {
                const item = formatOutputValues(values)?.[0]
                formFields.setValues({ target: [item] })
              }}
              featchData={(p) =>
                equipmentsApiV4(http).get({
                  ...p,
                  ...params,
                  order: 'desc',
                  sort: 'id'
                })
              }
              optionDisabled={formFields.values.target.map((e) =>
                e.id.toString()
              )}
              placeholder={languageTab.form.target.input.placeholderEquipment}
              disabled={systemLoading.state.loading || statePage.isEdit}
              helperText={formFields.errors.target?.message}
            />
          )}

          {/* ALARMES RULES */}
          {formFields.values.targetType?.[0]?.slug === 'template' && (
            <TagInput.ContentApi
              name="target"
              value={formFields.values.target.map(formatInputValue)}
              onChange={(values) => {
                const item = formatOutputValues(values)?.[0]
                formFields.setValues({ target: [item] })
              }}
              featchData={() =>
                alarmsTargetsApiV4(http).get({
                  order: 'asc',
                  sort: 'name',
                  status: 1,
                  dynamicEntities: 1
                })
              }
              optionDisabled={formFields.values.target.map((e) =>
                e.id.toString()
              )}
              placeholder={languageTab.form.target.input.placeholderTemplate}
              disabled={systemLoading.state.loading || statePage.isEdit}
              helperText={formFields.errors.target?.message}
            />
          )}
        </TagInput.Root>
      </div>

      {!statePage.isEdit && (
        <div className="footer-form">
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {statePage.isEdit
              ? languageTab.form.btn.save
              : languageTab.form.btn.continue}
          </Button>
        </div>
      )}
    </form>
  )
}

const useFormFields = () => {
  const requiredField = 'CAMPO OBRIGATÓRIO'

  const formSchema = z.object({
    dataEntityId: z.number().nullish(),
    targetType: z
      .array(z.object({ id: z.number(), name: z.string(), slug: z.string() }))
      .min(1, { message: requiredField }),
    target: z
      .array(z.object({ id: z.number(), name: z.string() }))
      .min(1, { message: requiredField })
  })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (data: Partial<FormSchema>): FormSchema => ({
    targetType: data?.targetType ?? [],
    target: data?.target ?? [],
    dataEntityId: data.dataEntityId ?? null
  })

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })
  const defineValues = (dataParsed: Partial<FormSchema>) => {
    Object.entries(dataParsed).forEach(([key, value]) => {
      setValue(key as keyof FormSchema, value)
    })
  }
  const reset = () => {
    defineValues(parseInitialData({}))
  }
  const setValues = (data: Partial<FormSchema> = {}) => {
    defineValues(data)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors, reset }
}
