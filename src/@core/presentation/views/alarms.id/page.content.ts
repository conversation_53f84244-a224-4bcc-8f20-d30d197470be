import { IStateData } from './page.types'

export const stateData: IStateData = {
  tab: {
    active: 'data'
  },
  tabData: {
    alarm: {}
  },
  tabRules: {
    dataEntityId: 0,
    target: [],
    targetType: {
      id: 0,
      name: '',
      slug: ''
    }
  },
  tabMonitoring: {
    monitoring: {}
  },
  tabNotification: {
    table: {
      items: [],
      lastPage: 0
    }
  },
  tabHistorical: {
    table: {
      items: [],
      lastPage: 0
    }
  }
}
