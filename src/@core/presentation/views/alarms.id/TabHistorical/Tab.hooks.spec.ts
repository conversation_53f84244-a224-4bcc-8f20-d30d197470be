import { renderHook, waitFor } from '@testing-library/react'

import {
  alarmTriggeredMock1,
  alarmTriggeredMock2
} from '@/__mock__/content/api-alarm-triggered.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStateAlarmesIdPage } from '../page.hooks'
import { useLanguageTabHistory, useMethodTabHistorical } from './Tab.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsTriggeredApiV4')
const spyAlarmsTriggeredApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsTriggeredApiV4'),
  'alarmsTriggeredApiV4'
)

describe('@/@core/presentation/views/alarms.id/TabHistorical/Tab.hooks.ts', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function useMethodTabHistorical', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabHistorical(),
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyAlarmsTriggeredApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request success **/
    spyAlarmsTriggeredApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmTriggeredMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(() => {
      result.current.method.fetchData()
    })

    const { table } = result.current.state.tabHistorical

    expect(table.items).toHaveLength(1)
    expect(table.lastPage).toBe(1)

    /* request CREATE error **/
    spyAlarmsTriggeredApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        response: {
          data: {
            name: ['campo obrigatório']
          }
        }
      })
    }))

    let res
    await waitFor(async () => {
      res = await result.current.method.recognize(alarmTriggeredMock1.id)
    })
    expect(res).toBeFalsy()

    /* request CREATE success **/
    spyAlarmsTriggeredApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmTriggeredMock1
      })
    }))

    let response
    await waitFor(async () => {
      response = await result.current.method.recognize(alarmTriggeredMock1.id)
    })
    expect(response).toBeTruthy()

    spyAlarmsTriggeredApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmTriggeredMock2
      })
    }))

    let responseId
    await waitFor(async () => {
      responseId = await result.current.method.recognize(4)
    })
    expect(responseId).toBeTruthy()
  })
})

describe('@/@core/presentation/views/alarms.id/TabHistorical/Tab.hooks | useLanguageTabMonitoring', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabHistory()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.form.input).toEqual({
      equipment: 'Equipamento',
      status: 'Status'
    })
  })
})
