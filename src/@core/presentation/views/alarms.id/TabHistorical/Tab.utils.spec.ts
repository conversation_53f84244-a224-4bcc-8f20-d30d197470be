import { IAlarmTriggered } from '@/@core/domain/AlarmTriggered'
import { alarmStatusMap } from '@/content/mapAlarmStatus'
import { getAlarmStatus, parseDataInput } from './Tab.utils'

describe('parseDataInput', () => {
  it('should parse IAlarmTriggered to IAlarmHistoricalPage with all fields', () => {
    const input: IAlarmTriggered = {
      id: 1,
      alarmId: 2,
      alarmName: 'Test Alarm',
      companyId: 3,
      companyName: 'Test Company',
      equipmentId: 4,
      equipmentName: 'Test Equipment',
      triggeredAt: '2024-01-01T00:00:00Z',
      triggeredValues: 'val1',
      normalizedAt: '2024-01-01T01:00:00Z',
      normalizedValues: 'val2',
      status: 'alarm',
      actionSupport: {
        deviceStatusAfterTrigger: 10,
        deviceStatusAfterNormalize: 20
      }
    }
    const result = parseDataInput(input)
    expect(result).toEqual({
      id: 1,
      alarmName: 'Test Alarm',
      triggeredAt: '2024-01-01T00:00:00Z',
      normalizedAt: '2024-01-01T01:00:00Z',
      status: 'alarm',
      actionSupport: {
        deviceStatusAfterTrigger: 10,
        deviceStatusAfterNormalize: 20
      },
      companyName: 'Test Company',
      equipmentName: 'Test Equipment'
    })
  })

  it('should use default values for nullable fields', () => {
    const input: IAlarmTriggered = {
      id: 2,
      alarmId: null,
      alarmName: null,
      companyId: 3,
      companyName: 'Company',
      equipmentId: null,
      equipmentName: null,
      triggeredAt: '2024-01-02T00:00:00Z',
      triggeredValues: '',
      normalizedAt: '2024-01-02T01:00:00Z',
      normalizedValues: '',
      status: 'normalized',
      actionSupport: {
        deviceStatusAfterTrigger: null,
        deviceStatusAfterNormalize: null
      }
    }
    const result = parseDataInput(input)
    expect(result).toEqual({
      id: 2,
      alarmName: '',
      triggeredAt: '2024-01-02T00:00:00Z',
      normalizedAt: '2024-01-02T01:00:00Z',
      status: 'normalized',
      actionSupport: {
        deviceStatusAfterTrigger: 0,
        deviceStatusAfterNormalize: 0
      },
      companyName: 'Company',
      equipmentName: ''
    })
  })
})

describe('getAlarmStatus', () => {
  it('should return the correct status object when status exists', () => {
    expect(getAlarmStatus('alarm')).toEqual(alarmStatusMap['alarm'])
    expect(getAlarmStatus('pre_alarm')).toEqual(alarmStatusMap['pre_alarm'])
    expect(getAlarmStatus('normalized')).toEqual(alarmStatusMap['normalized'])
  })

  it('should return the default status object when status does not exist', () => {
    expect(getAlarmStatus('unknown_status')).toEqual(alarmStatusMap['default'])
    expect(getAlarmStatus('')).toEqual(alarmStatusMap['default'])
  })
})
