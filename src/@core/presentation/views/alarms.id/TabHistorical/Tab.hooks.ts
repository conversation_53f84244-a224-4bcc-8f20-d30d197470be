import { http } from '@/@core/infra/http'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsTriggeredApiV4 } from '@/@core/infra/api/AlarmsTriggeredApiV4'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

import { useStateAlarmesIdPage } from '../page.hooks'
import { parseDataInput } from './Tab.utils'

export const useMethodTabHistorical = () => {
  const log = useLog()

  const statePage = useStateAlarmesIdPage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const languageTabHistory = useLanguageTabHistory()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const search = memory.local.get().alarms.record.tabHistorical

      const result = await alarmsTriggeredApiV4(http).get({
        ...search,
        equipmentId: search.equipmentId?.[0]?.id ?? undefined,
        alarmId: Number(statePage.tabData.alarm.id)
      })

      statePage.setTabHistorical({
        table: {
          items: result.data.items.map(parseDataInput),
          lastPage: result.data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/TabHistorical/useMethodTabHistorical/fetchData'
      })
      statePage.setTabHistorical({
        table: {
          items: [],
          lastPage: 0
        }
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }
  const recognize = async (id: number) => {
    let success = false

    try {
      systemLoading.setData({ loading: true })

      const { data } = await alarmsTriggeredApiV4(http).create(id)

      statePage.setTabHistorical({
        table: {
          items: statePage.tabHistorical.table.items.map((item) =>
            item.id === data.id ? parseDataInput(data) : item
          ),
          lastPage: statePage.tabHistorical.table.lastPage
        }
      })

      systemToast.addToast({
        message: languageTabHistory.form.formMessages.createSuccessMessage,
        type: 'success'
      })

      success = true
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/TabHistorical/useMethodTabHistorical/recognize'
      })

      systemToast.addToast({
        message: languageTabHistory.form.formMessages.createErrorMessage,
        type: 'error'
      })
      success = false
    } finally {
      systemLoading.setData({ loading: false })
    }

    return success
  }

  return { fetchData, recognize }
}

export const useLanguageTabHistory = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages } = languageByMode(lang)

  const { form, title, table, modalNormalizeAlarm } =
    pages.alarmsId.tabHistorical

  return {
    title,
    form: {
      input: {
        status: form.input.status,
        equipment: form.input.equipment
      },
      formMessages: form.formMessages
    },
    table: {
      columns: {
        status: table.columns.status,
        company: table.columns.company,
        equipment: table.columns.equipment,
        triggered: table.columns.triggered,
        normalized: table.columns.normalized,
        actions: table.columns.actions
      }
    },
    modalNormalizeAlarm
  }
}
