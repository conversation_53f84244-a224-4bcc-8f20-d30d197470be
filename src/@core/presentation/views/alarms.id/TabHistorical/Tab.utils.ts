import { IAlarmTriggered } from '@/@core/domain/AlarmTriggered'
import { alarmStatusMap } from '@/content/mapAlarmStatus'
import { IAlarmHistoricalPage } from './Tab.types'

export const parseDataInput = (data: IAlarmTriggered): IAlarmHistoricalPage => {
  return {
    id: data.id,
    alarmName: data.alarmName ?? '',
    triggeredAt: data.triggeredAt,
    normalizedAt: data.normalizedAt,
    status: data.status,
    actionSupport: {
      deviceStatusAfterTrigger:
        data.actionSupport?.deviceStatusAfterTrigger ?? 0,
      deviceStatusAfterNormalize:
        data.actionSupport?.deviceStatusAfterNormalize ?? 0
    },
    companyName: data.companyName,
    equipmentName: data.equipmentName ?? ''
  }
}

export const getAlarmStatus = (status: string) => {
  return alarmStatusMap[status] ?? alarmStatusMap['default']
}
