import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { PageSection } from '@/@core/presentation/shared/pages'
import { useEffect, useRef } from 'react'
import { useStateAlarmesIdPage } from '../page.hooks'
import { useLanguageTabHistory, useMethodTabHistorical } from './Tab.hooks'
import { SectionHistorical } from './_components/SectionHistorical'

export const TabHistory = () => {
  const isMounted = useRef<boolean>(false)
  const languageTab = useLanguageTabHistory()

  const system = useSystemStore()

  const statePage = useStateAlarmesIdPage()
  const methodTabHistorical = useMethodTabHistorical()

  useEffect(() => {
    if (
      !isMounted.current &&
      !system.state.mountComponent?.['alarms.id-TabHistorical']
    ) {
      const handler = () => {
        if (!statePage.isEdit) return

        methodTabHistorical.fetchData()
      }
      handler()

      system.setMountComponent('alarms.id-TabHistorical')
    }

    return () => {
      isMounted.current = true
    }
  }, [])

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2">
            <PageSection.Content title={languageTab.title} />
          </div>
        </div>
      </PageSection.Root>
      <SectionHistorical />
    </>
  )
}
