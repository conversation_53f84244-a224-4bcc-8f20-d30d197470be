import dayjs from 'dayjs'
import { FC, useState } from 'react'

import { IAlarmTriggeredSort } from '@/@core/domain/AlarmTriggered'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import {
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { useStateAlarmesIdPage } from '@/@core/presentation/views/alarms.id/page.hooks'
import { mergeSortOrderData } from '@/@core/utils/handleSorteColumn'
import { useDebounceFunction } from '@/hooks/useDebouce'

import { useLanguageTabHistory, useMethodTabHistorical } from '../Tab.hooks'
import { IAlarmHistoricalPage } from '../Tab.types'
import { getAlarmStatus } from '../Tab.utils'

export const SectionHistorical = () => {
  const statePage = useStateAlarmesIdPage()
  const systemLoading = useSystemLoadingStore()

  const languageTab = useLanguageTabHistory()
  const methodTabHistorical = useMethodTabHistorical()

  const searchFields = memory.local.get().alarms.record.tabHistorical

  const handleSortColumn = async (props: { key: IAlarmTriggeredSort }) => {
    const { sort, order } = mergeSortOrderData(props.key, searchFields)

    memory.local.set({
      alarms: { record: { tabHistorical: { order, sort } } }
    })
    await methodTabHistorical.fetchData()
  }
  const handleInput = useDebounceFunction(() => {
    methodTabHistorical.fetchData()
  }, 250)

  return (
    <>
      <Table.Root>
        <Table.Info>
          <Table.InfoTitle>{languageTab.title}</Table.InfoTitle>

          <TagInput.Root className="ml-auto">
            <TagInput.Content
              placeholder={languageTab.form.input.status}
              className="min-w-[150px]"
              name="status"
              value={String(searchFields?.status ?? '')}
              onChange={(items) => {
                memory.local.set({
                  alarms: {
                    record: {
                      tabHistorical: { status: items?.[0]?.value ?? '' }
                    }
                  }
                })
                handleInput()
              }}
              options={[
                { value: 'alarm', label: 'Alarme' },
                { value: '', label: 'Todos' },
                { value: 'normalized', label: 'Normalizado' },
                { value: 'recognized', label: 'Reconhecido' }
              ]}
              disabled={systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root className="min-w-[250px]">
            <TagInput.ContentApi
              placeholder={languageTab.form.input.equipment}
              name="equipment"
              defaultValue={formatInputValues(searchFields?.equipmentId ?? [])}
              onChange={(values) => {
                memory.local.set({
                  alarms: {
                    record: {
                      tabHistorical: { equipmentId: formatOutputValues(values) }
                    }
                  }
                })
                handleInput()
              }}
              featchData={(p) => equipmentsApiV4(http).get({ ...p })}
              disabled={systemLoading.state.loading}
              isClearable
            />
          </TagInput.Root>
        </Table.Info>

        <Table.Header>
          <Table.Row>
            <Table.Head
              onClick={() =>
                handleSortColumn({
                  key: 'status'
                })
              }
            >
              {languageTab.table.columns.status}
              <Table.CellIcon
                field="status"
                sort={searchFields.status}
                order={searchFields.order}
              />
            </Table.Head>
            <Table.Head>{languageTab.table.columns.company}</Table.Head>
            <Table.Head>{languageTab.table.columns.equipment}</Table.Head>
            <Table.Head
              onClick={() =>
                handleSortColumn({
                  key: 'triggered_at'
                })
              }
            >
              {languageTab.table.columns.triggered}
              <Table.CellIcon
                field="triggered_at"
                sort={searchFields.status}
                order={searchFields.order}
              />
            </Table.Head>
            <Table.Head
              onClick={() =>
                handleSortColumn({
                  key: 'normalized_at'
                })
              }
            >
              {languageTab.table.columns.normalized}
              <Table.CellIcon
                field="normalized_at"
                sort={searchFields.status}
                order={searchFields.order}
              />
            </Table.Head>
            <Table.Head>{languageTab.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {!systemLoading.state.loading &&
            statePage.tabHistorical.table.items.map((item) => (
              <Table.Row key={item?.id}>
                <Table.Cell>
                  <Badge.Root
                    className={cn(
                      'cell-device-status',
                      getAlarmStatus(item.status).css
                    )}
                  >
                    <Badge.Content className="m-auto">
                      {getAlarmStatus(item.status).label}
                    </Badge.Content>
                  </Badge.Root>
                </Table.Cell>
                <Table.Cell>{item.companyName}</Table.Cell>
                <Table.Cell>{item.equipmentName}</Table.Cell>
                <Table.Cell>
                  {item.triggeredAt
                    ? dayjs(item.triggeredAt).format('DD/MM/YYYY hh:mm:ss')
                    : ''}
                </Table.Cell>
                <Table.Cell>
                  {item.normalizedAt
                    ? dayjs(item.normalizedAt).format('DD/MM/YYYY hh:mm:ss')
                    : ''}
                </Table.Cell>
                <Table.Cell width={80} role="td-actions">
                  <Actions triggered={item} />
                </Table.Cell>
              </Table.Row>
            ))}

          <Table.RowLoading status={systemLoading.state.loading} colSpan={2} />
        </Table.Body>
        <Table.Mobile>
          {!systemLoading.state.loading &&
            statePage.tabHistorical.table.items.map((item) => (
              <TableMobile.Item key={item.id}>
                <TableMobile.Head />
                <TableMobile.Row>
                  <TableMobile.Cell>
                    {languageTab.table.columns.status}
                  </TableMobile.Cell>
                  <TableMobile.Cell>
                    <Badge.Root
                      className={cn(
                        'cell-device-status',
                        getAlarmStatus(item.status).css
                      )}
                    >
                      <Badge.Content className="m-auto">
                        {getAlarmStatus(item.status).label}
                      </Badge.Content>
                    </Badge.Root>
                  </TableMobile.Cell>
                </TableMobile.Row>
                <TableMobile.Row>
                  <TableMobile.Cell>
                    {languageTab.table.columns.company}
                  </TableMobile.Cell>
                  <TableMobile.Cell>{item.companyName}</TableMobile.Cell>
                </TableMobile.Row>
                <TableMobile.Row>
                  <TableMobile.Cell>
                    {languageTab.table.columns.equipment}
                  </TableMobile.Cell>
                  <TableMobile.Cell>{item.equipmentName}</TableMobile.Cell>
                </TableMobile.Row>
                <TableMobile.Row>
                  <TableMobile.Cell>
                    {languageTab.table.columns.triggered}
                  </TableMobile.Cell>
                  <TableMobile.Cell>{item.triggeredAt}</TableMobile.Cell>
                </TableMobile.Row>
                <TableMobile.Row>
                  <TableMobile.Cell>
                    {languageTab.table.columns.normalized}
                  </TableMobile.Cell>
                  <TableMobile.Cell>{item.normalizedAt}</TableMobile.Cell>
                </TableMobile.Row>
                <TableMobile.Footer className="flex justify-end">
                  <Actions triggered={item} />
                </TableMobile.Footer>
              </TableMobile.Item>
            ))}
        </Table.Mobile>
        <Table.Paginate
          status={systemLoading.state.loading}
          lastPage={statePage.tabHistorical.table.lastPage}
          currentPage={searchFields.page!}
          handleChangePage={(page) => {
            memory.local.set({
              alarms: {
                record: { tabHistorical: { page } }
              }
            })
            methodTabHistorical.fetchData()
          }}
        />
      </Table.Root>
    </>
  )
}

const Actions: FC<{ triggered: IAlarmHistoricalPage }> = ({ triggered }) => {
  if (triggered.status !== 'alarm') return null
  return (
    <div className="table-td-actions">
      <ModalNormalizeAlarm triggeredId={triggered.id} />
    </div>
  )
}

interface ModalNormalizeAlarmProps {
  triggeredId: number
}

const ModalNormalizeAlarm: FC<ModalNormalizeAlarmProps> = ({ triggeredId }) => {
  const [openDialog, setOpenDialog] = useState(false)

  const methodTabHistorical = useMethodTabHistorical()
  const languageTab = useLanguageTabHistory()

  const handleClick = async () => {
    const result = await methodTabHistorical.recognize(triggeredId)
    if (result) setOpenDialog(false)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-10 text-center text-[19px]">
        <Icon
          icon="fileCheck02"
          className="icon-menu-primary"
          height="18"
          width="18"
          viewBox="0 0 20 20"
        />
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>{languageTab.modalNormalizeAlarm.title}</Dialog.Title>
        </Dialog.Header>

        <Dialog.Description>
          {languageTab.modalNormalizeAlarm.textInfo}
        </Dialog.Description>

        <Dialog.Footer>
          <Button type="button" onClick={() => setOpenDialog(false)}>
            {languageTab.modalNormalizeAlarm.buttonCancel}
          </Button>

          <Button type="button" variant="error-primary" onClick={handleClick}>
            {languageTab.modalNormalizeAlarm.buttonConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
