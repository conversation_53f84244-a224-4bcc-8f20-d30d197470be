export type IAlarmData = {
  id: number | null
  name: string
  description: string
  status: boolean
  account: { id: number; name: string } | null
  categoryId: number
  timeConfirmation: number | null
  initialHour: string
  finalHour: string
  daysWeek: number[]
  daysRetention: number | null
}

export type IFormData = {
  id: number | null
  name: string
  description: string
  status: boolean
  account: { id: number; name: string } | null
  categoryId: string
}

export type IFormDataSend = {
  id: number | null
  name: string
  description: string
  timeConfirmation: number
  initialHour: string | null
  finalHour: string | null
  daysWeek: number[]
  daysRetention: number | null
  status: boolean
  accountId?: number
  categoryId?: number
}
