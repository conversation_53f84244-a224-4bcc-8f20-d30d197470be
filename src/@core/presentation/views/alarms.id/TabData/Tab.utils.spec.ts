import { alarmMock1 } from '@/__mock__/content/api-alarms.content'

import IAlarm from '@/@core/domain/Alarm'
import { IAlarmData, IFormData, IFormDataSend } from './Tab.types'
import { formDataOutput, parseAlarmData } from './Tab.utils'

describe('@core/presentation/views/alarms.id/TabData/Tab.utils', () => {
  const alarmData: IAlarmData = { ...alarmMock1 }

  it('should check return the function formDataOutput', () => {
    const dataInput: IFormData = {
      id: 56,
      name: 'Fator potência horário',
      description: '',
      status: true,
      account: { id: 9, name: 'Desenvolvimento' },
      categoryId: '3'
    }
    const dataOutput: IFormDataSend = {
      id: 56,
      name: 'Fator potência horário',
      description: '',
      status: true,
      accountId: 9,
      categoryId: 3,
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysRetention: 1,
      daysWeek: [0, 1, 2, 3, 4, 5, 6]
    }
    expect(formDataOutput({ ...alarmMock1 }, dataInput)).toEqual(dataOutput)
  })

  it('should check return the function parseAlarmData', () => {
    const dataInput: IAlarm = { ...alarmMock1 }

    const dataOutput: IAlarmData = {
      id: 56,
      name: 'Fator potência horário',
      description: '',
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysWeek: [0, 1, 2, 3, 4, 5, 6],
      daysRetention: 1,
      status: true,
      account: { id: 9, name: 'Desenvolvimento' },
      categoryId: 3
    }

    const result = parseAlarmData(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataOutput', () => {
    const formValues: IFormData = {
      id: 56,
      name: 'Fator potência horário',
      description: '',
      status: true,
      account: { id: 9, name: 'Desenvolvimento' },
      categoryId: '3'
    }
    const dataOutput: IFormDataSend = {
      id: 56,
      timeConfirmation: 0,
      initialHour: '00:00',
      finalHour: '23:59',
      daysWeek: [0, 1, 2, 3, 4, 5, 6],
      daysRetention: 1,
      name: 'Fator potência horário',
      description: '',
      status: true,
      accountId: 9,
      categoryId: 3
    }

    expect(formDataOutput(alarmData, formValues)).toEqual(dataOutput)
  })
})
