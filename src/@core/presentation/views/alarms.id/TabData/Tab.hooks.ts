import { useRouter } from 'next/router'

import { alarmsApiV4 } from '@/@core/infra/api/AlarmsApiV4'
import { http } from '@/@core/infra/http'

import { languageByMode } from '@/@core/language'
import loggerRequest from '@/@core/logging/loggerRequest'
import { toastMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { useLog } from '@/@core/logging/logger'
import { useStateAlarmesIdPage } from '../page.hooks'

import { IFormData } from './Tab.types'

import { parseAlarmMonitoringData } from '../TabMonitoring/Tab.utils'
import {
  formDataCreateOutput,
  formDataOutput,
  parseAlarmData
} from './Tab.utils'

export const useMethodTabData = () => {
  const router = useRouter()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const statePage = useStateAlarmesIdPage()

  const languageTab = useLanguageTabData()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const { status, data } = await alarmsApiV4(http).getById(
        Number(router.query?.id)
      )

      if (status === 204) {
        return
      }

      statePage.set({
        tabData: { alarm: parseAlarmData(data) },
        tabMonitoring: { monitoring: parseAlarmMonitoringData(data) }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabData/useMethodTabData/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }
  const onSubmit = async (values: IFormData) => {
    if (!statePage.isEdit) {
      statePage.set({
        tabData: {
          alarm: formDataCreateOutput(values)
        }
      })
      statePage.setTab('rules')
      return
    }

    try {
      systemLoading.setData({ pageLoading: true })

      const payload = formDataOutput(statePage.tabData.alarm, values)

      const { status, data } = await alarmsApiV4(http).update(
        Number(payload.id),
        payload
      )

      const conditionalRequest = status === 200

      systemToast.addToast({
        message: toastMessageSwitch(
          languageTab.form.message,
          payload.id,
          conditionalRequest
        ),
        type: toastTypeSwitch(conditionalRequest)
      })

      if (!conditionalRequest) return

      statePage.set({
        tabData: { alarm: parseAlarmData(data) },
        tabMonitoring: { monitoring: parseAlarmMonitoringData(data) }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabData/useMethodTabData/onSubmit'
      })
      systemToast.addToast({
        message: languageTab.form.message.updateErrorMessage,
        type: 'error'
      })
    } finally {
      systemLoading.setData({ pageLoading: false })
    }
  }

  return { fetchData, onSubmit }
}

export const useLanguageTabData = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const {
    tabData: { title, subtitle, form }
  } = pages.alarmsId

  return {
    title,
    subtitle,
    form: {
      input: {
        status: form.input.status,
        name: form.input.name,
        description: form.input.description,
        category: form.input.category,
        account: form.input.account
      },
      message: form.messages,
      requiredField,
      btn: { cancel, save, confirm, add, clean, continue: btn.continue }
    }
  }
}
