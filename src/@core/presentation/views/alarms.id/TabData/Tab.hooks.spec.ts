import { act, renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { useStateAlarmesIdPage } from '../page.hooks'
import { parseAlarmMonitoringData } from '../TabMonitoring/Tab.utils'
import { useLanguageTabData, useMethodTabData } from './Tab.hooks'
import { parseAlarmData } from './Tab.utils'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4')
const spyAlarmsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4'),
  'alarmsApiV4'
)

describe('src/@core/presentation/views/alarms.id/TabData/Tab.hooks | useMethodTabData', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  const alarmData = parseAlarmData(alarmMock1)
  const monitoringData = parseAlarmMonitoringData(alarmMock1)

  const payload = {
    id: 56,
    name: 'Fator potência horário',
    description: '',
    status: true,
    account: { id: 9, name: 'Desenvolvimento' },
    categoryId: '3'
  }

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyAlarmsApiV4.mockImplementation(() => ({
      getById: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request successful without data **/
    spyAlarmsApiV4.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request success **/
    spyAlarmsApiV4.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmMock1
      })
    }))
    await waitFor(() => {
      result.current.method.fetchData()
    })

    expect(result.current.state.tabData.alarm.id).toBe(alarmMock1.id)
    expect(result.current.state.tabData.alarm.name).toBe(alarmMock1.name)
  })

  it('should check return the function onSubmit when creating', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodTabData(),
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => {
      result.current.state.reset()
    })

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts).toHaveLength(0)
    expect(result.current.state.tabData.alarm).toEqual({
      id: null,
      name: 'Fator potência horário',
      description: '',
      status: true,
      account: { id: 9, name: 'Desenvolvimento' },
      categoryId: 3,
      timeConfirmation: null,
      initialHour: '',
      finalHour: '',
      daysWeek: [],
      daysRetention: null
    })
  })

  it('should check return the function onSubmit when editing', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodTabData(),
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request not authenticated **/
    await act(async () => {
      result.current.toast.reset()
      result.current.state.reset()
      result.current.state.set({ tabData: { alarm: { ...alarmData } } })
    })

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 403,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar alarme'
    )

    /* request error **/
    await act(async () => {
      result.current.toast.reset()
      result.current.state.reset()
      result.current.state.set({ tabData: { alarm: { ...alarmData } } })
    })

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar alarme'
    )

    /* request success **/
    await act(async () => {
      result.current.toast.reset()
      result.current.state.reset()
      result.current.state.set({ tabData: { alarm: { ...alarmData } } })
    })

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: { ...alarmMock1 }
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({ ...payload })
    })

    expect(result.current.state.tabData.alarm).toEqual(alarmData)
    expect(result.current.state.tabMonitoring.monitoring).toEqual(
      monitoringData
    )
  })
})

describe('src/@core/presentation/views/alarms.id/TabData/Tab.hooks | useLanguageTabData', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabData()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.form.input).toEqual({
      account: 'Conta',
      category: 'Categoria',
      description: 'Descrição',
      name: 'Nome',
      status: 'Status'
    })
  })
})
