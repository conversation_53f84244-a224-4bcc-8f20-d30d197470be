import { useRouter } from 'next/router'
import { useEffect, useRef } from 'react'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { PageSection } from '@/@core/presentation/shared/pages'

import checkQueryIdIsNumber from '@/@core/utils/checkIsNumber'

import { SectionData } from './_components/SectionData'
import { useLanguageTabData, useMethodTabData } from './Tab.hooks'

export const TabData = () => {
  const isMounted = useRef<boolean>(false)

  const router = useRouter()

  const system = useSystemStore()

  const methodTabData = useMethodTabData()
  const languagePage = useLanguageTabData()

  useEffect(() => {
    if (
      !isMounted.current &&
      !system.state.mountComponent?.['alarms.id-TabData']
    ) {
      checkQueryIdIsNumber(router.query.id) && methodTabData.fetchData()

      system.setMountComponent('alarms.id-TabData')
    }

    return () => {
      isMounted.current = true
    }
  }, [])

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2 space-y-[8px]">
            <PageSection.Content title={languagePage.title} />
            <p className="font-acuminPro-Regular text-[14px] leading-[20px] dark:text-comerc-vibra-grayLightMode-300">
              {languagePage.subtitle}
            </p>
          </div>
        </div>
      </PageSection.Root>
      <SectionData />
    </>
  )
}
