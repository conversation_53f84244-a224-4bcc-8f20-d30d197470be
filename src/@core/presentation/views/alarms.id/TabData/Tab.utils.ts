import IAlarm from '@/@core/domain/Alarm'
import { IAlarmData, IFormData, IFormDataSend } from './Tab.types'

export const parseAlarmData = (data: IAlarm): IAlarmData => {
  return {
    id: data.id,
    name: data.name,
    description: data.description,
    status: data.status,
    account: data.account,
    categoryId: data.category.id,
    daysRetention: data.daysRetention,
    timeConfirmation: data.timeConfirmation,
    initialHour: data.initialHour,
    finalHour: data.finalHour,
    daysWeek: data.daysWeek
  }
}

export const formDataOutput = (
  alarmData: Partial<IAlarmData>,
  formValues: IFormData
): IFormDataSend => {
  return {
    /** alarmData */
    id: alarmData.id!,
    timeConfirmation: alarmData.timeConfirmation!,
    initialHour: alarmData.initialHour!,
    finalHour: alarmData.finalHour!,
    daysWeek: alarmData.daysWeek!.map(Number),
    daysRetention: alarmData.daysRetention!,
    /** formValues */
    name: formValues.name,
    description: formValues.description,
    status: formValues.status,
    accountId: formValues.account?.id,
    categoryId: +formValues.categoryId
  }
}

export const formDataCreateOutput = (
  formValues: Partial<IFormData>
): Partial<IAlarmData> => {
  return {
    /** alarmData */
    id: null,
    timeConfirmation: null,
    initialHour: '',
    finalHour: '',
    daysWeek: [],
    daysRetention: null,
    /** formValues */
    name: formValues?.name,
    description: formValues?.description,
    status: formValues?.status,
    categoryId: Number(formValues?.categoryId),
    account: formValues?.account
  }
}
