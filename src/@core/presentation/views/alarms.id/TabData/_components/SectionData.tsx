import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/router'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { accountsApiV3 } from '@/@core/infra/api/AccountsApiV3'
import { http } from '@/@core/infra/http'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import {
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import { useStateAlarmesIdPage } from '@/@core/presentation/views/alarms.id/page.hooks'
import {
  useLanguageTabData,
  useMethodTabData
} from '@/@core/presentation/views/alarms.id/TabData/Tab.hooks'

import { IAlarmData } from '../Tab.types'

const CONST_CATEGORY_USER_ID = '3'

export const SectionData = () => {
  const router = useRouter()

  const authStore = useAuthStore()
  const systemLoading = useSystemLoadingStore()

  const statePage = useStateAlarmesIdPage()
  const languageTab = useLanguageTabData()
  const methodTabData = useMethodTabData()

  const formFields = useFormFields()

  useEffect(() => {
    formFields.setValues({
      ...statePage.tabData.alarm,
      account: authStore.state.isSuperAdmin
        ? statePage.tabData.alarm.account
        : authStore.state.me.account
    })
  }, [statePage.tabData.alarm])

  const showAccountInput = useMemo(() => {
    return (
      authStore.state.isSuperAdmin &&
      formFields.values.categoryId === CONST_CATEGORY_USER_ID
    )
  }, [authStore.state.isSuperAdmin, formFields.values.categoryId])

  const categoryOptions = useMemo(() => {
    return authStore.state.isSuperAdmin
      ? [
          { value: '2', label: 'Operação' },
          { value: '3', label: 'Usuário' }
        ]
      : [
          { value: '3', label: 'Usuário' }
          /** */
        ]
  }, [authStore.state.isSuperAdmin])

  return (
    <form
      className="form-container"
      onSubmit={formFields.handleSubmit(() =>
        methodTabData.onSubmit(formFields.values)
      )}
    >
      <div className="mb-2">
        <Switch.Content
          label={languageTab.form.input.status}
          labelPosition="end"
          checked={!!formFields.values.status}
          onChange={(value) => {
            formFields.setValue('status', value)
          }}
          disabled={systemLoading.state.loading}
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
        <Input.Root>
          <Input.Label htmlFor="name">
            {languageTab.form.input.name}
          </Input.Label>
          <Input.Content
            id="name"
            value={formFields.values.name}
            onChange={(e) => {
              formFields.setValue('name', e.target.value)
            }}
            helperText={formFields.errors.name?.message}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="description">
            {languageTab.form.input.description}
          </Input.Label>
          <Input.Content
            id="description"
            value={formFields.values.description}
            onChange={(e) => {
              formFields.setValue('description', e.target.value)
            }}
            helperText={formFields.errors.description?.message}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <TagInput.Root>
          <TagInput.Label htmlFor="category">
            {languageTab.form.input.category}
          </TagInput.Label>
          <TagInput.Content
            name="category"
            value={formFields.values.categoryId}
            disabled={statePage.isEdit ?? systemLoading.state.loading}
            onChange={(items) => {
              formFields.setValue('categoryId', items?.[0]?.value ?? '')

              if (authStore.state.isSuperAdmin)
                formFields.setValue('account', null)
            }}
            options={categoryOptions}
            helperText={formFields.errors.categoryId?.message}
          />
        </TagInput.Root>

        {showAccountInput && (
          <TagInput.Root>
            <TagInput.Label htmlFor="account">
              {languageTab.form.input.account}
            </TagInput.Label>
            <TagInput.ContentApi
              name="account"
              value={
                formFields.values.account
                  ? formatInputValues([formFields.values.account])
                  : []
              }
              onChange={(values) => {
                formFields.setValue('account', formatOutputValues(values)?.[0])
              }}
              featchData={(p) =>
                accountsApiV3(http).get({ ...p, sort: 'name' })
              }
              helperText={formFields.errors.account?.message}
              disabled={statePage.isEdit ?? systemLoading.state.loading}
            />
          </TagInput.Root>
        )}
      </div>

      <div className="footer-form">
        <Button
          type="button"
          onClick={() => router.push('/alarms')}
          disabled={systemLoading.state.loading}
        >
          {languageTab.form.btn.cancel}
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={systemLoading.state.loading}
        >
          {statePage.isEdit
            ? languageTab.form.btn.save
            : languageTab.form.btn.continue}
        </Button>
      </div>
    </form>
  )
}

const useFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabData()

  const formSchema = z
    .object({
      id: z.union([z.number(), z.null()]),
      name: z.string().min(1, { message }),
      description: z.string(),
      status: z.boolean(),
      account: z.object({ id: z.number(), name: z.string() }).nullable(),
      categoryId: z.string()
    })
    .superRefine((values, ctx) => {
      if (!values.name || values.name.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: ['name']
        })
      }
      if (!values.categoryId || values.categoryId.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: ['categoryId']
        })
      }
      if (values.categoryId === CONST_CATEGORY_USER_ID && !values.account?.id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: ['account']
        })
      }
    })

  type IFormSchema = z.infer<typeof formSchema>

  const parseInitialData = (data: Partial<IAlarmData>): IFormSchema => {
    return {
      id: data.id ?? null,
      name: data.name ?? '',
      description: data.description ?? '',
      status: data.status ?? false,
      account: data.account ?? null,
      categoryId: data.categoryId?.toString() ?? ''
    }
  }

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<IFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const defineValues = (dataParsed: Partial<IFormSchema>) =>
    Object.entries(dataParsed).forEach(([key, value]) => {
      setValue(key as keyof IFormSchema, value)
    })

  const setValues = (data: Partial<IAlarmData> = {}) => {
    defineValues(parseInitialData(data))
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors }
}
