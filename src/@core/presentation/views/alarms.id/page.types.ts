import { IAlarmData } from './TabData/Tab.types'
import { IAlarmHistoricalPage } from './TabHistorical/Tab.types'
import { IAlarmMonitoringData } from './TabMonitoring/Tab.types'
import { INotification } from './TabNotification/Tab.types'
import { IRuleData } from './TabRules/Tab.types'

export interface IStateData {
  tab: {
    active: string
  }
  tabData: {
    alarm: Partial<IAlarmData>
  }
  tabRules: {
    target: IRuleData['target']
    targetType: IRuleData['targetType']
    dataEntityId: number
  }
  tabMonitoring: {
    monitoring: Partial<IAlarmMonitoringData>
  }
  tabNotification: {
    table: {
      items: INotification[]
      lastPage: number
    }
  }
  tabHistorical: {
    table: {
      items: IAlarmHistoricalPage[]
      lastPage: number
    }
  }
}
export interface IState extends IStateData {
  set: (p: Partial<IStateData>) => void
  reset: () => void
  setTab: (p: Partial<IStateData['tab']['active']>) => void
  setTabNotification: (p: IStateData['tabNotification']) => void
  setTabHistorical: (p: IStateData['tabHistorical']) => void
}
