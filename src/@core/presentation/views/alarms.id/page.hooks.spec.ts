import '@/__mock__/logging/logger'

import { act, renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { useStateSection } from '@/@core/presentation/shared/AlarmRuleStage/Section.hook'
import { parseConditionalData } from '@/@core/presentation/shared/AlarmRuleStage/Section.utils'
import {
  alarmsConditionalsMock1,
  alarmsConditionalsMock2
} from '@/__mock__/content/api-alarms-conditionals.content'
import {
  alarmsNotificationsMock1,
  alarmsNotificationsMock2
} from '@/__mock__/content/api-alarms-notifications.content'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  useLanguageAlarmesIdPage,
  useMethodsAlarmesIdPage,
  useStateAlarmesIdPage
} from './page.hooks'
import { parseAlarmData } from './TabData/Tab.utils'
import { parseAlarmMonitoringData } from './TabMonitoring/Tab.utils'
import { parseNotificationData } from './TabNotification/Tab.utils'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsApiV4')
const spyAlarmsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsApiV4'),
  'alarmsApiV4'
)
jest.mock('@/@core/infra/api/AlarmsMultiTargetsApiV4')
const spyAlarmsMultiTargetsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsMultiTargetsApiV4'),
  'alarmsMultiTargetsApiV4'
)
jest.mock('@/@core/infra/api/AlarmsConditionalsApiV4')
const spyAlarmsConditionalsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsConditionalsApiV4'),
  'alarmsConditionalsApiV4'
)
jest.mock('@/@core/infra/api/AlarmsNotificationsApiV4')
const spyAlarmsNotificationsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsNotificationsApiV4'),
  'alarmsNotificationsApiV4'
)

describe('@core/presentation/views/alarms.id/page.hooks | usePageLanguage', () => {
  it('check de page title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.title).toBe('Alarme')
  })
})

describe('@core/presentation/views/alarms.id/page.hooks | useStatePage', () => {
  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        tabData: {
          alarm: parseAlarmData(alarmMock1)
        }
      })
    })
  })

  it('should exec method setTab', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => {
      result.current.state.setTab('data')
    })

    expect(result.current.state.tab.active).toBe('data')

    await waitFor(() => {
      result.current.state.setTab('rules')
    })

    expect(result.current.state.tab.active).toBe('rules')

    await waitFor(() => result.current.state.reset())
  })
})

describe('@core/presentation/views/alarms.id/page.hooks | useMethodsAlarmesIdPage', () => {
  beforeEach(() => {
    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('check de page title', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        state: useStateAlarmesIdPage(),
        methods: useMethodsAlarmesIdPage(),
        section: useStateSection()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request CREATE error **/
    await act(async () => {
      result.current.toast.reset()
      result.current.state.reset()
    })
    spyAlarmsApiV4.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue(() => ({
        status: 500,
        data: null
      }))
    }))
    await act(async () => {
      result.current.methods.createNewAlarm()
    })
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao criar alarme'
    )

    /* request CREATE with error payload **/
    await act(async () => {
      result.current.toast.reset()
      result.current.state.reset()
    })
    spyAlarmsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue(() => ({
        status: 204,
        data: null
      }))
    }))
    await act(async () => {
      result.current.methods.createNewAlarm()
    })
    expect(result.current.toast.state.toasts[0].type).toBe('error')
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao criar alarme'
    )

    /* request CREATE success **/
    await act(async () => {
      const tabData = {
        alarm: parseAlarmData(alarmMock1)
      }
      const tabRules = {
        target: { id: 1, name: 'Template' },
        targetType: { id: 2, name: 'Consumo água', slug: '' },
        dataEntityId: 3
      }
      const tabMonitoring = {
        monitoring: parseAlarmMonitoringData(alarmMock1)
      }
      const tabNotification = {
        table: {
          items: [alarmsNotificationsMock1, alarmsNotificationsMock2].map(
            parseNotificationData
          ),
          lastPage: 1
        }
      }
      result.current.toast.reset()
      result.current.state.set({
        tabData,
        tabRules,
        tabMonitoring,
        tabNotification
      })
      result.current.section.set({
        itemsConditionalTriggering: [
          alarmsConditionalsMock1,
          alarmsConditionalsMock2
        ].map(parseConditionalData),
        itemsConditionalNormalization: [
          alarmsConditionalsMock1,
          alarmsConditionalsMock2
        ].map(parseConditionalData)
      })
    })
    spyAlarmsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 201,
        data: { ...alarmMock1 }
      })
    }))
    spyAlarmsMultiTargetsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue(() => ({}))
    }))
    spyAlarmsConditionalsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue(() => ({}))
    }))
    spyAlarmsNotificationsApiV4.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue(() => ({}))
    }))

    await act(async () => {
      result.current.methods.createNewAlarm()
    })
  })
})
