import { renderHook, waitFor } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { useLanguagePage, useMethodPage, useStatePage } from './page.hooks'

jest.mock('@/@core/infra/api/AccountsApiV3')

const spyAccountsApiV3 = jest.spyOn(
  require('@/@core/infra/api/AccountsApiV3'),
  'accountsApiV3'
)

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

describe('src/@core/presentation/views/accounts/page | useLanguagePage', () => {
  it('check de page title', () => {
    const { result } = renderHook(() => useLanguagePage(), {
      wrapper: AppStoreProvider
    })
    expect(result.current.page.title).toBe('Contas')
  })
})

describe('src/@core/presentation/views/accounts/page | useStatePage', () => {
  it('should exec method set', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        items: [
          {
            id: 1,
            name: 'name 1'
          }
        ],
        total: 1,
        lastPage: 1
      })
    })

    expect(result.current.state.items).toHaveLength(1)
    expect(result.current.state.total).toBe(1)
    expect(result.current.state.lastPage).toBe(1)
  })

  it('should exec method reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)
  })
})

describe('src/@core/presentation/views/accounts/page | useMethodPage', () => {
  it('should exec method getData', async () => {
    spyUseRouter.mockImplementation(() => ({
      pathname: '/accounts',
      query: ''
    }))

    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData with error **/
    spyAccountsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData successful without data **/
    spyAccountsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData with success **/
    spyAccountsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [{ id: 1, name: 'name 1' }],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(1)
    expect(result.current.state.total).toBe(1)
    expect(result.current.state.lastPage).toBe(1)
  })

  it('should exec method handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request with error **/
    spyAccountsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(() => {
      result.current.method.handleDelete(9, { pincode: '1234' })
    })

    /* request successful **/
    spyAccountsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))
    await waitFor(() => {
      result.current.method.handleDelete(9, { pincode: '1234' })
    })
  })
})
