import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/router'
import { FC, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { handleKeyEnter } from '@/@core/utils/handleInputSearch'
import { mergeSortOrderData } from '@/@core/utils/handleSorteColumn'
import { useDebounceFunction } from '@/hooks/useDebouce'

import { useLanguagePage, useMethodPage, useStatePage } from '../page.hooks'
import { IAccountPage } from '../page.types'

export const AccountsTable = () => {
  const router = useRouter()

  const statePage = useStatePage()
  const methodPage = useMethodPage()
  const languagePage = useLanguagePage()

  const permissions = useSystemStore().state.permissions
  const systemLoading = useSystemLoadingStore()
  const systemLoadingStore = useSystemLoadingStore()

  const searchFields = memory.local.get().accounts.listing.search

  const handleSortColumn = async (props: { key: 'id' | 'name' }) => {
    const { sort, order } = mergeSortOrderData(props.key, searchFields)

    memory.local.set({
      accounts: {
        listing: { search: { order, sort } }
      }
    })
    await methodPage.getData()
  }
  const handleInput = useDebounceFunction(() => {
    methodPage.getData()
  }, 250)

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languagePage.page.title}</Table.InfoTitle>

        <Table.InfoBadge className="lg:mr-auto">
          {statePage.total}
          <span className="hidden md:inline-block md:ml-1">
            {languagePage.table.totalRegisters}
          </span>
        </Table.InfoBadge>

        <TagInput.Root className="min-w-[150px]">
          <TagInput.Content
            placeholder="Status"
            value={
              searchFields.statusIds.length
                ? String(searchFields.statusIds?.[0])
                : ''
            }
            onChange={(items) => {
              memory.local.set({
                accounts: {
                  listing: {
                    search: { statusIds: [Number(items?.[0]?.value)] }
                  }
                }
              })
              handleInput()
            }}
            options={[
              { value: '1', label: 'Ativo' },
              { value: '4', label: 'Desativado' }
            ]}
            disabled={systemLoadingStore.state.loading}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Content
            slotStart={
              <Icon
                icon="searchLg"
                className="icon-menu-primary"
                height="24"
                width="24"
                viewBox="0 0 20 20"
              />
            }
            placeholder={languagePage.table.search.fieldQuery}
            type="text"
            defaultValue={searchFields?.q ?? ''}
            onChange={(e) => {
              memory.local.set({
                accounts: {
                  listing: { search: { q: e.target.value, page: 1 } }
                }
              })
            }}
            onKeyUp={(e) => handleKeyEnter(e.key, methodPage.getData)}
          />
        </Input.Root>

        <Table.InfoNewRegister
          onClick={() => router.push('/accounts/new')}
          permission={permissions.accounts?.create}
        />
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'id' })}
          >
            ID
            <Table.CellIcon
              field="id"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'name' })}
          >
            {languagePage.table.columns.name}
            <Table.CellIcon
              field="name"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head></Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.items.map((account) => (
          <Table.Row key={account.id}>
            <Table.Cell>{account.id}</Table.Cell>
            <Table.Cell>{account.name}</Table.Cell>
            <Table.Cell width={80}>
              <Actions account={account} />
            </Table.Cell>
          </Table.Row>
        ))}
        <Table.RowLoading status={systemLoading.state.loading} colSpan={2} />
      </Table.Body>

      <Table.Mobile>
        {statePage.items.map((account) => (
          <TableMobile.Item key={account.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.name}
              </TableMobile.Cell>
              <TableMobile.Cell>{account.name}</TableMobile.Cell>
            </TableMobile.Row>

            <TableMobile.Footer>
              <Actions account={account} />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        currentPage={searchFields.page}
        lastPage={statePage.lastPage}
        handleChangePage={(page) => {
          memory.local.set({
            accounts: {
              listing: { search: { page } }
            }
          })
          methodPage.getData()
        }}
      />
    </Table.Root>
  )
}

const Actions: FC<{ account: IAccountPage }> = ({ account }) => {
  const router = useRouter()

  const permissions = useSystemStore().state.permissions

  return (
    <div className="table-td-actions">
      {permissions?.accounts?.delete && (
        <ModalDelete accountId={account.id} accountName={account.name} />
      )}
      {permissions.accounts?.create && (
        <button
          className="table-td-action hover:cursor-pointer"
          onClick={() => router.push(`/accounts/${account.id}`)}
        >
          <Icon
            icon="edit"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      )}
    </div>
  )
}

interface ModalDeleteProps {
  accountId: number
  accountName: string
}

const ModalDelete: FC<ModalDeleteProps> = ({ accountId, accountName }) => {
  const [openDialog, setOpenDialog] = useState(false)
  const languagePage = useLanguagePage()
  const methodPage = useMethodPage()

  const formField = useFields()

  const handleClickConfirm = async () => {
    const result = await methodPage.handleDelete(accountId, {
      pincode: formField.values.pincode
    })
    if (!result) return

    await methodPage.getData()
    setOpenDialog(false)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]">
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>{languagePage.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>

        <Dialog.Description>
          <p>
            {languagePage.table.modalDelete.textInfo}{' '}
            <span className="text-red-600">{accountName}</span>
          </p>
          <p>{languagePage.table.modalDelete.subTextInfo}</p>
          <p className="mt-3">
            {languagePage.table.modalDelete.deletionConfirmationText}
          </p>
        </Dialog.Description>

        <form onSubmit={formField.handleSubmit(handleClickConfirm)} id="form">
          <Input.Root>
            <Input.Content
              type="password"
              value={formField.values.pincode}
              onChange={({ target }) =>
                formField.setValue('pincode', target.value.replace(/\D/g, ''))
              }
              helperText={formField.errors.pincode?.message}
              maxLength={4}
            />
          </Input.Root>
        </form>

        <Dialog.Footer>
          <Button type="button" onClick={() => setOpenDialog(false)}>
            {languagePage.table.modalDelete.cancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languagePage.table.modalDelete.confirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}

const useFields = () => {
  const { requiredField } = useLanguagePage().table.modalDelete

  const formSchema = z.object({
    pincode: z.string().min(4, requiredField)
  })

  type FormSchema = z.infer<typeof formSchema>

  const defaultValues: FormSchema = {
    pincode: ''
  }

  const {
    handleSubmit,
    setValue,
    formState: { errors },
    watch
  } = useForm<FormSchema>({
    defaultValues,
    resolver: zodResolver(formSchema)
  })

  const values = watch()

  return { handleSubmit, setValue, values, errors }
}
