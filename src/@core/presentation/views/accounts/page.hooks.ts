import { AxiosError } from 'axios'
import { useCallback } from 'react'
import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { accountsApiV3 } from '@/@core/infra/api/AccountsApiV3'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { toastRequestMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import { stateData } from './page.content'
import { IState } from './page.types'
import { parseAccountData } from './page.utils'

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => set((state) => ({ ...state, ...initialData })),
  reset: () => set((state) => ({ ...state, ...stateData }))
}))

export const useStatePage = () => {
  const state = statePage()

  return { ...state }
}

export const useMethodPage = () => {
  const statePage = useStatePage()
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const log = useLog()

  const getData = useCallback(async () => {
    try {
      systemLoading.setData({ loading: true })

      statePage.reset()

      const { search } = memory.local.get().accounts.listing

      const result = await accountsApiV3(http).get({
        ...search,
        management: 1
      })

      if (result.status === 204) {
        systemLoading.setData({ loading: false })
        return
      }

      statePage.set({
        items: result.data.items.map(parseAccountData),
        lastPage: result.data.lastPage,
        total: result.data.total
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: `Erro no getData conta - @core/presentation/views/accounts/page.hooks.ts`
      })
    } finally {
      systemLoading.setData({ loading: false })
    }
  }, [])

  const handleDelete = async (
    accountId: number,
    payload: { pincode: string }
  ): Promise<boolean> => {
    const requestMessage = languagePage.table.modalDelete.messages

    try {
      systemLoading.setData({ loading: true })

      const { status } = await accountsApiV3(http).delete(accountId, {
        pincode: Number(payload.pincode)
      })

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(requestMessage, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return true
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'accounts/hooks/useMethodPage/handleDelete'
      })

      const { response } = error as AxiosError
      const responseMessage = (response?.data as string[])?.[0]

      systemToast.addToast({
        message:
          responseMessage ??
          languagePage.table.modalDelete.messages.errorMessage,
        type: 'error'
      })

      return false
    } finally {
      systemLoading.setData({ loading: false })
    }
  }

  return { getData, handleDelete }
}

export const useLanguagePage = () => {
  const { lang } = useSystemLanguageStore().state

  const { pages, table, btn, validationFields } = languageByMode(lang)

  return {
    page: {
      ...pages.accounts
    },
    table: {
      ...pages.accounts.table,
      ...table,
      modalDelete: {
        ...pages.accounts.table.modalDelete,
        cancel: btn.cancel,
        confirm: btn.confirm,
        continue: btn.continue,
        requiredField: validationFields.requiredField
      }
    }
  }
}
