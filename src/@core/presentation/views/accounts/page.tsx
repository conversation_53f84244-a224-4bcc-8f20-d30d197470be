import React, { useRef } from 'react'

import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'

import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'
import { AccountsTable } from './components/Table'
import { useLanguagePage, useMethodPage, useStatePage } from './page.hooks'

export const Page = () => {
  const isMounted = useRef<boolean>(false)

  const statePage = useStatePage()
  const languagePage = useLanguagePage()
  const methodPage = useMethodPage()
  useBreadcrumb('accounts')

  const permissions = useSystemStore().state.permissions

  useTitlePage(languagePage.page.title)

  React.useEffect(() => {
    if (!isMounted.current && permissions.accounts?.list) methodPage.getData()

    return () => {
      statePage.reset()
      isMounted.current = true
    }
  }, [])

  return (
    <PageContent>
      <Breadcrumbs />
      <AccountsTable />
    </PageContent>
  )
}