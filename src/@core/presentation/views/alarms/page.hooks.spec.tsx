import { renderHook, waitFor } from '@testing-library/react'
import { act } from 'react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import {
  useLanguageAlarmsPage,
  useMethodAlarmsPage,
  useStateAlarmsPage
} from './page.hooks'

jest.mock('@/@core/infra/api/AlarmsApiV4')

const spyAlarmsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsApiV4'),
  'alarmsApiV4'
)

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

describe('src/@core/presentation/views/alarms/page.hooks | useLanguageAlarmsPage', () => {
  it('check de page title', () => {
    const { result } = renderHook(() => useLanguageAlarmsPage(), {
      wrapper: AppStoreProvider
    })
    expect(result.current.page.title).toBe('Alarmes')
  })
})

describe('src/@core/presentation/views/alarms/page.hooks | useStateAlarmsPage', () => {
  it('should exec method set', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmsPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        items: [
          {
            id: 1,
            name: 'name 1',
            status: true,
            daysRetention: 0,
            daysWeek: [],
            initialHour: null,
            finalHour: null,
            description: '',
            timeConfirmation: 0
          }
        ],
        total: 1,
        lastPage: 1
      })
    })

    expect(result.current.state.items).toHaveLength(1)
    expect(result.current.state.total).toBe(1)
    expect(result.current.state.lastPage).toBe(1)
  })

  it('should exec method reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmsPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)
  })
})

describe('src/@core/presentation/views/alarms/page.hooks | useMethodAlarmsPage', () => {
  it('should exec method getData', async () => {
    spyUseRouter.mockImplementation(() => ({
      pathname: '/alarms',
      query: ''
    }))

    const { result } = renderHook(
      () => ({
        state: useStateAlarmsPage(),
        method: useMethodAlarmsPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData with error **/
    spyAlarmsApiV4.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)
    expect(result.current.state.total).toBe(0)
    expect(result.current.state.lastPage).toBe(0)

    /* getData successful without data **/
    spyAlarmsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(0)

    /* getData with success **/
    spyAlarmsApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmMock1],
          total: 1,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))
    await waitFor(() => {
      result.current.method.getData()
    })

    expect(result.current.state.items).toHaveLength(1)
    expect(result.current.state.total).toBe(1)
    expect(result.current.state.lastPage).toBe(1)
  })

  it('should exec method handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodAlarmsPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )
    /* request with error **/
    spyAlarmsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(() => {
      result.current.method.handleDelete(1)
    })

    /* request successful **/
    spyAlarmsApiV4.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))

    await waitFor(() => {
      result.current.method.handleDelete(1)
    })
  })

  it('should exec method handleUpdate', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmsPage(),
        method: useMethodAlarmsPage(),
        toast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const mockPayload = {
      id: 1,
      name: 'Updated Alarm',
      status: true,
      daysRetention: 0,
      daysWeek: [],
      initialHour: null,
      finalHour: null,
      description: '',
      timeConfirmation: 0
    }

    await act(async () => result.current.state.reset())
    await act(async () => result.current.toast.reset())

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        data: { id: 1, name: 'Updated Alarm' }
      })
    }))

    await act(async () =>
      result.current.state.set({
        items: [
          {
            id: 1,
            name: 'Old Alarm',
            status: true,
            daysRetention: 0,
            daysWeek: [],
            initialHour: null,
            finalHour: null,
            description: '',
            timeConfirmation: 0
          },
          {
            id: 2,
            name: 'Another Alarm',
            status: true,
            daysRetention: 0,
            daysWeek: [],
            initialHour: null,
            finalHour: null,
            description: '',
            timeConfirmation: 0
          }
        ]
      })
    )

    await act(async () => {
      await result.current.method.handleUpdate(mockPayload)
    })

    expect(result.current.state.items).toHaveLength(2)
    expect(result.current.state.items[0]).toEqual({
      id: 1,
      name: 'Updated Alarm',
      status: true,
      daysRetention: 0,
      daysWeek: [],
      initialHour: null,
      finalHour: null,
      description: '',
      timeConfirmation: 0
    })
    expect(result.current.state.items[1]).toEqual({
      id: 2,
      name: 'Another Alarm',
      status: true,
      daysRetention: 0,
      daysWeek: [],
      initialHour: null,
      finalHour: null,
      description: '',
      timeConfirmation: 0
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Alarme atualizado com sucesso'
    )
  })

  it('should handle error in handleUpdate', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmsPage(),
        method: useMethodAlarmsPage(),
        toast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const mockPayload = {
      id: 1,
      name: 'Updated Alarm',
      status: true,
      daysRetention: 0,
      daysWeek: [],
      initialHour: null,
      finalHour: null,
      description: '',
      timeConfirmation: 0
    }

    await act(async () => result.current.state.reset())
    await act(async () => result.current.toast.reset())

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue(new Error('Update failed'))
    }))

    await act(async () => {
      result.current.state.set({
        items: [
          {
            id: 1,
            name: 'Old Alarm',
            status: true,
            daysRetention: 0,
            daysWeek: [],
            initialHour: null,
            finalHour: null,
            description: '',
            timeConfirmation: 0
          }
        ]
      })
    })

    await act(async () => {
      await result.current.method.handleUpdate(mockPayload)
    })

    expect(result.current.state.items[0]).toEqual({
      id: 1,
      name: 'Old Alarm',
      status: true,
      daysRetention: 0,
      daysWeek: [],
      initialHour: null,
      finalHour: null,
      description: '',
      timeConfirmation: 0
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar alarme'
    )
  })
})
