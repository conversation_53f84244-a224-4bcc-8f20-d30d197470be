import { AxiosError } from 'axios'
import { useCallback } from 'react'
import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { alarmsApiV4 } from '@/@core/infra/api/AlarmsApiV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

import { stateData } from './page.content'
import { IAlarmPage, IState } from './page.types'
import { parseAlarmData } from './page.utils'

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => set((state) => ({ ...state, ...initialData })),
  reset: () => set((state) => ({ ...state, ...stateData }))
}))

export const useStateAlarmsPage = () => {
  const state = statePage()

  return { ...state }
}

export const useMethodAlarmsPage = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const statePage = useStateAlarmsPage()
  const languagePage = useLanguageAlarmsPage()
  const log = useLog()

  const getData = useCallback(async () => {
    try {
      systemLoading.setData({ loading: true })

      statePage.reset()

      const { search } = memory.local.get().alarms.listing

      const result = await alarmsApiV4(http).get({
        ...search
      })

      if (result.status === 204) {
        return
      }

      statePage.set({
        items: result.data.items.map(parseAlarmData),
        lastPage: result.data.lastPage,
        total: result.data.total
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: `Erro no getData alarmes - @core/presentation/views/alarms/page.hooks.ts`
      })
    } finally {
      systemLoading.setData({ loading: false })
    }
  }, [])

  const handleDelete = async (id: number) => {
    let success = false
    try {
      await alarmsApiV4(http).delete(id)

      systemToast.addToast({
        message: languagePage.table.modalDelete.deleteSuccessMessage
      })

      statePage.set({
        items: statePage.items.filter((item) => item.id !== id)
      })

      success = true
    } catch (error) {
      const { response } = error as AxiosError
      const responseMessage = (response?.data as string[])?.[0]

      systemToast.addToast({
        message:
          responseMessage ?? languagePage.table.modalDelete.deleteErrorMessage,
        type: 'error'
      })
      success = false
    }
    return success
  }

  const handleUpdate = async (payload: IAlarmPage) => {
    const currentId = payload.id

    try {
      systemLoading.setData({ pageLoading: true })

      const { data } = await alarmsApiV4(http).update(currentId, payload)
      statePage.set({
        items: statePage.items.map((item) =>
          item.id === currentId ? { ...item, ...data } : item
        )
      })

      systemToast.addToast({
        message: languagePage.table.updateSuccessMessage
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'accounts.id/tabData/hooks/onSubmit'
      })
      systemToast.addToast({
        message: languagePage.table.updateErrorMessage
      })
    } finally {
      systemLoading.setData({ pageLoading: false })
    }
  }

  return { getData, handleDelete, handleUpdate }
}

export const useLanguageAlarmsPage = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages, table } = languageByMode(lang)

  return {
    page: {
      ...pages.alarms
    },
    table: {
      ...pages.alarms.table,
      ...table
    }
  }
}
