import React, { useRef } from 'react'

import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'
import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'

import { AlarmsTable } from './components/Table'
import {
  useLanguageAlarmsPage,
  useMethodAlarmsPage,
  useStateAlarmsPage
} from './page.hooks'

export const Page = () => {
  const isMounted = useRef<boolean>(false)

  const statePage = useStateAlarmsPage()
  const languagePage = useLanguageAlarmsPage()
  const methodPage = useMethodAlarmsPage()

  const permissions = useSystemStore().state.permissions

  useTitlePage(languagePage.page.title)
  useBreadcrumb('alarms')

  React.useEffect(() => {
    if (!isMounted.current && permissions.accounts?.list) methodPage.getData()

    return () => {
      statePage.reset()
      isMounted.current = true
    }
  }, [])

  return (
    <PageContent>
      <Breadcrumbs />

      <AlarmsTable />
    </PageContent>
  )
}
