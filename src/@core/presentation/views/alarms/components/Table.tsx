import { useRouter } from 'next/router'
import React, { FC } from 'react'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { memory } from '@/@core/infra/memory'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { handleKeyEnter } from '@/@core/utils/handleInputSearch'
import { mergeSortOrderData } from '@/@core/utils/handleSorteColumn'
import { useDebounceFunction } from '@/hooks/useDebouce'

import {
  useLanguageAlarmsPage,
  useMethodAlarmsPage,
  useStateAlarmsPage
} from '../page.hooks'
import { IAlarmPage } from '../page.types'

export const AlarmsTable = () => {
  const router = useRouter()

  const statePage = useStateAlarmsPage()
  const methodPage = useMethodAlarmsPage()
  const languagePage = useLanguageAlarmsPage()

  const permissions = useSystemStore().state.permissions
  const systemLoading = useSystemLoadingStore()

  const searchFields = memory.local.get().alarms.listing.search

  const handleSortColumn = async (props: { key: 'id' | 'name' }) => {
    const { sort, order } = mergeSortOrderData(props.key, searchFields)

    memory.local.set({
      alarms: {
        listing: { search: { order, sort } }
      }
    })

    await methodPage.getData()
  }

  const handleInput = useDebounceFunction(() => {
    methodPage.getData()
  }, 250)

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languagePage.page.title}</Table.InfoTitle>

        <Table.InfoBadge className="sm:mr-auto">
          {statePage.total}
          <span className="hidden md:inline-block md:ml-1">
            {languagePage.table.totalRegisters}
          </span>
        </Table.InfoBadge>

        <TagInput.Root className="min-w-[150px]">
          <TagInput.Content
            name="status"
            value={String(searchFields?.status ?? '')}
            onChange={(items) => {
              const status = items?.[0]?.value
                ? Number(items?.[0]?.value)
                : undefined

              memory.local.set({
                alarms: {
                  listing: { search: { status } }
                }
              })
              handleInput()
            }}
            options={[
              { value: '1', label: 'Ativo' },
              { value: '0', label: 'Desativado' }
            ]}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Content
            slotStart={
              <Icon
                icon="searchLg"
                className="icon-menu-primary"
                height="24"
                width="24"
                viewBox="0 0 20 20"
              />
            }
            type="text"
            defaultValue={searchFields?.q ?? ''}
            placeholder={languagePage.table.search.fieldQuery}
            onChange={(e) => {
              memory.local.set({
                alarms: {
                  listing: { search: { q: e.target.value, page: 1 } }
                }
              })
            }}
            onKeyUp={(e) => handleKeyEnter(e.key, methodPage.getData)}
          />
        </Input.Root>

        <Table.InfoNewRegister
          onClick={() => router.push('/alarms/new')}
          permission={permissions.alarms?.create}
        />
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head
            // className="w-full"
            onClick={() =>
              handleSortColumn({
                key: 'name'
              })
            }
          >
            {languagePage.table.columns.name}
            <Table.CellIcon
              field="name"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head className="w-20" />
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.items.map((alarm) => (
          <Table.Row key={alarm.id}>
            <Table.Cell role="td-name">{alarm.name}</Table.Cell>
            <Table.Cell width={80} role="td-actions">
              <Actions alarm={alarm} />
            </Table.Cell>
          </Table.Row>
        ))}

        <Table.RowLoading status={systemLoading.state.loading} colSpan={2} />
      </Table.Body>

      <Table.Mobile>
        {statePage.items.map((alarm) => (
          <TableMobile.Item key={alarm.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>
                {languagePage.table.columns.name}
              </TableMobile.Cell>
              <TableMobile.Cell>{alarm.name}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Footer>
              <Actions alarm={alarm} />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={statePage.lastPage}
        currentPage={searchFields.page}
        handleChangePage={(page) => {
          memory.local.set({
            alarms: {
              listing: { search: { page } }
            }
          })
          methodPage.getData()
        }}
      />
    </Table.Root>
  )
}

const Actions: FC<{ alarm: IAlarmPage }> = ({ alarm }) => {
  // const router = useRouter()
  const permissions = useSystemStore().state.permissions
  const systemLoading = useSystemLoadingStore()

  const methodPage = useMethodAlarmsPage()
  const router = useRouter()

  return (
    <div className="table-td-actions">
      {permissions?.['alarms']?.delete && <ModalDelete alarm={alarm} />}
      {permissions?.alarms?.create && (
        <Switch.Content
          disabled={systemLoading.state.loading}
          checked={alarm.status}
          onChange={(value) => {
            methodPage.handleUpdate({ ...alarm, status: value })
          }}
        />
      )}

      <button
        className="table-td-action hover:cursor-pointer"
        onClick={() => router.push(`/alarms/${alarm.id}`)}
      >
        <Icon
          icon="edit"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>
    </div>
  )
}

interface ModalDeleteProps {
  alarm: IAlarmPage
}

const ModalDelete: FC<ModalDeleteProps> = ({ alarm }) => {
  const [openDialog, setOpenDialog] = React.useState(false)
  const languagePage = useLanguageAlarmsPage()
  const methodPage = useMethodAlarmsPage()

  const handleClickConfirm = async () => {
    const result = await methodPage.handleDelete(alarm.id)
    if (result) setOpenDialog(false)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]">
        <Button.Icon>
          <Icon
            icon="trash01"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </Button.Icon>
      </Dialog.Trigger>
      <Dialog.Content className="max-w-2xl">
        <Dialog.Header>
          <Dialog.Title>{languagePage.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>
        <Dialog.Description hidden>
          {languagePage.table.modalDelete.title}
        </Dialog.Description>
        <p>
          {languagePage.table.modalDelete.textInfo}
          <span className="text-red-600"> {alarm.name}</span>
        </p>
        <Dialog.Footer>
          <Button
            type="button"
            variant="secondary-gray"
            onClick={() => setOpenDialog(false)}
          >
            {languagePage.table.modalDelete.textCancel}
          </Button>
          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languagePage.table.modalDelete.textConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
