import { act, renderHook } from '@testing-library/react'

import useListAlarmsTargetsTypes from '@/@core/framework/store/hook/useListAlarmsTargetsTypes'
import { memory, memoryApp } from '@/@core/infra/memory'
import { alarmsTargetsTypesMock1, alarmsTargetsTypesMock2 } from '@/__mock__/content/api-alarms-targets-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListAlarmsTargetsTypesService } from './listAlarmsTargetsTypesService'

jest.mock(
  '@/@core/infra/api/AlarmsTargetsTypesApiV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/AlarmsTargetsTypesApiV4'),
  'alarmsTargetsTypesApiV4'
)

describe('src/@core/services/listAlarmsTargetsTypesService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListAlarmsTargetsTypesService(),
        store: useListAlarmsTargetsTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsTargetsTypesMock1, alarmsTargetsTypesMock2]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().alarmsTargetsTypesList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().alarmsTargetsTypesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().alarmsTargetsTypesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
