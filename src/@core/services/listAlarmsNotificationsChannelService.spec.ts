import { act, renderHook } from '@testing-library/react'

import useListAlarmsNotificationsChannelsStore from '@/@core/framework/store/hook/useListAlarmsNotificationsChannel'
import { memory, memoryApp } from '@/@core/infra/memory'
import { alarmsNotificationsChannelsMock1 } from '@/__mock__/content/api-alarms-notifications-channels.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListAlarmsNotificationsChannelsService } from './listAlarmsNotificationsChannelService'

jest.mock(
  '@/@core/infra/api/ListAlarmsNotificationsChannelsV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/ListAlarmsNotificationsChannelsV4'),
  'listAlarmsNotificationsChannelsV4'
)

describe('src/@core/services/listAlarmsNotificationsChannelService', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListAlarmsNotificationsChannelsService(),
        store: useListAlarmsNotificationsChannelsStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsNotificationsChannelsMock1]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().alarmsNotificationsChannelsList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().alarmsNotificationsChannelsList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().alarmsNotificationsChannelsList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)
  })
})
