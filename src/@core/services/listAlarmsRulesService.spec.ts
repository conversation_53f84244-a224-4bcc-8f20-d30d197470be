import { act, renderHook } from '@testing-library/react'

import useListAlarmsRules from '@/@core/framework/store/hook/useListAlarmsRules'
import { memory, memoryApp } from '@/@core/infra/memory'
import { alarmsRulesMock1, alarmsRulesMock2 } from '@/__mock__/content/api-alarms-rules.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListAlarmsRulesService } from './listAlarmsRulesService'

jest.mock(
  '@/@core/infra/api/AlarmsRulesApiV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/AlarmsRulesApiV4'),
  'alarmsRulesApiV4'
)

describe('src/@core/services/listAlarmsRulesService', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListAlarmsRulesService(),
        store: useListAlarmsRules()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsRulesMock1, alarmsRulesMock2]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().alarmsRulesList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().alarmsRulesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().alarmsRulesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
