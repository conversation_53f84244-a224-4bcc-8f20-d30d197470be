import { act, renderHook } from '@testing-library/react'

import useListApportionmentMeasuresUnits from '@/@core/framework/store/hook/useListApportionmentMeasuresUnits'
import { memory, memoryApp } from '@/@core/infra/memory'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { apportionmentMeasuresUnitMock1, apportionmentMeasuresUnitMock2 } from '@/__mock__/content/api-apportionment-measures-units.content'
import { ListApportionmentMeasuresUnitsService } from './listApportionmentMeasuresUnitsService'

jest.mock(
  '@/@core/infra/api/ApportionmentMeasuresUnitsApiV3'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/ApportionmentMeasuresUnitsApiV3'),
  'apportionmentMeasuresUnitsApiV3'
)

describe('src/@core/services/listApportionmentMeasuresUnitsService', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListApportionmentMeasuresUnitsService(),
        store: useListApportionmentMeasuresUnits()
      }),
      { wrapper: AppStoreProvider }
    )

    let typeId: number = 1

    /** checks the initial data */
    expect(memory.local.get().apportionmentMeasuresUnitsList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)


    /** execut without data */
    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [{ ...apportionmentMeasuresUnitMock1, typeId }, { ...apportionmentMeasuresUnitMock2, typeId }]
      })
    }))

    await act(async () => {
      result.current.service.handler(typeId)
    })
    expect(memory.local.get().apportionmentMeasuresUnitsList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)


    /** execut with data*/
    await act(async () => {
      result.current.service.handler(typeId)
    })
    expect(memory.local.get().apportionmentMeasuresUnitsList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
