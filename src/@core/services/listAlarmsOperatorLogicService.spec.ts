import { act, renderHook } from '@testing-library/react'

import useListAlarmsOperatorLogic from '@/@core/framework/store/hook/useListAlarmsOperatorLogic'
import { memory, memoryApp } from '@/@core/infra/memory'
import { alarmsOperatorLogicMock1, alarmsOperatorLogicMock2 } from '@/__mock__/content/api-alarms-operator-logic.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListAlarmsOperatorLogicService } from './listAlarmsOperatorLogicService'

jest.mock(
  '@/@core/infra/api/AlarmsOperatorLogicApiV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/AlarmsOperatorLogicApiV4'),
  'alarmsOperatorLogicApiV4'
)

describe('src/@core/services/ListAlarmsOperatorLogicService', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListAlarmsOperatorLogicService(),
        store: useListAlarmsOperatorLogic()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsOperatorLogicMock1, alarmsOperatorLogicMock2]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().alarmsOperatorLogicList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().alarmsOperatorLogicList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })

    expect(memory.local.get().alarmsOperatorLogicList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
