import useListAlarmsTargets from '@/@core/framework/store/hook/useListAlarmsTargets'
import { alarmsTargetsApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'

export const ListAlarmsTargetsService = () => {
  const listAlarmsTargets = useListAlarmsTargets()

  const handler = async ({
    dynamicEntities,
    status
  }: {
    dynamicEntities?: number
    status?: number
  } = {}) => {
    const { list: oldList } = memory.local.get().alarmsTargetsList

    if (oldList.length) {
      listAlarmsTargets.set({ list: oldList })
      return
    }

    const {
      data: { items: newList }
    } = await alarmsTargetsApiV4(http).get({ dynamicEntities, status })

    memory.local.set({ alarmsTargetsList: { list: newList } })

    listAlarmsTargets.set({ list: newList })
  }

  return { handler }
}
