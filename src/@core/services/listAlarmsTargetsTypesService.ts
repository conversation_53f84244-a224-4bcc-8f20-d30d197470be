import useListAlarmsTargetsTypes from '@/@core/framework/store/hook/useListAlarmsTargetsTypes'
import { alarmsTargetsTypesApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'

export const ListAlarmsTargetsTypesService = () => {
  const listAlarmsTargetsTypes = useListAlarmsTargetsTypes()

  const handler = async () => {
    const { list: oldList } = memory.local.get().alarmsTargetsTypesList

    if (oldList.length) {
      listAlarmsTargetsTypes.set({ list: oldList })
      return
    }

    const {
      data: { items: newList }
    } = await alarmsTargetsTypesApiV4(http).get()

    memory.local.set({ alarmsTargetsTypesList: { list: newList } })

    listAlarmsTargetsTypes.set({ list: newList })
  }

  return { handler }
}
