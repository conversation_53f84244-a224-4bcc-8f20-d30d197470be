import { act, renderHook } from '@testing-library/react'

import useListDeviceModelStore from '@/@core/framework/store/hook/useListDeviceModelStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { deviceModelMock1, deviceModelMock2 } from '@/__mock__/content/api-device-model.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListDeviceModelService } from './listDeviceModelService'

jest.mock(
  '@/@core/infra/api/DeviceModelApiV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/DeviceModelApiV4'),
  'deviceModelApiV4'
)

describe('src/@core/services/ListDeviceModelService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListDeviceModelService(),
        store: useListDeviceModelStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        list: [deviceModelMock1, deviceModelMock2]
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().deviceModelList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().deviceModelList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().deviceModelList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })

})
