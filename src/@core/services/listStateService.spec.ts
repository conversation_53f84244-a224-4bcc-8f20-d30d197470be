import { act, renderHook } from '@testing-library/react'

import useListStateStore from '@/@core/framework/store/hook/useListStateStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { listStateMock1, listStateMock2 } from '@/__mock__/content/api-list-states.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListStateService } from './listStateService'

jest.mock('src/@/@core/infra/api/ListStatesApiV3')

const spyApi = jest.spyOn(
  require('@/@core/infra/api/ListStatesApiV3'),
  'listStatesApiV3'
)

describe('src/@core/services/listStateService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListStateService(),
        store: useListStateStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [listStateMock1, listStateMock2]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().statesList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().statesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().statesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
