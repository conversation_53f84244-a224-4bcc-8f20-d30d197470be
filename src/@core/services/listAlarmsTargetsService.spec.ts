import { act, renderHook } from '@testing-library/react'

import useListAlarmsTargets from '@/@core/framework/store/hook/useListAlarmsTargets'
import { memory, memoryApp } from '@/@core/infra/memory'
import { alarmsTargetsMock1, alarmsTargetsMock2 } from '@/__mock__/content/api-alarms-targets.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListAlarmsTargetsService } from './listAlarmsTargetsService'

jest.mock(
  '@/@core/infra/api/AlarmsTargetsApiV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/AlarmsTargetsApiV4'),
  'alarmsTargetsApiV4'
)

describe('src/@core/services/listAlarmsTargetsService', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListAlarmsTargetsService(),
        store: useListAlarmsTargets()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [alarmsTargetsMock1, alarmsTargetsMock2]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().alarmsTargetsList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().alarmsTargetsList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().alarmsTargetsList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
