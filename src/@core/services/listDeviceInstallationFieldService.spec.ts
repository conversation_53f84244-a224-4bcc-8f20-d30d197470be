import { act, renderHook } from '@testing-library/react'

import useListDeviceInstallationFieldStore from '@/@core/framework/store/hook/useListDeviceInstallationFieldStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { deviceInstallationMock1, deviceInstallationMock2 } from '@/__mock__/content/api-device-installation.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListDeviceInstallationFieldService } from './listDeviceInstallationFieldService'

jest.mock(
  '@/@core/infra/api/DeviceInstallationFieldsApiV4'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/DeviceInstallationFieldsApiV4'),
  'deviceInstallationFieldsApiV4'
)

describe('src/@core/services/listDeviceInstallationFieldService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListDeviceInstallationFieldService(),
        store: useListDeviceInstallationFieldStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        list: [deviceInstallationMock1, deviceInstallationMock2]
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().deviceInstallationFieldList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().deviceInstallationFieldList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().deviceInstallationFieldList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
