import { act, renderHook } from '@testing-library/react'

import useListSubtypeStore from '@/@core/framework/store/hook/useListSubtypeStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { subTypeMock1 } from '@/__mock__/content/api-sub-type.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListSubTypeService } from './listSubTypeService'

jest.mock('src/@/@core/infra/api/SubTypesApiV3')

const spyApi = jest.spyOn(
  require('@/@core/infra/api/SubTypesApiV3'),
  'subTypesApiV3'
)

describe('src/@core/services/listSubTypeService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListSubTypeService(),
        store: useListSubtypeStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        list: [subTypeMock1]
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().subTypesList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().subTypesList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().subTypesList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)
  })
})
