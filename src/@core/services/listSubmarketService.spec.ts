import { act, renderHook } from '@testing-library/react'

import useListSubmarketStore from '@/@core/framework/store/hook/useListSubmarketStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { submarketMock1 } from '@/__mock__/content/api-submarkets.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListSubmarketService } from './listSubmarketService'

jest.mock('src/@/@core/infra/api/SubmarketsApiV3')

const spyApi = jest.spyOn(
  require('@/@core/infra/api/SubmarketsApiV3'),
  'submarketsApiV3'
)

describe('src/@core/services/listSubmarketService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListSubmarketService(),
        store: useListSubmarketStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [submarketMock1]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().submarketList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().submarketList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().submarketList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)
  })
})
