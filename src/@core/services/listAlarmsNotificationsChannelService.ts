import useListAlarmsNotificationsChannelsStore from '@/@core/framework/store/hook/useListAlarmsNotificationsChannel'
import { listAlarmsNotificationsChannelsV4 } from '@/@core/infra/api/ListAlarmsNotificationsChannelsV4'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'

export const ListAlarmsNotificationsChannelsService = () => {
  const listAlarmsNotificationsChannelsStore =
    useListAlarmsNotificationsChannelsStore()

  const handler = async () => {
    const { list: oldList } = memory.local.get().alarmsNotificationsChannelsList

    if (oldList.length) {
      listAlarmsNotificationsChannelsStore.set({ list: oldList })
      return
    }

    const {
      data: { items: newList }
    } = await listAlarmsNotificationsChannelsV4(http).get()

    memory.local.set({ alarmsNotificationsChannelsList: { list: newList } })

    listAlarmsNotificationsChannelsStore.set({ list: newList })
  }

  return { handler }
}
