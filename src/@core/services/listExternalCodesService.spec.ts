import { act, renderHook } from '@testing-library/react'

import useListExternalCodesStore from '@/@core/framework/store/hook/useListExternalCodesStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { listExternalCodeMock1 } from '@/__mock__/content/api-list-external-code.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListExternalCodesService } from './listExternalCodesService'

jest.mock('src/@/@core/infra/api/ListExternalCodesApiV4')

const spyApi = jest.spyOn(
  require('@/@core/infra/api/ListExternalCodesApiV4'),
  'listExternalCodesApiV4'
)

describe('src/@core/services/listExternalCodesService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListExternalCodesService(),
        store: useListExternalCodesStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: listExternalCodeMock1
        }
      })
    }))

    const dataNull = { comerc: null, zordon: null }
    const dataEmpty = { comerc: '', zordon: '' }
    const dataFilled = { comerc: 'comerc', zordon: 'zordon' }

    /** checks the initial data */
    expect(memory.local.get().externalCodeList.list).toEqual(dataNull)
    expect(result.current.store.state.list).toEqual(dataEmpty)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().externalCodeList.list).toEqual(dataFilled)
    expect(result.current.store.state.list).toEqual(dataFilled)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().externalCodeList.list).toEqual(dataFilled)
    expect(result.current.store.state.list).toEqual(dataFilled)
  })
})
