
import { act, renderHook } from '@testing-library/react'

import useListCityStore from '@/@core/framework/store/hook/useListCityStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { listCItyMock1, listCItyMock2 } from '@/__mock__/content/api-list-cities.content'
import { ListCityService } from './listCityService'

jest.mock(
  '@/@core/infra/api'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api'),
  'listCitiesApiv3'
)

describe('src/@core/services/listCityService', () => {
  beforeEach(() => {
    memoryApp.init()
  })

  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListCityService(),
        store: useListCityStore()
      }),
      { wrapper: AppStoreProvider }
    )

    const stateId = 1

    spyApi.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: [listCItyMock1, listCItyMock2]
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().citiesList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler(stateId)
    })
    expect(memory.local.get().citiesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler(stateId)
    })
    expect(memory.local.get().citiesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
