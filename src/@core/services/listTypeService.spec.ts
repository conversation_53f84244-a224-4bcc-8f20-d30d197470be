import { act, renderHook } from '@testing-library/react'

import useListTypeStore from '@/@core/framework/store/hook/useListTypeStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { typeMock1 } from '@/__mock__/content/api-type.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListTypeService } from './listTypeService'

jest.mock('src/@/@core/infra/api/TypesApiV3')

const spyApi = jest.spyOn(
  require('@/@core/infra/api/TypesApiV3'),
  'typesApiV3'
)

describe('src/@core/services/listTypeService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListTypeService(),
        store: useListTypeStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [typeMock1]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().typeList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().typeList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().typeList.list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)
  })
})
