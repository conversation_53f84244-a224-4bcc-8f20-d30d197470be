import { act, renderHook } from '@testing-library/react'

import useListApportionmentsFeeDivisionTypes from '@/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes'
import { memory, memoryApp } from '@/@core/infra/memory'
import { apportionmentsFeeDivisionTypesMock1, apportionmentsFeeDivisionTypesMock2 } from '@/__mock__/content/api-apportionments-fee-division-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListApportionmentsFeeDivisionTypesService } from './listApportionmentsFeeDivisionTypesService'

jest.mock(
  '@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3'
)
const spyApi = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3'),
  'apportionmentsFeeDivisionTypesApiV3'
)

describe('src/@core/services/listApportionmentsFeeDivisionTypesService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListApportionmentsFeeDivisionTypesService(),
        store: useListApportionmentsFeeDivisionTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    let typeId: number = 1

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentsFeeDivisionTypesMock1, apportionmentsFeeDivisionTypesMock2]
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().apportionmentsFeeDivisionTypesList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler({ typeId })
    })
    expect(memory.local.get().apportionmentsFeeDivisionTypesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler({ typeId })
    })
    expect(memory.local.get().apportionmentsFeeDivisionTypesList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
