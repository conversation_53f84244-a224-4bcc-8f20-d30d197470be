import useListAlarmsRules from '@/@core/framework/store/hook/useListAlarmsRules'
import { alarmsRulesApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'

export const ListAlarmsRulesService = () => {
  const listAlarmsRule = useListAlarmsRules()

  const handler = async ({ dataEntityId }: { dataEntityId?: number } = {}) => {
    const { list: oldList } = memory.local.get().alarmsRulesList

    if (oldList.length) {
      listAlarmsRule.set({ list: oldList })
      return
    }

    const {
      data: { items: newList }
    } = await alarmsRulesApiV4(http).get({
      dataEntityId,
      order: 'asc',
      sort: 'name'
    })

    memory.local.set({ alarmsRulesList: { list: newList } })

    listAlarmsRule.set({ list: newList })
  }

  return { handler }
}
