import { act, renderHook } from '@testing-library/react'

import useListOperationalRuleStore from '@/@core/framework/store/hook/useListOperationalRuleStore'
import { memory, memoryApp } from '@/@core/infra/memory'
import { operationalRuleMock1, operationalRuleMock2 } from '@/__mock__/content/api-operational-rules.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListOperationalRulesService } from './listOperationalRulesService'

jest.mock('src/@/@core/infra/api/OperationalRulesApiV3')

const spyApi = jest.spyOn(
  require('@/@core/infra/api/OperationalRulesApiV3'),
  'operationalRulesApiV3'
)

describe('src/@core/services/listOperationalRulesService', () => {
  beforeEach(() => {
    memoryApp.init()
  })
  afterEach(() => {
    memoryApp.down()
  })

  it('should execute the handler method and validate the stored data', async () => {
    const { result } = renderHook(
      () => ({
        service: ListOperationalRulesService(),
        store: useListOperationalRuleStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApi.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [operationalRuleMock1, operationalRuleMock2]
        }
      })
    }))

    /** checks the initial data */
    expect(memory.local.get().operationalRuleList.list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    /** execut without data */
    await act(async () => {
      result.current.service.handler()
    })
    expect(memory.local.get().operationalRuleList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)

    /** execut with data */
    await act(async () => {
      await result.current.service.handler()
    })
    expect(memory.local.get().operationalRuleList.list).toHaveLength(2)
    expect(result.current.store.state.list).toHaveLength(2)
  })
})
