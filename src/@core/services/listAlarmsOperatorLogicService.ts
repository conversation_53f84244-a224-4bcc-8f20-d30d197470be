import useListAlarmsOperatorLogic from '@/@core/framework/store/hook/useListAlarmsOperatorLogic'
import { alarmsOperatorLogicApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'

export const ListAlarmsOperatorLogicService = () => {
  const listAlarmsOperatorLogic = useListAlarmsOperatorLogic()

  const handler = async () => {
    const { list: oldList } = memory.local.get().alarmsOperatorLogicList

    if (oldList.length) {
      listAlarmsOperatorLogic.set({ list: oldList })
      return
    }

    const {
      data: { items: newList }
    } = await alarmsOperatorLogicApiV4(http).get()

    memory.local.set({ alarmsOperatorLogicList: { list: newList } })

    listAlarmsOperatorLogic.set({ list: newList })
  }

  return { handler }
}
