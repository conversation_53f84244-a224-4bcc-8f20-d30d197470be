import useListExternalCodesStore from '@/@core/framework/store/hook/useListExternalCodesStore'
import { listExternalCodesApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { memory } from '@/@core/infra/memory'

export const ListExternalCodesService = () => {
  const istExternalCodesStore = useListExternalCodesStore()

  const handler = async () => {
    const { list: oldList } = memory.local.get().externalCodeList

    if (Object.values(oldList).every(Boolean)) {
      istExternalCodesStore.set({ list: oldList })
      return
    }

    const {
      data: { items: newList }
    } = await listExternalCodesApiV4(http).get()

    memory.local.set({
      externalCodeList: { list: newList }
    })

    istExternalCodesStore.set({
      list: newList
    })
  }

  return { handler }
}
