/** @type {import('next').NextConfig} */

const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  pullToRefresh: true
})

const getNextConfig = (isNodeEnvDevelopment) => {
  const nextConfigOptions = {
    productionBrowserSourceMaps: false,
    poweredByHeader: false,
    reactStrictMode: true,
    swcMinify: true,
    async redirects() {
      return [
        {
          source: '/',
          destination: '/dashboard',
          permanent: true
        }
      ]
    },
    compiler: {
      styledComponents: true
    },
    images: {
      remotePatterns: [
        {
          protocol: 'https',
          hostname: process.env.NEXT_HOST_NAME,
          pathname: '/files/**'
        }
      ]
    },
    async headers() {
      return [
        {
          source: '/:path*',
          headers: [
            {
              key: 'X-Frame-Options',
              value: 'SAMEORIGIN'
            },
            {
              key: 'X-XSS-Protection',
              value: '1; mode=block'
            },
            {
              key: 'X-Content-Type-Options',
              value: 'nosniff'
            },
            {
              key: 'Strict-Transport-Security',
              value: 'max-age=31536000; includeSubDomains; preload'
            },
            {
              key: 'Server',
              value: 'Comerc Telemetria'
            },
            {
              key: 'Referrer-Policy',
              value: 'no-referrer-when-downgrade'
            },
            {
              key: 'content-security-policy',
              value: 'upgrade-insecure-requests'
            },
            {
              key: 'cache-control',
              value: 'private, max-age=86400, must-revalidate'
            },
            {
              key: 'Access-Control-Allow-Origin',
              value: '*'
            }
          ]
        }
      ]
    }
  }

  if (isNodeEnvDevelopment) {
    return nextConfigOptions
  }

  return withPWA(nextConfigOptions)
}

module.exports = getNextConfig(process.env.NODE_ENV === 'development')
