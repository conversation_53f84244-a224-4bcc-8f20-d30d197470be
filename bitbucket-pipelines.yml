definitions:
  steps:
    - step: &test
        runs-on: zordon.eks
        deployment: testing
        image:
          name: node:22.17-alpine
        services:
          - docker
        caches:
          - docker
          - node
        script:
          - npm install
          - npm run test:coverage
        artifacts:
          paths:
            - coverage/lcov.info
            - coverage/clover.xml

    - step: &snyk
        runs-on: zordon.eks
        image:
          name: atlassian/pipelines-awscli:1.29.35
        services:
          - docker
        caches:
          - docker
        script:
          - apk update && apk upgrade
          - docker build -t ${BITBUCKET_COMMIT::7}:snyk .
          - apk add jq curl nodejs npm
          - npm install snyk snyk-to-html -g
          - snyk auth $SNYK_TOKEN_PLATFORM
          - snyk container test ${BITBUCKET_COMMIT::7}:snyk --file=./Dockerfile --json-file-output=resultado-snyk-container.json --severity-threshold=high || true
          - cat resultado-snyk-container.json | snyk-to-html > resultado-snyk-container.html
          - critical_vulnerabilities=$(jq '[.vulnerabilities[] | select(.severity == "high" or .severity == "critical")] | length' resultado-snyk-container.json)
          - echo "Número de vulnerabilidades críticas/altas:$critical_vulnerabilities"
          - if [ "$critical_vulnerabilities" -ge 1 ]; then exit 1; fi

    - step: &sonar
        runs-on: zordon.eks
        image: sonarsource/sonar-scanner-cli
        script:
          - >-
            sonar-scanner
            -Dsonar.projectName="Telemetria Software Frontend"
            -Dsonar.projectKey=zordon-front-mobile
            -Dsonar.host.url=${SONAR_HOST}
            -Dsonar.token=${SONAR_TOKEN}
            -Dsonar.sources=src/
            -Dsonar.tests=src/
            -Dsonar.test.inclusions=**/*.spec.*
            -Dsonar.language=TypeScript
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.javascript.coverage.reportPaths=coverage/clover.xml
            -Dsonar.exclusions=deploy/**,**/__mock__/**,**/__snapshots__/**,**/*.spec.*,**/*.stories.*,src/store/**,src/@core/language/**,src/**/*.tsx
            -Dsonar.qualitygate.wait=true

    - step: &build
        runs-on: zordon.eks
        image:
          name: atlassian/pipelines-awscli:1.29.35
        services:
          - docker
        caches:
          - docker
        script:
          - apk update && apk upgrade
          - $(aws ecr get-login --no-include-email --region $AWS_REGION)
          - apk add --no-cache bash curl
          - curl -1sLf 'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.alpine.sh' | bash
          - apk update && apk add infisical
          - if [[ $BITBUCKET_TAG == *"-hmg"* ]]; then ENV_PATH="staging"; fi
          - if [[ $BITBUCKET_TAG == *"-prod"* ]]; then ENV_PATH="prod"; fi
          - export INFISICAL_TOKEN=${INFISICAL_TOKEN_PLATFORM}
          - infisical export --projectId="732303f1-77ce-4394-9bec-cfe6e2f4e2f4" --format=dotenv-export --env=${ENV_PATH} --path=/$BITBUCKET_REPO_SLUG > .env
          - docker build -t $REPOSITORY_URL:$BITBUCKET_TAG .
          - apk add jq curl nodejs npm
          - npm install snyk snyk-to-html -g
          - snyk auth $SNYK_TOKEN_PLATFORM
          - snyk container test $REPOSITORY_URL:$BITBUCKET_TAG --file=./Dockerfile --json-file-output=resultado-snyk-container.json --severity-threshold=high || true
          - cat resultado-snyk-container.json | snyk-to-html > resultado-snyk-container.html
          - critical_vulnerabilities=$(jq '[.vulnerabilities[] | select(.severity == "high" or .severity == "critical")] | length' resultado-snyk-container.json)
          - echo "Número de vulnerabilidades críticas/altas:$critical_vulnerabilities"
          - if [ "$critical_vulnerabilities" -ge 1 ]; then exit 1; fi
          - docker push $REPOSITORY_URL:$BITBUCKET_TAG

    - step: &deploy
        image: amazon/aws-cli
        caches:
          - docker
        services:
          - docker
        script:
          - >-
            aws configure set aws_access_key_id "${AWS_ACCESS_KEY_ID}"
            && aws configure set aws_secret_access_key "${AWS_SECRET_ACCESS_KEY}"
            && aws configure set region $AWS_REGION
          - curl -L https://dl.k8s.io/release/v1.30.2/bin/linux/amd64/kubectl -o /usr/local/bin/kubectl
          - chmod +x /usr/local/bin/kubectl
          - aws eks update-kubeconfig --name $AWS_CLUSTER_NAME --kubeconfig kubeconfig.yaml --region $AWS_REGION
          - sed -i "s/latest/$BITBUCKET_TAG/g" deploy/$RESOURCE_PATH/kube.yaml
          - >-
            kubectl --kubeconfig=kubeconfig.yaml
            apply
            -f deploy/$RESOURCE_PATH/kube.yaml

    - step: &upload-secrets
        name: Update Secrets
        image: atlassian/pipelines-kubectl
        caches:
          - pip
        script:
          - apk add --no-cache py3-pip bash curl && pip3 install --no-cache-dir awscli
          - apk add py3-pip
          - curl -1sLf 'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.alpine.sh' | bash
          - apk update && apk add infisical
          - pip3 install awscli
          - apk update && apk add infisical
          - aws configure set aws_access_key_id "${AWS_ACCESS_KEY_ID}"
          - aws configure set aws_secret_access_key "${AWS_SECRET_ACCESS_KEY}"
          - PASS=$(aws ecr get-login-password --region $AWS_REGION)
          - echo $KUBE_CONFIG_AWS_PVE_CLUSTER_NONPROD | base64 -d > kubeconfig.yml
          - BUSINESSUNIT=$NAMESPACE
          - if [[ $BUSINESSUNIT == "$NAMESPACE" ]]; then BUSINESSUNIT="zordon-eks"; fi
          - if [[ $BITBUCKET_TAG == *"-hmg"* ]]; then ENV_PATH="staging"; SECRET_NAME="$BITBUCKET_REPO_SLUG-hmg-secret"; CLUSTER="${BUSINESSUNIT}-nonprod"; fi
          - if [[ $BITBUCKET_TAG == *"-prod"* ]]; then ENV_PATH="prod"; SECRET_NAME="$BITBUCKET_REPO_SLUG-secret"; CLUSTER="${BUSINESSUNIT}-prod"; fi
          - aws eks --region $AWS_REGION update-kubeconfig --name $CLUSTER --kubeconfig kubeconfigaws.yml
          - kubectl --kubeconfig=kubeconfigaws.yml -n $NAMESPACE delete secret $SECRET_NAME --ignore-not-found=true
          - |
            kubectl --kubeconfig=kubeconfigaws.yml -n $NAMESPACE create secret generic $SECRET_NAME --save-config \
            --from-env-file=<(infisical export --token=${INFISICAL_TOKEN_PLATFORM}  \
            --projectId=732303f1-77ce-4394-9bec-cfe6e2f4e2f4 \
            --format=dotenv  \
            --env=${ENV_PATH} \
            --path=/$BITBUCKET_REPO_SLUG | sed "s/'//g")
        services:
          - docker

    - step: &commit-deployment
        name: Commit Deployment Updates
        image: atlassian/default-image:2
        script:
          - mkdir -p ~/.ssh && echo $ID_RSA_BITBUCKET_ENCODED | base64 -di > ~/.ssh/id_rsa
          - chmod 400 ~/.ssh/id_rsa
          - eval "$(ssh-agent -s)" && ssh-add ~/.ssh/id_rsa
          - git config --global user.email "<EMAIL>"
          - git config --global user.name "Doc88 Deployment"
          - mkdir -p ~/deploy && cp ./update-tag.sh ~/deploy && chmod +x ~/deploy/update-tag.sh
          - cd ~/deploy && <NAME_EMAIL>:doc88_dev/$BITBUCKET_REPO_SLUG-k8s-config.git
          - if [[ $BITBUCKET_TAG == *"-hmg"* ]]; then ENV_PATH="hmg"; FOLDER="nonprod"; fi
          - if [[ $BITBUCKET_TAG == *"-prod"* ]]; then ENV_PATH="prod"; FOLDER="prod"; fi
          - ./update-tag.sh $BITBUCKET_TAG $BITBUCKET_REPO_SLUG-k8s-config/manifests/$FOLDER/deployment-$ENV_PATH.yaml "zordon-s-front"
          - cd $BITBUCKET_REPO_SLUG-k8s-config && git add . && git commit -m "Modificado por commit $BITBUCKET_REPO_SLUG:$BITBUCKET_TAG" && git push origin main
  services:
    docker:
      memory: 2048

pipelines:
  pull-requests:
    '**':
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *test
                name: Test on pull request
            - step:
                <<: *snyk
                name: Test snyk on pull request
      - step:
          <<: *sonar
          name: Sonar scan on pull request
  tags:
    '*-hmg':
      - parallel:
          - step:
              <<: *build
              name: 'Build n Push image'
          - step:
              <<: *upload-secrets
              name: 'Update Secrets'
      - step:
          <<: *commit-deployment
          name: 'Commit Deployment Updates: Deploy HMG'
          deployment: homolog

    '*-prod':
      - step:
          <<: *test
          name: Test on pull request
      - step:
          <<: *sonar
          name: Sonar scan on pull request
      - step:
          <<: *build
          name: Build on front beta prod
      - step:
          <<: *deploy
          name: Deploy on front beta prod
          deployment: Production
          trigger: manual
