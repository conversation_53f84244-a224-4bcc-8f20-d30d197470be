services:
  app:
    image: node:22.17-alpine
    container_name: front
    working_dir: /var/www
    volumes:
      - .:/var/www
    env_file:
      - .env
    ports:
      - '3000:3000'
    command: sh -c 'npm run dev'
  storybook:
    image: node:22.17-alpine
    container_name: front-storybook
    working_dir: /var/www
    depends_on:
      - app
    volumes:
      - .:/var/www
    ports:
      - '6006:6006'
    command: sh -c 'npm run storybook'
