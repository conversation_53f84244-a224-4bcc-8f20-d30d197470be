{"name": "telemetria-front", "version": "0.1.0", "private": true, "scripts": {"dev": "rm -rf .next && npm install && next dev", "build": "next build", "start": "next start -p 3000", "preview": "next build && next start -p 3000", "lint": "next lint", "test": "jest --bail", "test:coverage": "jest --bail --coverage --runInBand", "test:dev": "jest --watch -- -i", "storybook": "npm install && storybook dev -p 6006 --no-open", "build-storybook": "storybook build"}, "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.855.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fortawesome/free-brands-svg-icons": "^6.7.1", "@fortawesome/free-regular-svg-icons": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-fontawesome": "^0.2.3", "@hookform/resolvers": "^4.1.3", "@mui/material": "^6.4.11", "@mui/x-date-pickers": "^7.28.3", "@next/third-parties": "^14.2.30", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@reduxjs/toolkit": "^2.8.2", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^18.3.7", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.2", "lucide-react": "^0.511.0", "next": "^14.2.30", "next-pwa": "^5.6.0", "nookies": "^2.5.2", "postcss": "^8.5.6", "react": "^18.3.1", "react-datepicker": "^8.3.0", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-redux": "^9.2.0", "react-select": "^5.10.2", "sass": "^1.87.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.3", "uuid": "^11.1.0", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@chromatic-com/storybook": "^3.2.7", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/cookie": "^1.0.0", "@types/jest": "^29.5.14", "babel-loader": "^10.0.0", "eslint": "^9.25.1", "eslint-config-next": "^15.3.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-storybook": "^0.12.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "storybook": "^8.6.14", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "webpack": "^5.101.0"}, "engines": {"node": "^22.17"}}