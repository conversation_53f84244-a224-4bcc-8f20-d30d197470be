apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: front
  labels:
    app: frontend
spec:
  replicas: 1
  revisionHistoryLimit: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      imagePullSecrets:
        - name: aws-registry
      containers:
        - name: frontend
          imagePullPolicy: IfNotPresent
          securityContext:
            allowPrivilegeEscalation: false
          image: ************.dkr.ecr.us-west-2.amazonaws.com/zordon-s-front:latest
          command: ['/bin/sh']
          args: ['-c', 'npm run build && npm run start']
          envFrom:
            - configMapRef:
                name: configmap-frontend
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          resources:
            requests:
              cpu: '0.2'
              memory: 256Mi
          readinessProbe:
            initialDelaySeconds: 80
            httpGet:
              path: /
              port: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-front
  namespace: front
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/load-balancer-attributes: routing.http2.enabled=true
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/wafv2-acl-arn: arn:aws:wafv2:us-west-2:************:regional/webacl/webacl-non-prod/98e3f99f-7cd4-4dd5-9f55-a548ea889518
    alb.ingress.kubernetes.io/tags: company=comerc,client=comerc,environment=non production,management=devops,project=zordon
    external-dns.alpha.kubernetes.io/hostname: telemetria-hmg.comerc.com.br
spec:
  ingressClassName: alb
  rules:
    - host: hmg.zordon.app
      http:
        paths:
          - backend:
              service:
                name: front
                port:
                  name: http
            path: /
            pathType: Prefix
    - host: telemetria-hmg.comerc.com.br
      http:
        paths:
          - backend:
              service:
                name: frontend
                port:
                  name: http
            path: /
            pathType: Prefix
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: configmap-frontend
  namespace: front
data:
  NODE_ENV: 'production'
  NEXT_PUBLIC_BASE_URL: 'https://telemetria-api-hmg.comerc.com.br'
  NEXT_HOST_NAME: 'telemetria-api-hmg.comerc.com.br'
  NEXT_PUBLIC_KEY_TOKEN_ZORDON: 'zordon-mobile.auth_token'
  NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID: 'GTM-W43S5VML'
  NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ENABLE: 'true'
  ZENDESK_DISABLED: 'true'
  ZENDESK_KEY: '365adb03-4dd6-452d-97f2-ac3533c38c25'
  APP_BASE_PUSHER_KEY: ''
  APP_BASE_PUSHER_CLUSTER: ''
  APP_BASE_PUSHER_ID: ''
  APP_BASE_PUSHER_SECRET: ''
  NEXT_PUBLIC_LOG_GROUP_NAME: 'ZordonS-homolog'
  NEXT_PUBLIC_KEY_AWS_DEFAULT_REGION: 'us-west-2'
  NEXT_PUBLIC_KEY_AWS_ACCESS_KEY_ID: '********************'
  NEXT_PUBLIC_KEY_AWS_SECRET_ACCESS_KEY: 'DPklUDlsFEsyBOthaMCsCm/ayUWib0s65nwXHF6a'
  NEXT_LOG_DEVELOPMENT: 'false'
  NEXT_PUBLIC_NEXT_CLIENT_ID_COGNITO: '6kjh89lh3d8nfcqaf4akb29err'
  NEXT_PUBLIC_NEXT_URL_LOGIN_COGNITO: 'https://login-hmg.comerc.com.br'
  NEXT_PUBLIC_NEXT_COGNITO_BASE_URL: 'https://comerc-auth-api-hmg.comerc.com.br'
  NEXT_PUBLIC_COGNITO_CALLBACK_URL: 'https://telemetria-hmg.comerc.com.br/login'
  NEXT_PUBLIC_LINK_MY_SERVICES: 'hmg'
  NEXT_PUBLIC_LINK_MY_ACCOUNT: 'hmg'
