apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: front
  labels:
    app: frontend
spec:
  replicas: 1
  revisionHistoryLimit: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      imagePullSecrets:
        - name: aws-registry
      containers:
        - name: frontend
          imagePullPolicy: IfNotPresent
          securityContext:
            allowPrivilegeEscalation: false
          image: 300865496329.dkr.ecr.us-west-2.amazonaws.com/zordon-s-front:latest
          command: ['/bin/sh']
          args: ['-c', 'npm run build && npm run start']
          envFrom:
            - configMapRef:
                name: configmap-frontend
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          resources:
            requests:
              cpu: '0.2'
              memory: 256Mi
          readinessProbe:
            httpGet:
              path: /
              port: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-front
  namespace: front
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/load-balancer-attributes: routing.http2.enabled=true
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    external-dns.alpha.kubernetes.io/hostname: zordon.app, telemetria.comerc.com.br
    alb.ingress.kubernetes.io/tags: company=comerc,client=comerc,environment=production,management=devops,project=zordon
spec:
  ingressClassName: alb
  rules:
    - host: zordon.app
      http:
        paths:
          - backend:
              service:
                name: front
                port:
                  name: http
            path: /
            pathType: Prefix
    - host: telemetria.comerc.com.br
      http:
        paths:
          - backend:
              service:
                name: frontend
                port:
                  name: http
            path: /
            pathType: Prefix
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: configmap-frontend
  namespace: front
data:
  NODE_ENV: 'production'
  NEXT_PUBLIC_BASE_URL: 'https://cognito-api.zordon.app'
  NEXT_HOST_NAME: 'cognito-api.zordon.app'
  NEXT_PUBLIC_KEY_TOKEN_ZORDON: 'zordon-mobile.auth_token'
  NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID: 'GTM-T5FXLZZ'
  NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ENABLE: 'true'
  ZENDESK_DISABLED: 'false'
  ZENDESK_KEY: '365adb03-4dd6-452d-97f2-ac3533c38c25'
  APP_BASE_PUSHER_KEY: ''
  APP_BASE_PUSHER_CLUSTER: ''
  APP_BASE_PUSHER_ID: ''
  APP_BASE_PUSHER_SECRET: ''
  NEXT_PUBLIC_LOG_GROUP_NAME: 'ZordonS-production'
  NEXT_PUBLIC_KEY_AWS_DEFAULT_REGION: 'us-west-2'
  NEXT_PUBLIC_KEY_AWS_ACCESS_KEY_ID: '********************'
  NEXT_PUBLIC_KEY_AWS_SECRET_ACCESS_KEY: 'DPklUDlsFEsyBOthaMCsCm/ayUWib0s65nwXHF6a'
  NEXT_LOG_DEVELOPMENT: 'false'
  NEXT_PUBLIC_NEXT_CLIENT_ID_COGNITO: '745qibvgotlhk1vgpoqc9ucrfn'
  NEXT_PUBLIC_NEXT_URL_LOGIN_COGNITO: 'https://login.comerc.com.br'
  NEXT_PUBLIC_NEXT_COGNITO_BASE_URL: 'https://comerc-auth-api.comerc.com.br'
  NEXT_PUBLIC_COGNITO_CALLBACK_URL: 'https://telemetria.comerc.com.br/login'
  NEXT_PUBLIC_LINK_MY_SERVICES: 'prod'
  NEXT_PUBLIC_LINK_MY_ACCOUNT: 'prod'
