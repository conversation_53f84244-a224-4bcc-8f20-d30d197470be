# Guia do Usuário - Dashboard

## Índice
1. [<PERSON>is<PERSON> Geral](#visão-geral)
2. [Acesso ao Dashboard](#acesso-ao-dashboard)
3. [Interface Principal](#interface-principal)
4. [Navegação e Menu](#navegação-e-menu)
5. [Widgets e Visualizações](#widgets-e-visualizações)
6. [Gerenciamento de Dashboards](#gerenciamento-de-dashboards)
7. [Filtros e Período](#filtros-e-período)
8. [Permissões e Acesso](#permissões-e-acesso)
9. [Solução de Problemas](#solução-de-problemas)

## Visão Geral

O Dashboard é uma ferramenta de visualização de dados que permite monitorar informações em tempo real através de widgets interativos. O sistema oferece diferentes tipos de visualizações como gráficos, medidores, listas dinâmicas e cartões informativos.

### Principais Funcionalidades
- **Visualização em tempo real** de dados de empresas e equipamentos
- **Múltiplos tipos de widgets** (gráficos, medidores, listas, cartões)
- **Filtros por período** para análise temporal
- **Criação e exclusão** de dashboards personalizados
- **Interface responsiva** para desktop e mobile
- **Sistema de abas** para organização de conteúdo

## Acesso ao Dashboard

### Requisitos de Acesso
- **Autenticação**: É necessário estar logado no sistema
- **Permissões**: O usuário deve ter permissão para o módulo "dashboard"
- **Conta**: Super administradores precisam selecionar uma conta para visualizar

### Como Acessar
1. Faça login no sistema
2. No menu principal, clique em **"Dashboard"**
3. Se você for super administrador, selecione uma conta no menu lateral

## Interface Principal

### Layout da Tela
A interface do dashboard é dividida em três áreas principais:

#### 1. Menu Lateral (Esquerda)
- **Busca**: Campo para pesquisar empresas/equipamentos
- **Lista de Entidades**: Empresas e equipamentos disponíveis
- **Controles**: Botões para criar, recarregar e excluir dashboards

#### 2. Área Central (Principal)
- **Título**: Nome da entidade selecionada
- **Controles de Período**: Seletor de data
- **Abas**: Diferentes visualizações do dashboard
- **Widgets**: Gráficos e informações da entidade

#### 3. Barra Superior
- **Breadcrumb**: Navegação hierárquica
- **Controles de Recarregamento**: Atualizar widgets

### Responsividade
- **Desktop (≥1366px)**: Menu lateral fixo
- **Mobile (<1366px)**: Menu lateral retrátil

## Navegação e Menu

### Menu Lateral

#### Busca de Entidades
1. Use o campo de busca no topo do menu
2. Digite o nome da empresa ou equipamento
3. Os resultados são filtrados automaticamente

#### Seleção de Entidades
- **Empresas**: Identificadas pelo ícone de prédio 🏢
- **Equipamentos**: Identificados pelo ícone de sinal 📡
- Clique na entidade desejada para visualizar seu dashboard

#### Controles do Menu
- **📌 Fixar Menu**: Mantém o menu sempre visível
- **🔄 Recarregar**: Atualiza a lista de dashboards
- **➕ Criar Dashboard**: Abre modal de criação
- **🗑️ Excluir Dashboard**: Abre modal de exclusão

### Abas do Dashboard
Cada entidade pode ter múltiplas abas com diferentes visualizações:
- Clique nas abas para alternar entre visualizações
- A aba ativa é destacada em azul
- Cada aba contém widgets específicos para diferentes métricas

## Widgets e Visualizações

O sistema oferece diversos tipos de widgets para visualização de dados:

### 1. Gráficos Dinâmicos (DynamicChart)
- **Função**: Exibem dados em formato de gráfico de linha/área
- **Características**: 
  - Dados históricos por período
  - Zoom e navegação interativa
  - Múltiplas séries de dados

### 2. Gráficos de Medidor (DynamicChartGauge)
- **Função**: Mostram valores atuais em formato de velocímetro
- **Características**:
  - Indicação visual de limites
  - Valores em tempo real
  - Cores indicativas de status

### 3. Cartões de Rosca (DynamicDonutCard)
- **Função**: Apresentam distribuição de dados em gráfico de rosca
- **Características**:
  - Percentuais de distribuição
  - Legendas interativas
  - Totalizadores

### 4. Cartões de Demanda (DynamicDemand)
- **Função**: Exibem informações de demanda energética
- **Características**:
  - Valores de pico e fora de pico
  - Barras de progresso
  - Unidades de medida

### 5. Cartões de Consumo (DynamicIntake)
- **Função**: Mostram dados de consumo
- **Características**:
  - Valores numéricos formatados
  - Unidades específicas
  - Comparações temporais

### 6. Listas Dinâmicas (DynamicListCard)
- **Função**: Apresentam dados em formato de lista
- **Características**:
  - Múltiplas colunas
  - Ordenação de dados
  - Formatação personalizada

### 7. Alarmes (AlarmsNew)
- **Função**: Exibem alertas e notificações
- **Características**:
  - Status de criticidade
  - Timestamps
  - Descrições detalhadas

## Gerenciamento de Dashboards

### Criar Novo Dashboard

1. **Acesso**: Clique no botão "Criar Dashboard" no menu lateral
2. **Configuração**:
   - **Conta**: Selecione a conta (preenchido automaticamente)
   - **Tipo de Entidade**: Escolha "Empresa" ou "Equipamento"
   - **Entidades**: Selecione as entidades específicas
   - **Template**: Escolha um template disponível
   - **Substituir**: Marque para substituir dashboards existentes

3. **Opções**:
   - ✅ **Substituir**: Remove dashboards antigos
   - ❌ **Não Substituir**: Combina com dashboards existentes

4. **Finalização**: Clique em "Criar" para confirmar

### Excluir Dashboard

1. **Acesso**: Clique no botão de lixeira 🗑️ no menu lateral
2. **Seleção**:
   - **Conta**: Escolha a conta
   - **Busca**: Use o campo para filtrar entidades
   - **Entidades**: Marque as entidades para exclusão

3. **Confirmação**: Clique em "Excluir" para confirmar

⚠️ **Atenção**: A exclusão é permanente e não pode ser desfeita.

## Filtros e Período

### Seletor de Período
1. **Acesso**: Clique no ícone de calendário 📅 na área principal
2. **Seleção**: 
   - Escolha a data inicial
   - Escolha a data final
   - Clique em "Aplicar"

3. **Formato**: DD/MM/AAAA
4. **Efeito**: Todos os widgets são atualizados com o novo período

### Atualização de Dados
- **Manual**: Use o botão de recarregar 🔄
- **Automática**: Alguns widgets se atualizam automaticamente
- **Por Período**: Mudanças de período recarregam todos os widgets

## Permissões e Acesso

### Níveis de Usuário

#### Super Administrador
- **Acesso**: Todas as contas
- **Requisito**: Deve selecionar uma conta para visualizar
- **Permissões**: Criar/excluir dashboards de qualquer conta

#### Usuário Regular
- **Acesso**: Apenas sua conta
- **Visualização**: Dashboards da sua conta
- **Permissões**: Dependem das configurações do administrador

### Permissões Necessárias
- **dashboard.list**: Visualizar dashboards
- **dashboard.create**: Criar novos dashboards  
- **dashboard.delete**: Excluir dashboards

## Solução de Problemas

### Problemas Comuns

#### Dashboard não carrega
1. Verifique sua conexão com a internet
2. Confirme se você tem permissões adequadas
3. Tente recarregar a página
4. Verifique se a conta está selecionada (super admin)

#### Widgets não exibem dados
1. Verifique o período selecionado
2. Confirme se há dados para o período
3. Use o botão de recarregar widgets
4. Verifique se a entidade está ativa

#### Menu não aparece (mobile)
1. Procure pelo botão de menu (☰)
2. Toque para abrir o menu lateral
3. Verifique a orientação da tela

#### Erro ao criar dashboard
1. Verifique se todos os campos estão preenchidos
2. Confirme se você tem permissão de criação
3. Tente com um template diferente
4. Verifique se as entidades estão ativas

### Contato para Suporte
Se os problemas persistirem, entre em contato com:
- **Suporte Técnico**: Através do sistema de tickets
- **Administrador**: Do seu sistema/conta
- **Documentação**: Consulte a documentação técnica adicional

---

*Este guia cobre as principais funcionalidades do Dashboard. Para recursos avançados ou configurações específicas, consulte a documentação técnica ou entre em contato com o suporte.*
