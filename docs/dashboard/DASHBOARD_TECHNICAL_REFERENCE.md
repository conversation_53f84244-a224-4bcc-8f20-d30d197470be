# Referência Técnica - Dashboard

## Índice
1. [Arquitetura do Sistema](#arquitetura-do-sistema)
2. [Tipos de Widget Detalhados](#tipos-de-widget-detalhados)
3. [APIs e Endpoints](#apis-e-endpoints)
4. [Estrutura de Dados](#estrutura-de-dados)
5. [Configurações Avançadas](#configurações-avançadas)
6. [Troubleshooting Técnico](#troubleshooting-técnico)

## Arquitetura do Sistema

### Componentes Principais

#### Frontend (React/TypeScript)
- **Página Principal**: `/src/@core/presentation/views/dashboard/page.tsx`
- **Componentes**: Widgets modulares e reutilizáveis
- **Estado**: Gerenciado via hooks customizados
- **Responsividade**: Breakpoint em 1366px para desktop/mobile

#### Backend APIs
- **Dashboard API v3**: Gerenciamento de dashboards
- **Menu API v4**: Navegação e entidades
- **Templates API v3**: Templates de dashboard

### Fluxo de Dados
1. **Autenticação**: Verificação de usuário e permissões
2. **Carregamento**: Busca entidades e dashboards disponíveis
3. **Seleção**: Usuário escolhe entidade
4. **Renderização**: Widgets são carregados com dados específicos
5. **Atualização**: Dados são atualizados conforme filtros

## Tipos de Widget Detalhados

### 1. DynamicChart
**Arquivo**: `DashboardWidgetDynamicChart.tsx`

**Configuração**:
```typescript
interface IStructureDynamicChart {
  title: string
  type: string
  apiRoute: { main: string; range?: string }
  chartOptions: Record<string, any>
  forNoRecords: {
    config: {
      contractedOutOfPeak: string
      contractedPeak: string
      hour: string
      peakTime: boolean
      reactiveDemand: string
      value: string
    }
  }
  rangeTitle?: string
}
```

**Uso**: Gráficos de linha/área para dados temporais

### 2. DynamicChartGauge
**Arquivo**: `DashboardWidgetDynamicChartGauge.tsx`

**Configuração**:
```typescript
interface IStructureDynamicChartGauge {
  title: string
  type: string
  // Configurações específicas do medidor
}
```

**Uso**: Medidores circulares para valores instantâneos

### 3. DynamicDonutCard
**Arquivo**: `DashboardWidgetDynamicDonutCard.tsx`

**Configuração**:
```typescript
interface IStructureDynamicDonutCard {
  title: string
  type: string
  apiRoutes: {
    apiRoute: {
      url: string
      method: string
      params: {
        aggregate?: string
        id: number
        order: string
        properties: string[]
        summarize_by: string
      }
    }
    content: {
      fieldLabel: string
      fieldValue: string
      textLabel: string
      textValue: string
      unit: string
    }
  }[]
  chartOptions: Record<string, string>
}
```

**Uso**: Gráficos de rosca para distribuições

### 4. DynamicDemand
**Arquivo**: `DashboardWidgetDynamicDemand.tsx`

**Configuração**:
```typescript
interface IStructureDynamicDemand {
  title: string
  type: string
  peak: string
  routes: {
    method: 'post'
    apiRoute: string
    config: {
      description: string
      peakTime: boolean
      title: string
      unit: string
      value: string
    }
  }[]
}
```

**Uso**: Informações de demanda energética com barras de progresso

### 5. DynamicIntake
**Arquivo**: `DashboardWidgetDynamicIntake.tsx`

**Configuração**:
```typescript
interface IStructureDynamicIntake {
  title: string
  type: string
  peak: string
  routes: {
    method: 'post'
    apiRoute: string
    config: {
      description: string
      peakTime: boolean
      title: string
      unit: string
      value: string
    }
  }[]
}
```

**Uso**: Valores de consumo formatados numericamente

### 6. DynamicListCard
**Arquivo**: `DashboardWidgetDynamicListCard.tsx`

**Uso**: Listas tabulares com dados dinâmicos

### 7. AlarmsNew
**Arquivo**: `DashboardWidgetDynamicAlarmsNew.tsx`

**Uso**: Exibição de alarmes e alertas do sistema

## APIs e Endpoints

### Dashboard API v3
**Base**: `/api/v3/dashboards`

#### Endpoints:
- **GET /tabs**: Busca abas de um dashboard
  ```typescript
  params: {
    entityType: string
    entityId: number
  }
  ```

- **POST /create**: Cria novo dashboard
  ```typescript
  payload: {
    entities: number[]
    entitiesType: string
    templateId: number
    replace: boolean
  }
  ```

- **DELETE /delete**: Remove dashboard
  ```typescript
  payload: {
    entityIds: number[]
    accountId: number
  }
  ```

### Menu API v4
**Base**: `/api/v4/menu`

#### Endpoints:
- **GET /dashboard**: Lista entidades disponíveis
  ```typescript
  params: {
    accountId: number
  }
  ```

### Templates API v3
**Base**: `/api/v3/dashboards/templates`

#### Endpoints:
- **GET /**: Lista templates disponíveis

## Estrutura de Dados

### Estado da Página
```typescript
interface IStateTestData {
  menu: {
    open: boolean
    items: IMenuDashboard[]
    itemActive: Partial<IMenuDashboard> | null
    inputSearch: string
    inputAccount: { id: number; name: string } | null
  }
  page: {
    tab: IDashboardTab | null
    tabs: IDashboardTab[]
    inputData: IInputData | null
    period: { initial: string; final: string }
  }
}
```

### Dados de Input
```typescript
interface IInputData {
  route: string
  date: string
  instance: string
  timezone: string
  id: number
  codigo: number
  initial_date: number
  final_date: number
}
```

### Estrutura de Abas
```typescript
interface IDashboardTab {
  label: string
  xWidgets: IDashboardTabWidgetX[]
}

interface IDashboardTabWidgetX {
  key: string
  yWidgets: IDashboardTabWidgetY[]
}

interface IDashboardTabWidgetY {
  key: string
  name: string
  dataStructure: IDashboardTabStructure
  order: number
  triggerByHeadField: boolean
}
```

## Configurações Avançadas

### Responsividade
```typescript
// Breakpoint para desktop/mobile
const isDesktop = positions.innerWidth >= 1366
```

### Persistência de Estado
- **Menu Fixo**: Salvo em localStorage como `dashboard-menu-fixed`
- **Menu Aberto**: Salvo em localStorage como `dashboard-menu-desktop-open`

### Atualização de Widgets
```typescript
// Sistema de notificação para atualizações
interface WidgetUpdates {
  [widgetKey: string]: any
}
```

### Filtros de Período
```typescript
// Formato de período
interface Period {
  initial: string // 'YYYY-MM-DD'
  final: string   // 'YYYY-MM-DD'
}
```

## Troubleshooting Técnico

### Logs e Debugging

#### Logs de Erro
- **Frontend**: Console do navegador
- **Requests**: Network tab das DevTools
- **Estado**: React DevTools

#### Pontos de Debug Comuns
1. **Autenticação**: Verificar `authStore.state.isSuperAdmin`
2. **Permissões**: Verificar `permissionsStore['dashboard']`
3. **APIs**: Verificar responses em Network tab
4. **Estado**: Verificar `statePage` no React DevTools

### Problemas de Performance

#### Otimizações Implementadas
- **Lazy Loading**: Widgets carregam sob demanda
- **Memoização**: Componentes otimizados com React.memo
- **Debounce**: Busca com delay para evitar requests excessivos

#### Monitoramento
- **Requests**: Verificar quantidade de chamadas API
- **Rendering**: Usar React Profiler
- **Memory**: Verificar vazamentos de memória

### Erros Comuns

#### 1. "Dashboard não carrega"
**Causas**:
- Usuário sem permissões
- Super admin sem conta selecionada
- Erro na API de menu

**Debug**:
```typescript
// Verificar no console
console.log('Auth State:', authStore.state)
console.log('Permissions:', permissionsStore)
```

#### 2. "Widgets não exibem dados"
**Causas**:
- Período inválido
- Entidade sem dados
- Erro na API específica do widget

**Debug**:
```typescript
// Verificar inputData
console.log('Input Data:', statePage.page.inputData)
console.log('Period:', statePage.page.period)
```

#### 3. "Menu não responde"
**Causas**:
- Estado de loading travado
- Erro na busca de entidades
- Problema de responsividade

**Debug**:
```typescript
// Verificar estado do menu
console.log('Menu State:', statePage.menu)
console.log('Is Desktop:', isDesktop)
```

### Configurações de Desenvolvimento

#### Variáveis de Ambiente
```env
NEXT_PUBLIC_API_BASE_URL=https://api.example.com
NEXT_PUBLIC_COGNITO_CALLBACK_URL=https://app.example.com/callback
```

#### Scripts Úteis
```bash
# Desenvolvimento
npm run dev

# Build
npm run build

# Testes
npm run test

# Lint
npm run lint
```

---

*Esta referência técnica complementa o guia do usuário com informações detalhadas para desenvolvedores e administradores do sistema.*
