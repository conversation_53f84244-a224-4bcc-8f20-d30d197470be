# Exemplos Práticos e Tutoriais - Dashboard

## Índice
1. [Cenários de Uso Comuns](#cenários-de-uso-comuns)
2. [Tutoriais Passo a Passo](#tutoriais-passo-a-passo)
3. [Exemplos de Configuração](#exemplos-de-configuração)
4. [Casos de Uso por Setor](#casos-de-uso-por-setor)
5. [<PERSON><PERSON>s Práticas](#melhores-práticas)
6. [FAQ - Perguntas Frequentes](#faq---perguntas-frequentes)

## Cenários de Uso Comuns

### 1. Monitoramento de Consumo Energético
**Objetivo**: Acompanhar o consumo de energia de uma empresa em tempo real

**Widgets Utilizados**:
- `DynamicChart`: Gráfico de consumo ao longo do tempo
- `DynamicIntake`: Valores de consumo atual
- `DynamicDemand`: Demanda de pico e fora de pico

**Benefícios**:
- Identificação de picos de consumo
- Otimização de custos energéticos
- Planejamento de demanda

### 2. Análise de Performance de Equipamentos
**Objetivo**: Monitorar o desempenho e status de equipamentos

**Widgets Utilizados**:
- `DynamicChartGauge`: Medidores de performance
- `AlarmsNew`: Alertas de manutenção
- `DynamicListCard`: Lista de equipamentos e status

**Benefícios**:
- Manutenção preventiva
- Redução de downtime
- Otimização operacional

### 3. Dashboard Executivo
**Objetivo**: Visão geral para tomada de decisões estratégicas

**Widgets Utilizados**:
- `DynamicDonutCard`: Distribuição de recursos
- `DynamicChart`: Tendências históricas
- `DynamicIntake`: KPIs principais

**Benefícios**:
- Visão consolidada
- Suporte à decisão
- Acompanhamento de metas

## Tutoriais Passo a Passo

### Tutorial 1: Criando Seu Primeiro Dashboard

#### Passo 1: Acesso Inicial
1. Faça login no sistema
2. Navegue para o menu "Dashboard"
3. Se for super admin, selecione uma conta

#### Passo 2: Explorando a Interface
1. **Menu Lateral**: Observe a lista de empresas/equipamentos
2. **Área Central**: Note que está vazia (sem dashboard selecionado)
3. **Controles**: Identifique os botões de ação

#### Passo 3: Selecionando uma Entidade
1. No menu lateral, clique em uma empresa ou equipamento
2. Aguarde o carregamento das abas
3. Explore as diferentes abas disponíveis

#### Passo 4: Interagindo com Widgets
1. **Gráficos**: Passe o mouse para ver detalhes
2. **Período**: Clique no calendário para alterar datas
3. **Atualização**: Use o botão de recarregar se necessário

### Tutorial 2: Criando um Dashboard Personalizado

#### Passo 1: Preparação
1. Identifique as entidades que precisam de dashboard
2. Verifique se você tem permissões de criação
3. Escolha um template adequado

#### Passo 2: Iniciando a Criação
1. Clique em "Criar Dashboard" no menu lateral
2. O modal de criação será aberto

#### Passo 3: Configuração Básica
1. **Conta**: Confirme se está correta (preenchida automaticamente)
2. **Tipo de Entidade**: Selecione "Empresa" ou "Equipamento"
3. **Template**: Escolha um template da lista

#### Passo 4: Seleção de Entidades
1. Use o campo de busca para filtrar entidades
2. Marque as entidades desejadas na árvore
3. Verifique se todas estão selecionadas

#### Passo 5: Opções Avançadas
- **Substituir**: ✅ para substituir dashboards existentes
- **Combinar**: ❌ para manter dashboards existentes

#### Passo 6: Finalização
1. Revise todas as configurações
2. Clique em "Criar"
3. Aguarde a confirmação de sucesso

### Tutorial 3: Análise de Dados com Filtros

#### Passo 1: Seleção de Período
1. Clique no ícone de calendário 📅
2. Selecione a data inicial
3. Selecione a data final
4. Clique em "Aplicar"

#### Passo 2: Interpretação dos Dados
1. **Gráficos de Linha**: Observe tendências ao longo do tempo
2. **Medidores**: Verifique valores atuais vs. limites
3. **Cartões**: Analise totalizadores e percentuais

#### Passo 3: Comparação de Períodos
1. Anote os valores do período atual
2. Altere para um período anterior
3. Compare os resultados
4. Identifique variações significativas

## Exemplos de Configuração

### Exemplo 1: Dashboard de Consumo Energético

**Estrutura de Abas**:
- **Visão Geral**: Consumo total e tendências
- **Demanda**: Picos e fora de pico
- **Histórico**: Comparações mensais/anuais

**Widgets Recomendados**:
```
Aba "Visão Geral":
├── DynamicIntake (Consumo Atual)
├── DynamicChart (Tendência 30 dias)
└── DynamicDonutCard (Distribuição por setor)

Aba "Demanda":
├── DynamicDemand (Pico/Fora de Pico)
├── DynamicChartGauge (Demanda Atual)
└── DynamicChart (Curva de Demanda)

Aba "Histórico":
├── DynamicChart (Comparativo Anual)
└── DynamicListCard (Resumo Mensal)
```

### Exemplo 2: Dashboard de Equipamentos

**Estrutura de Abas**:
- **Status**: Estado atual dos equipamentos
- **Performance**: Métricas de desempenho
- **Manutenção**: Alertas e programação

**Widgets Recomendados**:
```
Aba "Status":
├── DynamicListCard (Lista de Equipamentos)
├── DynamicDonutCard (Status Distribution)
└── AlarmsNew (Alertas Ativos)

Aba "Performance":
├── DynamicChartGauge (Eficiência)
├── DynamicChart (Performance Histórica)
└── DynamicIntake (Métricas Chave)

Aba "Manutenção":
├── AlarmsNew (Alertas de Manutenção)
├── DynamicListCard (Cronograma)
└── DynamicChart (Histórico de Falhas)
```

## Casos de Uso por Setor

### Setor Industrial
**Foco**: Eficiência operacional e controle de custos

**Dashboards Típicos**:
- Consumo energético por linha de produção
- Performance de equipamentos críticos
- Indicadores de sustentabilidade

**Widgets Mais Utilizados**:
- `DynamicChart`: Tendências de produção
- `DynamicDemand`: Demanda energética
- `AlarmsNew`: Alertas de equipamentos

### Setor Comercial
**Foco**: Gestão de custos e conforto

**Dashboards Típicos**:
- Consumo por área/andar
- Eficiência de sistemas HVAC
- Comparativos de períodos

**Widgets Mais Utilizados**:
- `DynamicIntake`: Consumo por área
- `DynamicDonutCard`: Distribuição de gastos
- `DynamicChart`: Tendências sazonais

### Setor Residencial
**Foco**: Economia e sustentabilidade

**Dashboards Típicos**:
- Consumo familiar
- Comparação com vizinhança
- Metas de economia

**Widgets Mais Utilizados**:
- `DynamicIntake`: Consumo mensal
- `DynamicChart`: Histórico familiar
- `DynamicChartGauge`: Meta vs. realizado

## Melhores Práticas

### 1. Organização de Dashboards
- **Agrupe por função**: Operacional, tático, estratégico
- **Use nomes descritivos**: "Consumo Fábrica A" vs. "Dashboard 1"
- **Mantenha consistência**: Mesmo layout para dashboards similares

### 2. Seleção de Widgets
- **Menos é mais**: Evite sobrecarga visual
- **Propósito claro**: Cada widget deve ter objetivo específico
- **Hierarquia visual**: Widgets mais importantes em destaque

### 3. Configuração de Períodos
- **Períodos relevantes**: Escolha intervalos significativos
- **Comparações úteis**: Mesmo período do ano anterior
- **Granularidade adequada**: Diário, semanal, mensal conforme necessidade

### 4. Manutenção
- **Revisão regular**: Verifique relevância dos dashboards
- **Limpeza periódica**: Remova dashboards obsoletos
- **Atualizações**: Mantenha templates atualizados

### 5. Performance
- **Períodos otimizados**: Evite intervalos muito longos
- **Cache inteligente**: Use recarregamento apenas quando necessário
- **Monitoramento**: Acompanhe tempo de carregamento

## FAQ - Perguntas Frequentes

### Q1: Por que meu dashboard não carrega?
**R**: Verifique:
- Conexão com internet
- Permissões de acesso
- Seleção de conta (super admin)
- Status da entidade

### Q2: Como alterar o período de análise?
**R**: Clique no ícone de calendário 📅 na área principal e selecione as datas desejadas.

### Q3: Posso criar dashboards para múltiplas entidades?
**R**: Sim, durante a criação, selecione múltiplas entidades na árvore de seleção.

### Q4: O que significa "Substituir" na criação?
**R**: 
- ✅ **Substituir**: Remove dashboards existentes e cria novos
- ❌ **Não Substituir**: Mantém existentes e adiciona novos

### Q5: Como excluir um dashboard?
**R**: Use o botão de lixeira 🗑️ no menu lateral, selecione as entidades e confirme a exclusão.

### Q6: Por que alguns widgets não mostram dados?
**R**: Pode ser devido a:
- Período sem dados
- Entidade inativa
- Problemas de conectividade
- Configuração incorreta do widget

### Q7: Como recarregar dados dos widgets?
**R**: Use o botão de recarregar 🔄 na área principal ou altere o período.

### Q8: Posso usar o dashboard no celular?
**R**: Sim, a interface é responsiva e se adapta a telas menores.

### Q9: Como buscar uma entidade específica?
**R**: Use o campo de busca no topo do menu lateral.

### Q10: O que são templates?
**R**: Modelos pré-configurados de dashboards com widgets específicos para diferentes tipos de análise.

---

*Este documento fornece exemplos práticos para maximizar o uso do Dashboard. Para dúvidas específicas, consulte a documentação técnica ou entre em contato com o suporte.*
