# Guia Rápido - Dashboard

## 🚀 Início Rápido

### Acesso
1. **Login** → **Menu Dashboard** → **Selecionar Conta** (se super admin)

### Primeira Visualização
1. **Menu Lateral** → **Clicar em Entidade** → **Explorar Abas**

### Criar Dashboard
1. **"Criar Dashboard"** → **Configurar** → **Selecionar Entidades** → **Criar**

## 📊 Tipos de Widget

| Widget | Ícone | Função | Uso Principal |
|--------|-------|--------|---------------|
| **DynamicChart** | 📈 | Gráficos de linha/área | Tendências temporais |
| **DynamicChartGauge** | ⏱️ | Medidores circulares | Valores instantâneos |
| **DynamicDonutCard** | 🍩 | Gráficos de rosca | Distribuições |
| **DynamicDemand** | ⚡ | Barras de demanda | Demanda energética |
| **DynamicIntake** | 📊 | Valores numéricos | Consumo/totalizadores |
| **DynamicListCard** | 📋 | Listas tabulares | Dados estruturados |
| **AlarmsNew** | 🚨 | Alertas | Notificações |

## 🎛️ Controles Principais

### Menu Lateral
| Botão | Função |
|-------|--------|
| 🔍 | Buscar entidades |
| ➕ | Criar dashboard |
| 🔄 | Recarregar lista |
| 🗑️ | Excluir dashboard |
| 📌 | Fixar/desafixar menu |

### Área Principal
| Botão | Função |
|-------|--------|
| 📅 | Selecionar período |
| 🔄 | Recarregar widgets |
| 📑 | Alternar abas |

## ⚙️ Ações Rápidas

### Alterar Período
```
📅 → Data Inicial → Data Final → Aplicar
```

### Buscar Entidade
```
🔍 → Digite nome → Selecionar resultado
```

### Criar Dashboard
```
➕ → Conta → Tipo → Entidades → Template → ✅/❌ Substituir → Criar
```

### Excluir Dashboard
```
🗑️ → Conta → Buscar → Selecionar → Excluir
```

## 🔧 Solução Rápida de Problemas

### Dashboard não carrega
- ✅ Verificar internet
- ✅ Confirmar permissões
- ✅ Selecionar conta (super admin)
- ✅ Recarregar página

### Widgets sem dados
- ✅ Verificar período
- ✅ Confirmar entidade ativa
- ✅ Usar botão recarregar
- ✅ Tentar período diferente

### Menu não aparece (mobile)
- ✅ Procurar botão ☰
- ✅ Tocar para abrir
- ✅ Verificar orientação

## 📱 Responsividade

### Desktop (≥1366px)
- Menu lateral fixo
- Todos os controles visíveis
- Layout completo

### Mobile (<1366px)
- Menu lateral retrátil
- Controles adaptados
- Layout otimizado

## 🔐 Permissões

### Níveis de Acesso
| Tipo | Acesso | Ações |
|------|--------|-------|
| **Super Admin** | Todas as contas | Criar/Excluir qualquer dashboard |
| **Admin Conta** | Sua conta | Criar/Excluir dashboards da conta |
| **Usuário** | Sua conta | Visualizar dashboards |

### Permissões Necessárias
- `dashboard.list` → Visualizar
- `dashboard.create` → Criar
- `dashboard.delete` → Excluir

## 🎯 Melhores Práticas

### ✅ Faça
- Use nomes descritivos
- Organize por função
- Mantenha períodos relevantes
- Revise regularmente
- Remova dashboards obsoletos

### ❌ Evite
- Muitos widgets por aba
- Períodos muito longos
- Nomes genéricos
- Dashboards duplicados
- Sobrecarga visual

## 🆘 Contatos de Suporte

### Problemas Técnicos
- **Sistema de Tickets**: Através da plataforma
- **Suporte Técnico**: Documentação técnica
- **Administrador**: Contato interno

### Dúvidas de Uso
- **Guia Completo**: `DASHBOARD_USER_GUIDE.md`
- **Exemplos**: `DASHBOARD_EXAMPLES_AND_TUTORIALS.md`
- **Referência Técnica**: `DASHBOARD_TECHNICAL_REFERENCE.md`

## 🔗 Links Úteis

### Documentação
- **Guia do Usuário**: Instruções detalhadas
- **Referência Técnica**: Informações para desenvolvedores
- **Exemplos e Tutoriais**: Casos práticos

### Navegação
- **Rota**: `/dashboard`
- **Menu Principal**: Dashboard (ordem 1)
- **Breadcrumb**: Dashboard

## 📋 Checklist de Verificação

### Antes de Usar
- [ ] Login realizado
- [ ] Permissões verificadas
- [ ] Conta selecionada (se super admin)
- [ ] Entidades disponíveis

### Criando Dashboard
- [ ] Template selecionado
- [ ] Entidades escolhidas
- [ ] Opção substituir definida
- [ ] Confirmação de criação

### Analisando Dados
- [ ] Período adequado
- [ ] Widgets carregados
- [ ] Dados consistentes
- [ ] Interpretação correta

### Manutenção
- [ ] Dashboards relevantes
- [ ] Performance adequada
- [ ] Dados atualizados
- [ ] Limpeza periódica

## 🎨 Atalhos de Teclado

*Nota: Atalhos dependem do navegador e podem variar*

### Navegação
- **F5**: Recarregar página
- **Ctrl + F**: Buscar na página
- **Esc**: Fechar modais

### Zoom
- **Ctrl + +**: Aumentar zoom
- **Ctrl + -**: Diminuir zoom
- **Ctrl + 0**: Zoom padrão

## 📊 Códigos de Status

### Entidades
- 🟢 **Ativo**: Funcionando normalmente
- 🟡 **Atenção**: Requer monitoramento
- 🔴 **Inativo**: Fora de operação
- ⚫ **Desconectado**: Sem comunicação

### Widgets
- ✅ **Carregado**: Dados disponíveis
- ⏳ **Carregando**: Buscando dados
- ❌ **Erro**: Falha no carregamento
- 📭 **Sem Dados**: Período sem informações

---

## 💡 Dicas Finais

1. **Explore**: Teste diferentes combinações de widgets
2. **Personalize**: Crie dashboards específicos para suas necessidades
3. **Monitore**: Acompanhe regularmente os dados
4. **Otimize**: Ajuste períodos para melhor performance
5. **Compartilhe**: Use insights para tomada de decisões

---

*Este guia rápido oferece acesso imediato às principais funcionalidades. Para informações detalhadas, consulte a documentação completa.*
