// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Used for __tests__/testing-library.js
// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom/jest-globals'

import { cleanup } from '@testing-library/react'
import { memoryApp } from './src/@core/infra/memory'

beforeEach(() => {
  cleanup()

  memoryApp.init()
})

afterEach(() => {
  memoryApp.down()
})
