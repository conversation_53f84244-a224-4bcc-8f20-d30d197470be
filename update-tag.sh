#!/bin/bash

# Search, word that will be used to search and replaced tag  - ex: reembolso-front:     or reembolso-back:
# e.g: update-tag.sh "' + tag + '" "' + filename + '" business-user-api"';
# e.g: TAG :: dev-0.0.8
# e.g: ARQUIVO :: business-user-api/manifests/nonprod/backend-deployment-dev.yaml
# e.g: STRING :: business-user-api
# TAG : NOME DO ARQUIVO : A STRINT A SER ENCONTRADA ::  Essa string que está no arqivo  :: image: harbor-tanzu.doc88.com.br/powerview/business-user-api:dev-0.0.7

tag=$1
file=$2

search=$3

sed -i -e "s/\($3:\).*/\1$1/"  $2